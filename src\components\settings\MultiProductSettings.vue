<template>
  <div class="multi-product-settings" @click.stop>
    <!-- <div class="setting-section setting-card">
      <div class="icon-title-box flex-row-between">
        <div class="icon-title"> " > " 图标显示/隐藏 </div>
        <el-switch 
          v-model="showArrow"
          @change="handleArrowDisplayChange"
        />
      </div>
      
      <div class="button-click-event-setting">
        <ClickEventSettings 
          :content="currentProductButtonContent"
          @update:content="handleProductButtonClickEventUpdate"
          @insert-param="handleParamInsert"
          @manage-param="handleParamManage"
        />
      </div>
    </div> -->
    <!-- 主标题编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑主标题</h4>
      <RichParamInput
        v-model="localSettings.headerTitle"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 正文文本编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑正文</h4>
      <RichParamInput
        v-model="localSettings.headerSubtitle"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 商品图片设置 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑商品</h4>
      <div class="product-count-control">
        <!-- 减号按钮 -->
        <button 
          class="control-btn minus-btn" 
          :disabled="productCount <= 2"
          @click="decreaseProductCount"
        >
          −
        </button>
        
        <!-- 数字按钮组（指示器） -->
        <div class="number-buttons">
          <button
            v-for="index in productCount"
            :key="index"
            :class="['number-btn', { active: currentProductIndex === index - 1 }]"
            @click="selectProduct(index - 1)"
          >
            {{ index }}
          </button>
        </div>
        
        <!-- 加号按钮 -->
        <button 
          class="control-btn plus-btn" 
          :disabled="productCount >= 3"
          @click="increaseProductCount"
        >
          +
        </button>
      </div>

      <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">商品图片</h4>
      <div class="image-upload-area">
        <div class="image-preview" v-if="currentProduct.image">
          <img 
            :src="getMediaUrl(currentProduct.image)"
            :alt="currentProduct.imageAlt || `商品${currentProductIndex + 1}`"
            class="preview-image"
              @click="selectImageFile"
          />
        </div>
        <div v-else class="image-placeholder">
          <el-icon><Picture /></el-icon>
          <span>点击选择图片</span>
        </div>
        <!-- 当前图片描述 -->
        <div class="setting-desc">
          {{ currentProduct?.content}}
        </div>
        <el-button 
          class="select-btn"
          @click="selectImageFile"
        >
          选择图片
        </el-button>
      </div>
    </div>

    <!-- 编辑商品名 -->
      <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑商品名</h4>
      <RichParamInput
            v-model="currentProduct.title"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 编辑标签 -->
      <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑标签</h4>
      <RichParamInput
        v-model="currentProduct.tag"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 编辑标签 -->
      <!-- <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑标签2(选填)</h4>
      <RichParamInput
        v-model="currentProduct.tag"
        @input="handleSettingsChange"
      />
      </div> -->
    <!-- 价格显示开关 -->
      <div class="setting-section setting-card setting-card-price setting-pd10">
      <h4 class="is-show-price-title"> " ￥ " 图标显示/隐藏 </h4>
      <el-switch 
          :model-value="showCurrencySymbol"
          @update:model-value="handleCurrencyDisplayChange"
      />
    </div>
      <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑价格</h4>
      <RichParamInput
            v-model="currentProduct.price"
        @input="handleSettingsChange"
      />
    </div>
    <!-- 按钮设置 -->
      <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑按钮</h4>
      
      <h4 class="button-name-title">按钮名称：<span>（最多4位）</span></h4>
      <RichParamInput
        v-model="currentProduct.buttonText"
        @input="handleButtonTextChange"
      />
      
      <div class="button-click-event-setting">
        <ClickEventSettings 
        :content="currentProductButtonContentWithIndex"
        @update:content="handleProductButtonClickEventUpdate"
        @insert-param="handleParamInsert"
        @manage-param="handleParamManage"
        />
      </div>
    </div>
    </div>

    
  </div>

  <!-- 媒体选择弹框 -->
  <MediaSelectorDialog 
    v-model="dialogVisible"
    :mediaType="dialogMediaType"
    :filter-app-key="appKey"
    @confirm="handleMediaSelect"
  />
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import ClickEventSettings from './ClickEventSettings.vue';
import RichParamInput from '../richtext/RichParamInput.vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import { ClickEventTypeConverter, ActionJsonGenerator, CLICK_EVENT_TYPES } from '@/utils/clickEventManager.js';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  template: {
    type: Object,
    default: null
  },
  appKey: {
    type: String,
    required: true
  }
});

const emit = defineEmits([
  'update:content',
  'input',
  'insert-param',
  'manage-param',
  'paramInsert',
  'paramManage',
  'settings-change'
]);

// 响应式数据
const dialogVisible = ref(false);
const dialogMediaType = ref('image');
const currentProductIndex = ref(0);
const productCount = ref(2);
const showCurrencySymbols = ref([false, false, false]); // 每个商品独立的货币符号显示状态
const showArrow = ref(false); // 添加箭头显示控制

// 添加初始化标记
const isInitialized = ref(false);

// 获取默认点击事件配置 - 统一使用clickEventManager标准
const getDefaultClickEvent = () => {
  return {
    actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
    actionUrl: '',
    actionPath: '',
    packageName: '',
    floorType: '0',
    emailAddress: '',
    emailSubject: '',
    emailBody: '',
    scheduleStartTimeString: '',
    scheduleEndTimeString: '',
    popupTitle: '',
    popupContent: '',
    popupButtonText: '',
    copyType: '1',
    selectedParamId: '',
    fixedContent: ''
  };
};

// 本地设置数据
const localSettings = ref({
  // 主标题 - 对应 positionNumber: 1
  headerTitle: '',
  // 正文 - 对应 positionNumber: 2
  headerSubtitle: '',
  // 商品数据 - 最多3个商品
  products: [],
  // 价格货币符号显示控制：1显示￥，0不显示￥
  aimCurrencyDisplay: 1,
  // 箭头显示控制：0显示，1不显示
  aimArrowDisplay: 1
});

// 当前商品的货币符号显示状态
const showCurrencySymbol = computed({
  get: () => {
    const currentProduct = localSettings.value.products[currentProductIndex.value];
    // 使用 aimCurrencyDisplay 属性来控制货币符号显示，1显示，0不显示
    return currentProduct?.aimCurrencyDisplay === 1;
  },
  set: (value) => {
    const currentProduct = localSettings.value.products[currentProductIndex.value];
    if (currentProduct) {
      // 设置 aimCurrencyDisplay 属性，1显示￥，0不显示￥
      const displayValue = value ? 1 : 0;
      
      // 更新状态
      const updatedProducts = [...localSettings.value.products];
      updatedProducts[currentProductIndex.value] = {
        ...currentProduct,
        aimCurrencyDisplay: displayValue // 控制货币符号显示
      };
      
      localSettings.value = {
        ...localSettings.value,
        products: updatedProducts
      };
      
      // 立即触发更新
      nextTick(() => {
        handleSettingsChange();
      });
    }
  }
});

// 重置为默认值
const resetToDefaults = () => {
  console.log('MultiProductSettings - 重置为默认值');
  
  // 使用统一的默认点击事件配置
  const defaultProducts = Array.from({ length: 3 }, (_, index) => ({
    id: `product-${index + 1}`,
    image: '',
    content: '',
    title: '',
    price: '',
    tag: '',
    buttonText: '',
    contentText: '',
    imageClickEvent: getDefaultClickEvent(),
    buttonClickEvent: getDefaultClickEvent(),
    actionJson: { target: '' },
    aimCurrencyDisplay: 1, // 默认显示￥符号
    visible: index < 2, // 前2个商品可见
    hidden: index >= 2 // 第3个商品默认隐藏
  }));

  localSettings.value = {
    headerTitle: '',
    headerSubtitle: '',
    products: defaultProducts,
    productCount: 2,
    selectedProductIndex: 0,
    defaultData: {
      image: '',
      content: '',
      title: '',
      price: '',
      tag: '',
      buttonText: '',
      contentText: '',
      imageClickEvent: getDefaultClickEvent(),
      buttonClickEvent: getDefaultClickEvent(),
      actionJson: { target: '' },
      aimCurrencyDisplay: 1 // 默认显示￥符号
    }
  };
  
  productCount.value = 2;
  currentProductIndex.value = 0;
  
  console.log('MultiProductSettings - 重置完成，默认设置:', localSettings.value);
  console.log('MultiProductSettings - 商品隐藏状态:', defaultProducts.map((p, i) => `商品${i+1}: hidden=${p.hidden}`));
};

// 初始化设置
const initializeSettings = (data) => {
  console.log('MultiProductSettings 初始化设置:', data);
  
  // 检查数据是否为空
  if (!data || Object.keys(data).length === 0) {
    console.warn('数据为空，重置为默认值');
    resetToDefaults();
    return;
  }

  // 先确保products数组存在并初始化
  if (!localSettings.value.products || !Array.isArray(localSettings.value.products)) {
    console.log('初始化products数组');
    const defaultProducts = Array.from({ length: 3 }, (_, index) => ({
      id: `product-${index + 1}`,
      image: '',
      content: '',
      title: '',
      price: '',
      tag: '',
      buttonText: '',
      contentText: '',
      imageClickEvent: getDefaultClickEvent(),
      buttonClickEvent: getDefaultClickEvent(),
      actionJson: { target: '' },
      aimCurrencyDisplay: 1, // 默认显示货币符号
      visible: index < 2,
      hidden: index >= 2
    }));

    if (!localSettings.value) {
      localSettings.value = {};
    }
    localSettings.value.products = defaultProducts;
    localSettings.value.productCount = 2;
    localSettings.value.selectedProductIndex = 0;
  }

  // 设置标题信息
  localSettings.value.headerTitle = data.headerTitle || '';
  localSettings.value.headerSubtitle = data.headerSubtitle || '';
  
  // 确定实际商品数量
  let actualProductCount = 2;
  if (data.productCount !== undefined && data.productCount > 0) {
    actualProductCount = Math.min(data.productCount, 3);
  }
  
  productCount.value = actualProductCount;

  // 处理商品数据
  if (data.products && Array.isArray(data.products)) {
    for (let i = 0; i < Math.min(3, data.products.length); i++) {
      if (data.products[i]) {
        const product = data.products[i];
        
        if (!localSettings.value.products[i]) {
          localSettings.value.products[i] = {
            id: `product-${i + 1}`,
            image: '',
            content: '',
            title: '',
            price: '',
            tag: '',
            buttonText: '',
            contentText: '',
            imageClickEvent: getDefaultClickEvent(),
            buttonClickEvent: getDefaultClickEvent(),
            actionJson: { target: '' },
            aimCurrencyDisplay: 1, // 默认显示货币符号
            visible: i < 2,
            hidden: i >= 2
          };
        }
        
        // 设置商品基础信息
        localSettings.value.products[i].image = product.image || '';
        localSettings.value.products[i].content = product.content; // 只赋值，不加 || ''
        console.log('MultiProductSettings - 初始化商品content:', product.content);
        console.log(`MultiProductSettings - 商品${i + 1}完整数据:`, product); // 添加完整数据调试
        localSettings.value.products[i].title = product.title || '';
        localSettings.value.products[i].tag = product.tag || '';
        localSettings.value.products[i].price = product.price || '';
        localSettings.value.products[i].buttonText = product.buttonText || '';
        // 使用 visible 属性控制货币符号显示
        localSettings.value.products[i].aimCurrencyDisplay = product.aimCurrencyDisplay;
        
        const shouldBeVisible = i < actualProductCount;
        localSettings.value.products[i].hidden = !shouldBeVisible;
        localSettings.value.products[i].visible = shouldBeVisible;
        
        // 处理按钮点击事件 - 统一使用clickEventManager标准
        if (product.buttonClickEvent) {
          // 兼容旧数据格式
          if (product.buttonClickEvent.type && !product.buttonClickEvent.actionType) {
            const actionType = ClickEventTypeConverter.toActionType(product.buttonClickEvent.type);
            const actionJson = ActionJsonGenerator.fromClickEvent(product.buttonClickEvent);
            localSettings.value.products[i].buttonClickEvent = {
              // 保持旧格式字段
              type: product.buttonClickEvent.type,
              url: product.buttonClickEvent.url || actionJson.target || '',
              // 添加新格式字段（校验函数需要）
              actionType,
              actionUrl: actionJson.target || '',
              actionPath: '',
              ...actionJson
            };
          } else {
            // 已经是新格式或混合格式
            localSettings.value.products[i].buttonClickEvent = {
              ...getDefaultClickEvent(),  // 使用默认值
              ...(product.buttonClickEvent || {}),  // 覆盖用户设置
              // 确保同时有新旧格式字段
              //type: product.buttonClickEvent.type || ClickEventTypeConverter.toClickEventType(product.buttonClickEvent.actionType),
              //url: product.buttonClickEvent.url || product.buttonClickEvent.actionUrl || '',
              //actionType: product.buttonClickEvent.actionType || ClickEventTypeConverter.toActionType(product.buttonClickEvent.type),
              //actionUrl: product.buttonClickEvent.actionUrl || product.buttonClickEvent.url || ''
            };
          }
        }
        
        if (product.actionJson) {
          localSettings.value.products[i].actionJson = product.actionJson;
        }
      }
    }
    
    // 确保超出productCount的商品都是隐藏的
    for (let i = actualProductCount; i < 3; i++) {
      if (localSettings.value.products[i]) {
        localSettings.value.products[i].hidden = true;
        localSettings.value.products[i].visible = false;
      }
    }
  }

  // 设置选中的商品索引
  localSettings.value.selectedProductIndex = data.selectedProductIndex || 0;
  currentProductIndex.value = data.selectedProductIndex || 0;
  console.log('MultiProductSettings - localSettings.value.products:', localSettings.value.products);
  console.log('初始化完成，当前设置:', localSettings.value);
};

// 监听内容变化
watch(() => props.content, (newVal, oldVal) => {
  console.log('Props内容变化:', newVal);
  
  // 避免不必要的重新初始化
  if (!newVal) {
    return;
  }

  // 如果有currentData，说明是从TemplateEditor传递过来的多商品数据
  if (newVal.isMultiProductSettings && newVal.currentData) {
    // 检查是否需要更新选中的商品（优先处理，确保校验失败时能正确切换商品）
    if (newVal.currentData.selectedProductIndex !== undefined && 
        newVal.currentData.selectedProductIndex !== currentProductIndex.value) {
      console.log('检测到selectedProductIndex变化，切换商品:', newVal.currentData.selectedProductIndex);
      selectProduct(newVal.currentData.selectedProductIndex);
    }
    
    // 首次初始化时，如果已经有设置状态且productCount不为默认值，则保持现有状态
    if (!isInitialized.value || 
        !localSettings.value || 
        !localSettings.value.products || 
        localSettings.value.products.length === 0) {
      console.log('需要初始化设置');
      initializeSettings(newVal.currentData);
      isInitialized.value = true;
    } else {
      console.log('已初始化，跳过重复初始化，保持当前productCount:', productCount.value);
    }
    return;
  }

  // 检查是否需要更新选中的商品（这个作为后备，处理其他情况）
  if (newVal.currentData && newVal.currentData.selectedProductIndex !== currentProductIndex.value) {
    console.log('预览区域切换商品:', newVal.currentData.selectedProductIndex);
    selectProduct(newVal.currentData.selectedProductIndex);
    return;
  }

  // 只在关键数据发生变化时才重新初始化
  if (oldVal && newVal.currentData && oldVal.currentData) {
  const shouldUpdate = 
    newVal.currentData.headerTitle !== oldVal.currentData.headerTitle ||
    newVal.currentData.headerSubtitle !== oldVal.currentData.headerSubtitle ||
    (newVal.currentData.productCount !== oldVal.currentData.productCount && isInitialized.value) ||
    JSON.stringify(newVal.currentData.products) !== JSON.stringify(oldVal.currentData.products);

  if (shouldUpdate && newVal.isMultiProductSettings) {
    console.log('检测到关键数据变化，需要更新设置');
    initializeSettings(newVal.currentData);
    }
  }
}, { 
  immediate: true, 
  deep: true 
});

// 当前选中的商品
const currentProduct = computed(() => {
  try {
    // 如果localSettings或products不存在，触发重置
    if (!localSettings.value || !localSettings.value.products || !Array.isArray(localSettings.value.products)) {
      console.warn('localSettings.value.products 不存在，触发重置');
      resetToDefaults();
      return localSettings.value.products[0];
    }
    
    // 确保索引在有效范围内
    const index = Math.max(0, Math.min(currentProductIndex.value || 0, localSettings.value.products.length - 1));
    const product = localSettings.value.products[index];
    
    if (!product) {
      console.warn(`商品索引${index}不存在，使用第一个商品`);
      return localSettings.value.products[0];
    }
    
    console.log(`当前商品 (索引${index}):`, product);
    console.log(`当前商品图片描述 (索引${index}):`, product.content); // 添加调试日志
    console.log(`当前商品完整数据 (索引${index}):`, JSON.stringify(product, null, 2)); // 添加完整数据调试
    return product;
  } catch (error) {
    console.error('MultiProductSettings - currentProduct 计算错误:', error);
    resetToDefaults();
    return localSettings.value.products[0];
  }
});

// 在模板中显示商品的计算属性
const visibleProducts = computed(() => {
  if (!localSettings.value.products || !Array.isArray(localSettings.value.products)) {
    console.log('localSettings.value.products 不存在或不是数组');
    return [];
  }
  
  const visible = localSettings.value.products.filter((product, index) => {
    // 商品可见的条件：索引小于商品数量 且 不是隐藏状态
    const isWithinCount = index < productCount.value;
    const isNotHidden = !product.hidden;
    const isVisible = isWithinCount && isNotHidden;
    
    console.log(`商品${index + 1}可见性检查:`, {
      index,
      productCount: productCount.value,
      isWithinCount,
      isNotHidden,
      productHidden: product.hidden,
      finalVisible: isVisible
    });
    
    return isVisible;
  });
  
  console.log('可见商品数量:', visible.length, '总商品数:', localSettings.value.products.length, '设置的商品数量:', productCount.value);
  return visible;
});

// 当前商品按钮内容（用于传递给ClickEventSettings）- 统一使用clickEventManager标准
const currentProductButtonContent = computed(() => {
  try {
    console.log('MultiProductSettings - 计算currentProductButtonContent');
    console.log('当前商品索引:', currentProductIndex.value);
    
    if (!localSettings.value.products[currentProductIndex.value]) {
      console.log('商品不存在，返回默认值');
      return {
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionUrl: '',
        actionPath: ''
      };
    }
    
    const currentProduct = localSettings.value.products[currentProductIndex.value];
    console.log('当前商品数据:', currentProduct);
    
    const clickEvent = currentProduct.buttonClickEvent;
    console.log('当前商品按钮点击事件:', clickEvent);
    
    // 统一使用clickEventManager的标准处理
    if (!clickEvent || (!clickEvent.actionType && !clickEvent.type)) {
      console.log('无有效点击事件，返回默认值');
      return {
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionUrl: '',
        actionPath: ''
      };
    }
    
    // 兼容旧数据格式
    let actionType = clickEvent.actionType;
    if (!actionType && clickEvent.type) {
      actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
    }
    
    // 使用统一的转换方法
    const buttonContent = ActionJsonGenerator.toClickEventSettings(clickEvent);
    
    console.log('MultiProductSettings - currentProductButtonContent 计算结果:', buttonContent);
    return buttonContent;
    
  } catch (error) {
    console.error('MultiProductSettings - currentProductButtonContent 计算错误:', error);
    return {
      actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
      actionUrl: '',
      actionPath: ''
    };
  }
});

const currentProductButtonContentWithIndex = computed(() => {
  const baseContent = currentProductButtonContent.value;
  return {
    ...baseContent,
    // 添加商品索引到contentId中，确保每个商品有独立的缓存
    contentId: `multi-product-button-event-${currentProductIndex.value}`,
    type: 'multi-product-button',
    productIndex: currentProductIndex.value
  };
});

// 处理设置变化
const handleSettingsChange = () => {
  console.log('MultiProductSettings - 处理设置变化，当前设置:', localSettings.value);
  
  // 构建所有商品数据，基于productCount来确定hidden状态
  const allProducts = localSettings.value.products.map((product, index) => {
    // 统一使用clickEventManager生成actionJson
    let actionJson = product.actionJson;
    if (!actionJson && product.buttonClickEvent) {
      if (product.buttonClickEvent.actionType) {
        // 新格式
        actionJson = ActionJsonGenerator.generate(
          product.buttonClickEvent.actionType,
          product.buttonClickEvent.actionUrl || '',
          product.buttonClickEvent.actionPath || '',
          product.buttonClickEvent
        );
      } else if (product.buttonClickEvent.type) {
        // 兼容旧格式
        actionJson = ActionJsonGenerator.fromClickEvent(product.buttonClickEvent);
      }
    }
    
    // 根据productCount确定是否隐藏
    const isHidden = index >= productCount.value;
    
    console.log(`处理商品${index + 1}数据:`, {
      index,
      productCount: productCount.value,
      isHidden,
      product
    });
    
    return {
      id: product.id,
      image: product.image,
      title: product.title,
      tag: product.tag,
      price: product.price,
      buttonText: product.buttonText,
      imageClickEvent: product.imageClickEvent || getDefaultClickEvent(),
      buttonClickEvent: product.buttonClickEvent || getDefaultClickEvent(),
      actionJson: actionJson || { target: '' },
      aimCurrencyDisplay: product.aimCurrencyDisplay,
      hidden: isHidden,
      visible: isHidden ? 1 : 0,
      content: product.content // 添加 content 字段
    };
  });
  
  // 统计可见商品数量
  const visibleProducts = allProducts.filter(product => !product.hidden);
  
  console.log('MultiProductSettings - 处理商品数据，可见商品数量:', visibleProducts.length, '总商品数量:', allProducts.length, '设置商品数量:', productCount.value);
  console.log('构建的所有商品数据:', allProducts);
  console.log('MultiProductSettings - handleSettingsChange allProducts:', allProducts);

  const currentData = {
    headerTitle: localSettings.value.headerTitle,
    headerSubtitle: localSettings.value.headerSubtitle,
    products: allProducts,
    selectedProductIndex: currentProductIndex.value,
    productCount: productCount.value,
    defaultData: localSettings.value.defaultData
  };

  console.log('可见商品数量:', visibleProducts.length, '总商品数量:', allProducts.length);

  // 更新模板内容
  const updatedContent = {
    type: 'multi-product-settings',
    contentId: 'multi-product-settings',
    isMultiProductSettings: true,
    currentData
  };
  console.log('handleMultiProductSettingsChange - emit前 products:', updatedContent.currentData.products);
  console.log('发送更新数据:', updatedContent);
  emit('update:content', updatedContent);
  emit('settings-change', updatedContent);
};

// 选择商品
const selectProduct = (index) => {
  if (index === currentProductIndex.value) return;

  console.log('切换到商品:', index);

  // 设置保护标记，防止ClickEventSettings在切换商品时清空字段
  window._isUpdatingContent = true;
  window._contentChanging = true;

  currentProductIndex.value = index;
  localSettings.value.selectedProductIndex = index;

  // 延迟清除保护标记，确保ClickEventSettings完成数据加载
  setTimeout(() => {
    window._isUpdatingContent = false;
    window._contentChanging = false;
  }, 100);

  handleSettingsChange();
};

// 商品操作方法
const increaseProductCount = () => {
  if (productCount.value < 3) {
    productCount.value++;
    // 显示新增的商品 - 取消隐藏状态
    if (localSettings.value.products[productCount.value - 1]) {
      localSettings.value.products[productCount.value - 1].hidden = false;
      localSettings.value.products[productCount.value - 1].visible = true;
    }
    // 自动选中新增的商品
    selectProduct(productCount.value - 1);
    
    console.log('增加商品数量到:', productCount.value);
    console.log('当前选中商品索引:', currentProductIndex.value);
    console.log('商品隐藏状态:', localSettings.value.products.map((p, i) => `商品${i+1}: hidden=${p.hidden}`));
    
    // 立即触发更新
    nextTick(() => {
      handleSettingsChange();
    });
  }
};

const decreaseProductCount = () => {
  if (productCount.value > 2) {
    // 如果当前选中的是要隐藏的商品，先切换到前一个商品
    if (currentProductIndex.value >= productCount.value - 1) {
      selectProduct(productCount.value - 2);
    }
    
    // 隐藏最后一个商品
    if (localSettings.value.products[productCount.value - 1]) {
      localSettings.value.products[productCount.value - 1].hidden = true;
      localSettings.value.products[productCount.value - 1].visible = false;
    }
    
    productCount.value--;
    
    console.log('减少商品数量到:', productCount.value);
    console.log('当前选中商品索引:', currentProductIndex.value);
    console.log('商品隐藏状态:', localSettings.value.products.map((p, i) => `商品${i+1}: hidden=${p.hidden}`));
    
    // 立即触发更新
    nextTick(() => {
      handleSettingsChange();
    });
  }
};

// 媒体选择方法
const selectImageFile = () => {
  dialogMediaType.value = 'image';
  dialogVisible.value = true;
};

const handleMediaSelect = (mediaData) => {
  console.log('MultiProductSettings - 选择媒体文件:', mediaData);
  
  if (dialogMediaType.value === 'image') {
    const mediaUrl = mediaData.mediaUrl || getMediaUrl(`/aim_files/${mediaData.appKey}/${mediaData.path}`);
    const relativePath = `/aim_files/${mediaData.appKey}/${mediaData.path}`;
    localSettings.value.products[currentProductIndex.value].image = relativePath;
    localSettings.value.products[currentProductIndex.value].imageAlt = mediaData.mediaDesc || `商品${currentProductIndex.value + 1}`;
  }
  
  handleSettingsChange();
  dialogVisible.value = false;
  ElMessage.success('媒体文件设置成功');
};

// 处理商品按钮点击事件更新 - 统一使用clickEventManager标准
const handleProductButtonClickEventUpdate = (eventContent) => {
  console.log('MultiProductSettings - 商品按钮点击事件更新:', eventContent);
  console.log('MultiProductSettings - 当前商品索引:', currentProductIndex.value);

  // 使用统一的转换方法生成 clickEvent
  const clickEvent = ActionJsonGenerator.fromClickEventSettings(eventContent);
  console.log('MultiProductSettings - 转换后的clickEvent:', clickEvent);

  // 为了兼容校验函数，同时保存新旧格式的字段
  const enhancedClickEvent = {
    ...clickEvent,
    // 新格式字段（校验函数需要）
    actionType: eventContent.actionType,
    actionUrl: eventContent.actionUrl || '',
    actionPath: eventContent.actionPath || '',
    // 确保所有可能的字段都被保存
    emailAddress: eventContent.emailAddress || '',
    emailSubject: eventContent.emailSubject || '',
    emailBody: eventContent.emailBody || '',
    scheduleTitle: eventContent.scheduleTitle || '',
    scheduleContent: eventContent.scheduleContent || '',
    scheduleStartTimeString: eventContent.scheduleStartTimeString || '',
    scheduleEndTimeString: eventContent.scheduleEndTimeString || '',
    popupTitle: eventContent.popupTitle || '',
    popupContent: eventContent.popupContent || '',
    popupButtonText: eventContent.popupButtonText || '',
    packageName: eventContent.packageName || [],
    floorType: eventContent.floorType || '0',
    copyType: eventContent.copyType || '1',
    selectedParamId: eventContent.selectedParamId || '',
    fixedContent: eventContent.fixedContent || ''
  };

  // 生成正确的actionJson格式
  const actionJson = ActionJsonGenerator.generate(
    eventContent.actionType,
    eventContent.actionUrl,
    eventContent.actionPath,
    eventContent
  );

  console.log('MultiProductSettings - 生成的clickEvent:', enhancedClickEvent);
  console.log('MultiProductSettings - 生成的actionJson:', actionJson);

  // 更新当前选中商品的按钮点击事件
  const updatedProducts = [...localSettings.value.products];
  updatedProducts[currentProductIndex.value] = {
    ...updatedProducts[currentProductIndex.value],
    buttonClickEvent: enhancedClickEvent,
    actionJson: actionJson
  };
  
  localSettings.value = {
    ...localSettings.value,
    products: updatedProducts
  };
  
  // 立即触发更新
  nextTick(() => {
    handleSettingsChange();
  });
};

const handleButtonTextChange = () => {
  console.log('MultiProductSettings - 按钮文本变化:', currentProduct.value.buttonText);
  handleSettingsChange();
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  emit('insert-param', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('manage-param');
};

// 处理货币符号显示变化
const handleCurrencyDisplayChange = (value) => {
  showCurrencySymbol.value = value;
};

// 处理箭头显示变化
const handleArrowDisplayChange = () => {
  localSettings.value.aimArrowDisplay = showArrow.value ? 0 : 1;
  handleSettingsChange();
};

// 组件挂载时初始化
onMounted(() => {
  console.log('MultiProductSettings - props.content:', props.content);
  console.log('MultiProductSettings - 组件挂载，props.content.currentData.products:', props.content?.currentData?.products);
  // 只有在未初始化时才进行初始化
  if (!isInitialized.value && props.content && props.content.currentData) {
    initializeSettings(props.content.currentData);
    isInitialized.value = true;
  } else if (!isInitialized.value) {
    resetToDefaults();
    isInitialized.value = true;
  }
});

watch(currentProduct, (val) => {
  console.log('设置面板 currentProduct:', val);
}, { immediate: true, deep: true });
</script>

<style scoped lang="scss">
.multi-product-settings {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  
  :deep(.setting-content) {
    padding: 0;
  }
  
  :deep(.rich-param-input) {
    padding: 14px 16px;
  }
}
.icon-title-box{
  width: 100%;
  padding: 16px 16px 10px;
}
.setting-section {
  margin-bottom: 24px;
  .setting-pd10{
    margin: 10px 16px;
  }
  .section-title {
    color: #303133;
    font-weight: normal;
    padding: 10px 16px;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
    position: relative;
  }
}

.setting-card {
  border: 1px solid #ebeef5;
  background-color: #fff;
  transition: .3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 100%;
}

.setting-card-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
}

.is-show-price-title {
  color: #303133;
  font-weight: normal;
}

/* 商品数量控制器样式 */
.product-count-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  border-color: #409eff;
  color: #409eff;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.number-buttons {
  display: flex;
  gap: 4px;
}

.number-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.number-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.number-btn.active {
  background-color: #409eff;
  border-color: #409eff;
  color: #ffffff;
}

/* 图片设置样式 */
.image-upload-area {
  width: 100%;
  padding: 16px;
}

.image-preview {
  margin-bottom: 12px;
}

.preview-image {
  width: 100%;
  height: 210px;
  // object-fit: cover;
  border-radius: 4px;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  width: 100%;
  height: 210px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  margin-bottom: 12px;
}

.image-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.image-placeholder span {
  font-size: 14px;
}

.setting-desc{
  margin-bottom: 10px;
}
.select-btn {
  width: auto;
}

/* 按钮设置样式 */
.button-name-title {
  color: #303133;
  font-weight: normal;
  padding: 10px 16px 0;
  width: 100%;
  
  span {
    color: #999;
  }
}

.button-click-event-setting {
  width: 100%;
  padding: 0px 16px;
}

.setting-label {
  display: block;
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

/* Element Plus 样式覆盖 */
:deep(.el-input__count) {
  font-size: 12px;
}

:deep(.el-textarea__inner) {
  resize: none;
}
</style> 