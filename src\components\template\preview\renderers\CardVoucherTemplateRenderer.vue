<template>
  <div class="card-voucher-template" :class="{ 'card-mode': usage === 'list' }">
    <!-- 券的外层容器 -->
    <div class="voucher-container" @click="handleVoucherClick">
      <!-- 背景图片 -->
      <div 
        v-if="backgroundContent && backgroundContent.src" 
        class="voucher-background"
        :style="{ backgroundImage: `url(${getMediaUrl(backgroundContent.src)})` }"
      ></div>
      
      <!-- 券的顶部金额区域 -->
      <div class="voucher-header">
        <!-- 左侧金额 -->
        <div class="voucher-amount-section">
          <span class="currency-symbol" v-if="showCurrencySymbol">¥</span>
          <span class="voucher-amount text-content">{{ displayContent.amount }}</span>
        </div>
        
        <!-- 右侧使用条件 -->
        <div class="voucher-condition-section">
          <div class="condition-tag">{{ displayContent.conditionTag }}</div>
          <div class="condition-text text-content">{{ displayContent.condition }}</div>
        </div>
      </div>

      <!-- 券的主体内容区域 -->
      <div class="voucher-body">
        <!-- 主要描述文本 -->
        <div class="voucher-description text-content">{{ displayContent.description }}</div>
        
        <!-- 有效期 -->
        <div class="voucher-validity text-content">{{ displayContent.validity }}</div>
      </div>
        <!-- 圆形图标 -->
        <div class="voucher-icon-container">
          <img 
            :src="getMediaUrl(displayContent.iconSrc)"
            class="voucher-icon image-element preview-image"
            alt="券图标"
          />
        </div>
      <!-- 券的底部区域 -->
      <div class="voucher-footer">
        {{ displayContent.button }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
})

const emit = defineEmits(['select-content', 'update:content'])

// 获取背景内容
const backgroundContent = computed(() => {
  const bgContent = props.contents.find(content => 
    content.type === 'background' || (content.type === 'image' && content.isBackground === 1)
  )
  console.log('CardVoucherRenderer - 所有内容:', props.contents)
  console.log('CardVoucherRenderer - 背景内容:', bgContent)
  return bgContent
})

// 根据positionNumber和isTextTitle获取不同类型的内容
const amountContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'text' && content.positionNumber === 1
  )
})

const conditionTagContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'text' && content.positionNumber === 2
  )
})

const conditionContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'text' && content.positionNumber === 3
  )
})

const descriptionContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'text' && content.positionNumber === 4
  )
})

const validityContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'text' && content.positionNumber === 5
  )
})

const iconContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'image' && content.positionNumber === 6
  )
})

const buttonContent = computed(() => {
  return props.contents.find(content => 
    content.type === 'button' && content.positionNumber === 7
  ) || { content: '立即领取' }
})

// 添加一个用于处理空值的计算属性
const defaultValues = {
  amount: '100',
  conditionTag: '满减券',
  condition: '满1000可用',
  description: '编辑文本，最多40个中文字',
  validity: '2022-01-01~2022-12-31',
  iconSrc: '/aim_files/aim_defult/defaultImg2.jpg'
}

// 处理展示内容的计算属性
const displayContent = computed(() => ({
  amount: amountContent.value?.content || defaultValues.amount,
  conditionTag: conditionTagContent.value?.content || defaultValues.conditionTag,
  condition: conditionContent.value?.content || defaultValues.condition,
  description: descriptionContent.value?.content || defaultValues.description,
  validity: validityContent.value?.content || defaultValues.validity,
  iconSrc: iconContent.value?.src || defaultValues.iconSrc,
  button: buttonContent.value?.content || '立即领取'
}))

// 添加调试日志
console.log('内容匹配结果：', {
  amount: amountContent.value,
  conditionTag: conditionTagContent.value,
  condition: conditionContent.value,
  description: descriptionContent.value,
  validity: validityContent.value,
  icon: iconContent.value,
  button: buttonContent.value
})

// 添加货币符号显示状态的计算属性
const showCurrencySymbol = computed(() => {
  // 如果内容数组为空，默认显示
  if (!props.contents || props.contents.length === 0) {
    return true;
  }
  
  // 获取第一个内容的 aimCurrencyDisplay 值
  const firstContent = props.contents[0];
  return firstContent.aimCurrencyDisplay !== 0;
});

// 处理券点击 - 触发显示单卡券设置面板
const handleVoucherClick = (event) => {
  event.stopPropagation()
  event.preventDefault()
  console.log('CardVoucherRenderer - 点击券卡，触发设置面板')
  
  // 添加标记防止重复处理
  if (event.target && event.target.closest) {
    const clickedElement = event.target.closest('.voucher-container')
    if (clickedElement) {
      clickedElement.setAttribute('data-click-processed', 'true')
      setTimeout(() => {
        clickedElement.removeAttribute('data-click-processed')
      }, 200)
    }
  }
  
  // 发送一个特殊的事件来触发设置面板显示
  emit('select-content', {
    contentId: 'card-voucher-settings',
    type: 'card-voucher-settings',
    isCardVoucherSettings: true
  })
}
</script>

<style scoped lang="scss">
.card-voucher-template {
  width: 100%;
  background: #fff;
}

.voucher-container {
  width: 100%;
  height: 364px;
  margin: 0 auto;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
}

.voucher-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: 100%;
  background-position: top center;
  background-repeat: no-repeat;
  z-index: 0;
}

.voucher-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 50px 20px 0;
  position: relative;
  z-index: 2;
  color: #fa282b;
  height: 60px;
}

.voucher-amount-section {
  display: flex;
  align-items: baseline;
  
  .currency-symbol {
    font-size: 24px;
    font-weight: bold;
    margin-right: 4px;
  }
  
  .voucher-amount {
    font-size: 46px;
    margin-right: 10px;
    font-weight: 700;
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.voucher-condition-section {
  .condition-tag {
    border: 1px solid #fa282b;
    padding: 1px 3px;
    border-radius: 4px;
    height: 20px;
    max-width: 80px;
    box-sizing: border-box;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 12px;
    margin-bottom: 2px;
    display: inline-block;
  }
  
  .condition-text {
    font-size: 17px;
    max-width: 136px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.voucher-body {
  padding: 0 20px;
  text-align: center;
  position: relative;
  z-index: 2;
  
  .voucher-description {
    text-align: center;
    color: #a62929;
    font-size: 13px;
    font-weight: 700;
    max-height: 34px;
    margin: 0 auto 4px;
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  .voucher-validity {
    text-align: center;
    color: #a62929;
    font-size: 12px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: 80%;
    margin: 0 auto;
  }
}

.voucher-icon-container {
  width: 70px;
  height: 70px;
  position: absolute;
  overflow: hidden;
  left: 50%;
  margin-left: -35px;
  top: 180px;
  bottom: 10px;
  right: 20px;
  border-radius: 70px;
  .voucher-icon {
    width: 100%;
    height: 100%;
  }
}
.voucher-footer {
  width: 290px;
  height: 40px;
  background-color: #ffbf00;
  border-radius: 20px;
  text-align: center;
  color: #fff;
  line-height: 40px;
  font-size: 16px;
  position: absolute;
  bottom: 35px;
  left: 26px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 