<template>
  <div 
    class="longtext-template"
    :class="[
      `longtext-style-${selectedStyle}`,
      { 'is-editing': isEditing, 'is-selected': isSelected, 'is-card-mode': isCardMode }
    ]"
    @click="handleTemplateClick"
  >
    <!-- 标题部分 -->
    <div class="title-section" v-if="titleContent">
      <TextElement
        :content="titleContent"
        :is-selected="titleContent === selectedContent"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>

    <!-- 描述部分 -->
    <div class="description-section" v-if="descriptionContent">
      <TextElement
        :content="descriptionContent"
        :is-selected="descriptionContent === selectedContent"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>

    <!-- 按钮部分 -->
    <div class="button-section" :class="{ 'general-style-buttons': selectedStyle === 'general' }">
      <div 
        v-if="buttonContent"
        class="button-wrapper"
        :class="{ 
          'left-button': selectedStyle === 'general',
          'single-button': selectedStyle === 'simple' 
        }"
      >
        <ButtonElement
          :content="buttonContent"
          :is-selected="selectedContent && selectedContent.contentId === buttonContent.contentId"
          :editable="editable"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
        />
      </div>
      
      <div 
        v-if="selectedStyle === 'general' && secondaryButtonContent"
        class="button-wrapper right-button"
      >
        <ButtonElement
          :content="secondaryButtonContent"
          :is-selected="selectedContent && selectedContent.contentId === secondaryButtonContent.contentId"
          :editable="editable"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
        />
      </div>
    </div>

    <!-- 图片部分 -->
    <div class="image-section" v-if="imageContent">
      <ImageElement
        :content="imageContent"
        :is-selected="imageContent === selectedContent"
        :get-media-url="getMediaUrl"
        @select="handleContentSelect"
        @update:content="handleContentUpdate"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, watch, inject, onMounted, nextTick } from 'vue';
import TextElement from '../elements/TextElement.vue';
import ImageElement from '../elements/ImageElement.vue';
import ButtonElement from '../elements/ButtonElement.vue';
import { CLICK_EVENT_TYPES } from '@/utils/clickEventManager';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  isCardMode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 注入长文本设置
const longTextSettings = inject('longTextSettings', null);

// 获取选中的样式
const selectedStyle = computed(() => {
  // 在首页列表模式下，根据实际按钮数量自动判断样式
  if (props.usage === 'list') {
    const buttons = props.contents?.filter(content => content.type === 'button') || [];
    // 如果有两个或以上按钮，显示为一般样式
    if (buttons.length >= 2) {
      return 'general';
    }
    // 否则显示为简单样式
    return 'simple';
  }
  
  // 在编辑器模式下，使用设置中的样式
  return longTextSettings?.selectedStyle || 'simple';
});

// 标题内容
const titleContent = computed(() => {
  return props.contents?.find(content => 
    content.type === 'text' && 
    content.isTextTitle === 1
  ) || null;
});

// 描述内容
const descriptionContent = computed(() => {
  return props.contents?.find(content => 
    content.type === 'text' && 
    content.isTextTitle === 0
  ) || null;
});

// 主按钮内容
const buttonContent = computed(() => {
  const buttons = props.contents?.filter(content => content.type === 'button') || [];
  if (buttons.length > 0) {
    // 确保按钮有独立的 contentId
    const button = buttons[0];
    if (!button.contentId || button.contentId === 'undefined') {
      // 生成简单的唯一ID
      button.contentId = `btn1-${Date.now()}`;
      console.log('生成第一个按钮的contentId:', button.contentId);
    }
    return button;
  }
  return null;
});

// 副按钮内容 - 一般样式时显示
const secondaryButtonContent = computed(() => {
  // 只有在一般样式下才显示第二个按钮
  if (selectedStyle.value !== 'general') {
    return null;
  }
  
  const buttons = props.contents?.filter(content => content.type === 'button') || [];
  if (buttons.length > 1) {
    // 确保第二个按钮有独立的 contentId
    const button = buttons[1];
    if (!button.contentId || button.contentId === 'undefined') {
      // 生成简单的唯一ID，确保与第一个按钮不同
      button.contentId = `btn2-${Date.now()}`;
      console.log('生成第二个按钮的contentId:', button.contentId);
    }
    return button;
  }
  return null;
});

// 创建默认按钮
const createDefaultButton = (index) => {
  return {
    contentId: `btn${index}-${Date.now()}`, // 简化contentId生成
    type: 'button',
    content: index === 1 ? '编辑按钮1' : '编辑按钮', // 根据索引设置默认内容
    pageId: 1,
    positionNumber: index === 1 ? 3 : 5, // 第一个按钮是3，第二个按钮是5
    isTextTitle: 0,
    pageLayout: 'center',
    pageLines: index === 1 ? 3 : 5,
    actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
    actionUrl: '',
    actionJson: { target: '' }
  };
};

// 图片内容
const imageContent = computed(() => {
  return props.contents?.find(content => 
    content.type === 'image'
  ) || null;
});

// 监听样式变化
watch(selectedStyle, (newStyle, oldStyle) => {
  console.log('LongTextTemplateRenderer - 样式变化:', oldStyle, '->', newStyle);
  
  // 当切换到一般样式时，确保有两个按钮
  if (newStyle === 'general') {
    console.log('LongTextTemplateRenderer - 切换到一般样式，检查按钮数量');
    const currentButtons = props.contents?.filter(content => content.type === 'button') || [];
    
    if (currentButtons.length < 2) {
      console.log('LongTextTemplateRenderer - 按钮数量不足，需要添加第二个按钮');
      // 触发添加第二个按钮的事件
      nextTick(() => {
        // 通知父组件需要添加第二个按钮
        emit('update:content', {
          type: 'add-button',
          buttonData: createDefaultButton(2)
        });
      });
    }
  }
}, { immediate: true });

// 处理内容选择
const handleContentSelect = (content) => {
  console.log('LongTextTemplateRenderer - 选择内容:', content);
  
  // 如果是按钮，确保传递正确的按钮索引
  if (content?.type === 'button') {
    const buttonContents = props.contents.filter(item => item.type === 'button');
    const buttonIndex = buttonContents.findIndex(item => item.contentId === content.contentId);
    content = {
      ...content,
      buttonIndex: buttonIndex, // 在按钮数组中的索引
      buttonPosition: content.positionNumber || 3 // 在模板中的位置
    };
    console.log('LongTextTemplateRenderer - 按钮选择信息:', {
      contentId: content.contentId,
      buttonIndex: buttonIndex,
      buttonPosition: content.buttonPosition
    });
  }
  
  emit('select-content', content);
};

// 处理内容更新
const handleContentUpdate = (newContent) => {
  console.log('LongTextTemplateRenderer - 更新内容:', newContent);
  
  // 如果是按钮内容更新，需要特殊处理
  if (newContent.type === 'button') {
    const contents = [...props.contents];
    
    // 找到要更新的按钮索引
    let targetIndex = -1;
    
    // 优先使用 buttonIndex 信息
    if (typeof newContent.buttonIndex !== 'undefined') {
      const buttonContents = contents.filter(item => item.type === 'button');
      if (newContent.buttonIndex >= 0 && newContent.buttonIndex < buttonContents.length) {
        const targetButton = buttonContents[newContent.buttonIndex];
        targetIndex = contents.findIndex(item => item.contentId === targetButton.contentId);
      }
    } else {
      // 否则根据 contentId 查找
      targetIndex = contents.findIndex(item => item.contentId === newContent.contentId);
    }
    
    if (targetIndex !== -1) {
      console.log('LongTextTemplateRenderer - 更新按钮:', {
        targetIndex: targetIndex,
        originalButton: contents[targetIndex],
        newContent: newContent
      });
      
      // 更新按钮内容，保持原有的 contentId
      contents[targetIndex] = {
        ...contents[targetIndex],
        ...newContent,
        contentId: contents[targetIndex].contentId // 保持原有的contentId
      };
      
      emit('update:content', { type: 'update-contents', contents });
    } else {
      console.warn('LongTextTemplateRenderer - 未找到要更新的按钮:', newContent);
      emit('update:content', newContent);
    }
  } else {
    // 非按钮内容，直接更新
    emit('update:content', newContent);
  }
};

// 处理模板点击事件
const handleTemplateClick = (event) => {
  // 检查点击的目标是否是内容元素
  const isContentElement = event.target.closest('[contenteditable="true"]') || 
                          event.target.closest('.preview-title') ||
                          event.target.closest('.preview-desc') ||
                          event.target.closest('.preview-button') ||
                          event.target.closest('.preview-image') ||
                          event.target.closest('.param-input') ||
                          event.target.closest('.j-btn') ||
                          event.target.closest('.text-element') ||
                          event.target.closest('.image-element') ||
                          event.target.closest('.button-element');
  
  // 如果点击的是内容元素，阻止事件冒泡
  if (isContentElement) {
    console.log('LongTextTemplateRenderer: 点击了内容元素，阻止冒泡');
    event.stopPropagation();
  } else {
    // 如果点击的不是内容元素，让事件继续冒泡到父组件
    console.log('LongTextTemplateRenderer: 点击了空白区域，事件将冒泡');
  }
};

// 组件挂载时检查按钮数量
onMounted(() => {
  if (selectedStyle.value === 'general') {
    const buttons = props.contents?.filter(content => content.type === 'button') || [];
    if (buttons.length < 2) {
      console.log('LongTextTemplateRenderer - 挂载时检查发现一般样式缺少第二个按钮');
    }
  }
});
</script>

<style lang="scss" scoped>
.longtext-template {
  width: 100%;
  padding: 16px;
  border-radius: 10px;
  background: #ffffff;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  
  .preview-title,.title-section,.preview-desc{
    margin: 0;
  }
  
  .preview-desc{
    height: 104px;
    margin-top: 4px;
    font-size: 14px;
    text-indent: 84px;
    color: #606060;
  }
  
  /* 按钮区域 */
  .button-section {
    margin-bottom: 16px;
    display: flex;
    gap: 16px;
    align-items: center;
    font-weight: bold;
    
    &.general-style-buttons {
      // 一般样式的按钮布局，参考通知类模板
      gap: 16px;
      
      .button-wrapper {
        flex: 1;
        
        .preview-button {
          margin: 5px auto;
        }
        
        :deep(.button-element) {
          font-size: 16px;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.3s ease;
        }
      }
      
      // 左按钮样式（类似通知类模板的左按钮）
      .left-button {
        :deep(.button-element) {
          background: #e5e5e5;
          color: #2878ff;
        }
      }
      
      // 右按钮样式（类似通知类模板的右按钮）
      .right-button {
        :deep(.button-element) {
          background: #2878ff;
          color: #ffffff;
        }
      }
    }
    
    // 简单样式的按钮
    &:not(.general-style-buttons) {
      .single-button {
        width: 100%;
        
        .preview-button {
          width: 100%;
          margin: 14px 0 10px;
        }
      }
    }
  }

  .image-section {
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  
  .image-section :deep(.preview-image img) {
    width: 100%;
    height: 102px;
    object-fit: fill;
    border-radius: 8px;
  }
}

/* 编辑状态样式 */
.is-editing {
  border: 2px dashed #007AFF;
  background: rgba(0, 122, 255, 0.05);
}

.is-selected {
  outline: 2px solid #007AFF;
  outline-offset: 2px;
}

/* 卡片模式样式 */
.is-card-mode {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.is-card-mode:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}
</style> 