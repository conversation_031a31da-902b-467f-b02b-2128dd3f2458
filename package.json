{"name": "template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "test": "vite --mode test", "build": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.ts,.tsx", "format": "prettier --write ."}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.8.4", "element-plus": "^2.9.7", "form-data": "^4.0.2", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "uuid": "^11.1.0", "vue": "^3.5.13"}, "devDependencies": {"@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@vitejs/plugin-vue": "^5.2.3", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.5", "eslint-plugin-vue": "^10.0.0", "postcss-html": "^1.8.0", "prettier": "^3.5.3", "sass": "^1.86.0", "stylelint": "^16.17.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^37.0.0", "terser": "^5.39.0", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.4.1", "vite": "^5.2.1"}}