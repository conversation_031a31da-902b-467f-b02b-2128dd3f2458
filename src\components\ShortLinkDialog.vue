<template>
  <el-dialog
    v-model="dialogVisible"
    title="生成短链"
    width="680px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="170px"
      class="short-link-form"
    >
      <el-form-item label="编码类型：" prop="aimCodeType">
        <el-radio-group v-model="form.aimCodeType">
          <el-radio :value="1">群发</el-radio>
          <el-radio :value="2">个性化</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="解析次数：" prop="aimurlShowtimes">
        <template #label>
          <span>解析次数：</span>
          <el-tooltip placement="top" effect="dark">
            <template #content>
              系统会根据输入数量进行扣费，发送短信时请注意不要超过该值，<br>超出部分将无法解析
            </template>
            <el-icon class="icon-question"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input v-model="form.aimurlShowtimes" placeholder="请输入生成短链的最大可解析次数（单位：次）" />
      </el-form-item>

      <el-form-item label="短信签名：" prop="aimurlSmssigns">
        <el-input v-model="form.aimurlSmssigns" placeholder="注意不能输入【】，多签名用回车、换行、中英文逗号或顿号隔开。" />
      </el-form-item>

      <el-form-item label="短链有效期：" prop="aimurlExpiretimes" class="period-validity">
        <el-select v-model="form.aimurlExpiretimes" placeholder="请选择短链有效期">
          <el-option label="7天" value="7"/>
          <el-option label="15天" value="15" />
          <el-option label="21天" value="21" />
          <el-option label="30天" value="30" />
          <el-option label="自定义" value="custom" />
        </el-select>
        <div class="customize" v-if="form.aimurlExpiretimes === 'custom'" >
          <el-input  v-model="form.customExpiretimes" placeholder="短链有效天数不能低于7天，高于30天" @input="form.customExpiretimes = form.customExpiretimes.replace(/\D/g, '')" /> 天
        </div>
      </el-form-item>

      <el-form-item label="短链生成类型：" prop="generationTypes">
        <el-radio-group v-model="form.generationTypes">
          <el-radio :value="1">单个</el-radio>
          <el-radio :value="2" v-show="CustomShortCode">批量</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item prop="generationType">
        <template #label>
          <span>短链：</span>
          <el-tooltip placement="top" effect="dark">
            <template #content>
              标准：指短码部分由系统按一定规则自动生成。<br>自定义：指短码部分由客户自主填写（支持3-10位的大小写英文、数字、符号(-和_)）
            </template>
            <el-icon class="icon-question"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-radio-group v-model="form.generationType">
          <el-radio :value="1">标准</el-radio>
          <el-radio :value="2" v-show="CustomShortCode">自定义</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="短链域名：" prop="domain" class="domain-cont">
        <el-select v-model="form.domain" :loading="isLoading" placeholder="请选择短链域名">
          <el-option 
            v-for="domain in props.domains"
            :key="domain"
            :label="domain"
            :value="domain"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-if="currentTemplateParams.length > 0" label="动参信息：" prop="aimurlParams">
        <div v-for="param in currentTemplateParams" :key="param.name" class="flex-row-start param-item">
          <span class="param-name">{{ param.name }}</span>
          <el-input
            v-model="form[`param_${param.name}`]"
            placeholder="请输入参数值"
          />
        </div>
      </el-form-item>

      <el-form-item prop="customUrl">
        <template #label>
          <span>自定义跳转地址：</span>
          <el-tooltip placement="top" effect="dark">
            <template #content>
              未填时，终端用户点击访问短信原文中的短链，默认跳转到智能短信H5页（即该h5落地页内容与模板内容一样）；<br>
已填时，终端用户点击访问短信原文中的短链，跳转客户填写的链接落地页，填写时必须为http或https做为前缀。<br>注意，若域名为客户域名时，则需客户先做开发后点击短链方可跳转到该链接对应的页面。
            </template>
            <el-icon class="icon-question"><QuestionFilled /></el-icon>
          </el-tooltip>
        </template>
        <el-input type="textarea" v-model="form.customUrl" placeholder="用于指定短信文本中智能短信短链的跳转地址，必须以http或https开头。默认跳转地址为该模板样式的H5页面。" />
      </el-form-item>

      <el-form-item label="厂商信息：" prop="factoryInfo">
        <div class="factory-info">
          <div v-for="factory in parsedFactoryInfos" :key="factory.factoryType" class="factory-item">
            <img 
              v-if="getFactoryIcon(factory.factoryType)" 
              :src="getFactoryIcon(factory.factoryType)" 
              alt="厂商图标" 
              class="factory-icon"
              @error="handleImageError"
            >
            <span>{{ getFactoryName(factory.factoryType) || factory.factoryType }}</span>
          </div>
        </div>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">生成短链</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import api from "@/api/index";
import { FACTORY_MAP, getFactoryIcon, getFactoryName, parseFactoryInfos } from '@/utils/factoryUtils';

const props = defineProps({
  domains: {
    type: Array,
    default: () => []
  },
});

const dialogVisible = ref(false);
const formRef = ref(null);
const isLoading = ref(false);
const CustomShortCode = ref(false);
const currentTemplateParams = ref([]); // 当前选中模板的动态参数

const form = reactive({
  appKey: '',
  tplId: '',
  aimCodeType: 1,
  aimurlExpiretimes: '7',
  customExpiretimes: '',
  aimurlShowtimes: '',
  aimurlParams: '',
  generationType: 1,
  generationTypes: 1,
  domain: '',
  factoryInfo: '',
  aimurlSmssigns: ''
});

const factoryInfo = ref('');


// 优化解析厂商信息逻辑
const parsedFactoryInfos = computed(() => {
  let parsedData = [];
  if (typeof factoryInfo.value === 'string') {
    try {
      // 尝试解析标准 JSON 数组
      parsedData = JSON.parse(factoryInfo.value);
    } catch (jsonError) {
      try {
        // 处理非标准 JSON 格式，如 {FACTORYTYPE:VIVO STATE:1}
        const formatted = factoryInfo.value
          .replace(/\n/g, '')
          .replace(/(\w+):/g, '"$1":')
          .replace(/'/g, '"')
          .replace(/}{/g, '},{');
        parsedData = JSON.parse(`[${formatted}]`);
      } catch (formattedError) {
        console.error('解析厂商信息失败:', formattedError);
      }
    }
  }

  // 过滤出 STATE 为 1 的厂商，并统一属性名
  return parsedData
    .filter(item => item.STATE === 1 || item.state === 1)
    .map(item => ({
      factoryType: (item.FACTORYTYPE || item.factoryType || item.factorytype || '').toUpperCase().trim(),
      state: item.STATE || item.state
    }))
    .filter(item => item.factoryType);
});

// 图片加载错误处理
const handleImageError = (event) => {
  console.error('图片加载失败:', event.target.src);
  event.target.style.display = 'none';
};


// 更新验证规则，使除自定义跳转链接、动参信息、厂商信息外的字段为必填项
const rules = {
  aimCodeType: [
    { required: true, message: '请选择编码类型', trigger: 'change' }
  ],
  aimurlShowtimes: [
    { required: true, message: '请输入短链可解析次数', trigger: 'blur' }
  ],
  aimurlSmssigns: [
    { required: true, message: '请输入短信签名', trigger: 'blur' }
  ],
  aimurlExpiretimes: [
    { 
      required: true,
      validator: (rule, value, callback) => {
        if (value === 'custom' && !form.customExpiretimes) {
          callback(new Error('请输入自定义短链有效期'));
        } else if (value !== 'custom' && !value) {
          callback(new Error('请选择短链有效期'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ],
  generationTypes: [
    { required: true, message: '请选择短链生成类型', trigger: 'change' }
  ],
  generationType: [
    { required: true, message: '请选择短链', trigger: 'change' }
  ],
  domain: [
    { required: true, message: '请选择短链域名', trigger: 'change' }
  ]
};

const open = (template) => {
  dialogVisible.value = true;
  form.appKey = template.appKey;
  form.tplId = template.templateId;
  // 确保获取到正确的 factoryinfos 数据
  if (template.channels && template.channels.length > 0) {
    const firstChannel = template.channels[0];
    if (firstChannel.factoryinfos) {
      factoryInfo.value = firstChannel.factoryinfos;
      form.factoryInfo = firstChannel.factoryinfos;
    } else {
      console.warn('模板 channels 中的第一个 channel 没有 factoryinfos 属性');
    }
  } else {
    console.warn('模板没有 channels 或 channels 为空');
    factoryInfo.value = template.factoryInfos || '';
    form.factoryInfo = template.factoryInfos || '';
  }

  // 提取当前模板的动态参数
  const allParams = new Set();
  if (template.pages && Array.isArray(template.pages)) {
    template.pages.forEach(page => {
      if (page.contents && Array.isArray(page.contents)) {
        page.contents.forEach(content => {
          if (content.content) {
            const matches = content.content.match(/\{#(.*?)#\}/g);
            if (matches) {
              matches.forEach(match => {
                const paramName = match.replace(/\{#|#\}/g, '');
                allParams.add(paramName);
              });
            }
          }
        });
      }
    });
  }
  currentTemplateParams.value = Array.from(allParams).map(name => ({ name }));
};

const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();

     // 构建单个参数对象（保持原有结构）
     const dyncParams = {};
    currentTemplateParams.value.forEach(param => {
      dyncParams[param.name] = form[`param_${param.name}`];
    });

    // 构造数组格式的aimurlParams（将原对象放入数组）
    const aimurlParamsArray = [{
      extData: '',
      dyncParams,
      customUrl: form.customUrl,
      customShortCode: form.generationType === 2 ? '' : ''
    }];

    // 检查数组是否为空（判断数组内所有对象是否为空）
    const isAimurlParamsEmpty = aimurlParamsArray.every(item => {
      return Object.entries(item).every(([key, value]) => {
        if (key === 'dyncParams' && typeof value === 'object' && value !== null) {
          return Object.values(value).every(v => v === '' || v === null || v === undefined);
        }
        return value === '' || value === null || value === undefined;
      });
    });

    const params = {
      appKey: form.appKey,
      tplId: form.tplId,
      aimCodeType: form.aimCodeType,
      aimurlExpiretimes: form.aimurlExpiretimes === 'custom' ? form.customExpiretimes : form.aimurlExpiretimes,
      aimurlShowtimes: form.aimurlShowtimes,
      aimurlParams: isAimurlParamsEmpty ? '' : JSON.stringify(aimurlParamsArray),
      aimurlSmssigns: form.aimurlSmssigns,
      generationType: form.generationType,
      domain: form.domain,
      // Factoryinfos: factoryInfos 
    };

    const res = await api.applyAimUrl(params);

    if (res.code === 0) {
      ElMessage.success('短链生成成功');
      dialogVisible.value = false;
      // 生成链接成功后重置表单
      if (formRef.value) {
        formRef.value.resetFields();
      }
      // 清空自定义有效期
      form.customExpiretimes = '';
      // 清空动态参数
      currentTemplateParams.value.forEach(param => {
        form[`param_${param.name}`] = '';
      });
    } else {
      ElMessage.error(res.message || '短链生成失败');
    }
  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 取消按钮方法
const handleCancel = () => {
  // 关闭对话框
  dialogVisible.value = false;
  // 重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 清空自定义有效期
  form.customExpiretimes = '';

  // 清空动态参数
  currentTemplateParams.value.forEach(param => {
    form[`param_${param.name}`] = '';
  });
};

// 监听 currentTemplateParams 变化，动态添加响应式属性
watch(() => currentTemplateParams.value, (newParams) => {
  newParams.forEach(param => {
    if (!form[`param_${param.name}`]) {
      form[`param_${param.name}`] = '';
    }
  });
}, { deep: true, immediate: true });

defineExpose({
  open
});
</script>

<style scoped lang="scss">
.short-link-form {
  :deep(.el-textarea__inner),:deep(.el-input__inner){
    &::placeholder {
      font-size: 13px;
      color: #ccc;
      font-weight: normal;
    }
  }
  :deep(.el-select__placeholder.is-transparent) {
    font-size: 13px !important; 
  }
  .el-input, .el-textarea ,.domain-cont .el-select{
    width: 440px;
  }
  .period-validity{
    .el-select{
      width: 34%;
      margin-right: 10px;
    }

  }
  .customize{
    display: flex;
    width: 60.5%;
    .el-input{
      margin-right: 8px;
    }
  }
  .icon-question {
    color: #409eff;
    font-size: 16px;
    margin-top: 8px;
  }
  .param-item{
    border-radius: 4px;
    height: 32px;
    line-height: 32px;
    box-shadow: 0 0 0 1px #dcdfe6 inset;
    margin-bottom: 10px;
    .param-name{
      width: 100px;
      text-align: center;
      border-right: none;
    }
    .el-input {
      width: 340px;
    
    }
    :deep(.el-input__wrapper){
      border-radius: 0 4px 4px 0;
    }
  }
  
}

.dialog-footer {
  text-align: right;
}

.factory-info {
  display: flex;
  align-items: center;
  gap: 8px;
  .factory-item{
    display: flex;
    align-items: center;
  }
  .factory-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;
    margin-right: 5px;
    border-radius: 50%;
  }
}
</style>