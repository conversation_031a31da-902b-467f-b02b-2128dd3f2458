/**
 * 参数管理服务 - 提供统一的参数操作API
 * 
 * 这个服务封装了所有与参数相关的操作，包括：
 * - 参数的创建、插入、删除
 * - 参数ID的分配和管理
 * - 参数与面板的关联
 * - 参数的收集和同步
 */

import { ref, reactive, inject, computed } from 'vue';

// 参数服务单例
let instance = null;

/**
 * 参数服务类，用于处理参数相关的操作
 */
class ParamService {
  constructor() {
    // 确保单例模式
    if (instance) {
      return instance;
    }
    
    // 存储当前使用的参数ID
    this.usedParamIds = new Set();
    
    // 已删除的参数ID，可重复使用
    this.deletedParamIds = [];
    
    // 参数与面板的关联关系
    this.paramPanelMap = new Map();
    
    // 设置为单例
    instance = this;
    
    // 存储到全局变量，供其他组件使用
    window.PARAM_SERVICE_INSTANCE = this;
    
    // 初始化全局参数管理器
    this.initGlobalParamManager();
  }
  
  /**
   * 初始化全局参数管理器
   * @returns {Object} 全局参数管理器
   */
  initGlobalParamManager() {
    if (window.GLOBAL_PARAM_MANAGER) {
      return window.GLOBAL_PARAM_MANAGER;
    }
    
    window.GLOBAL_PARAM_MANAGER = {
      usedIds: new Set(),
      deletedIds: new Set(),
      panelParams: {},
      paramGroups: { 'global': new Set() },
      
      reset() {
        this.usedIds.clear();
        this.panelParams = {};
        this.paramGroups = { 'global': new Set() };
      }
    };
    
    return window.GLOBAL_PARAM_MANAGER;
  }

  /**
   * 初始化参数服务
   * @returns {boolean} 初始化成功与否
   */
  async init() {
    try {
      this.initGlobalParamManager();
      
      // 从本地存储加载已删除的参数ID
      try {
        const stored = localStorage.getItem('deletedParamIds');
        if (stored) {
          const validIds = JSON.parse(stored).filter(id => id && !isNaN(parseInt(id)));
          this.deletedParamIds = new Set(validIds.map(id => String(id)));
          window.GLOBAL_PARAM_MANAGER.deletedIds = new Set(validIds.map(id => String(id)));
        }
      } catch (e) {
        console.error('加载已删除参数ID失败:', e);
      }
      
      // 从DOM收集现有的参数ID
      await this.collectParamIdsFromDOM();
      
      // 初始化完成
      this.initialized = true;
      
      // 设置全局实例
      if (typeof window !== 'undefined') {
        window.PARAM_SERVICE_INSTANCE = this;
      }
      
      return true;
    } catch (error) {
      console.error('参数服务初始化失败:', error);
      return false;
    }
  }

  /**
   * 获取下一个可用的参数ID
   * @param {string} panelId - 面板ID
   * @returns {string} - 参数ID
   */
  getNextParamId(panelId) {
    try {
      // 确保全局参数管理器已初始化
      if (!window.GLOBAL_PARAM_MANAGER) {
        this.initGlobalParamManager();
      }
      
      // 检查全局删除列表中是否有可重用的ID
      if (window.DELETED_PARAM_IDS && window.DELETED_PARAM_IDS.size > 0) {
        const deletedArray = Array.from(window.DELETED_PARAM_IDS)
          .map(id => parseInt(id))
          .filter(id => !isNaN(id))
          .sort((a, b) => a - b);
        
        if (deletedArray.length > 0) {
          const smallestDeletedId = deletedArray[0];
          window.DELETED_PARAM_IDS.delete(String(smallestDeletedId));
          
          // 标记为已使用
          this.usedParamIds.add(String(smallestDeletedId));
          window.GLOBAL_PARAM_MANAGER.usedIds.add(String(smallestDeletedId));
          
          // 记录参数与面板的关联
          if (panelId) {
            this.associateParamWithPanel(String(smallestDeletedId), panelId);
          }
          
          return smallestDeletedId;
        }
      }
      
      // 检查本地存储中是否有可重用的ID
      try {
        const stored = localStorage.getItem('deletedParamIds');
        if (stored) {
          const deletedIds = JSON.parse(stored);
          if (Array.isArray(deletedIds) && deletedIds.length > 0) {
            const validIds = deletedIds
              .map(id => parseInt(id))
              .filter(id => !isNaN(id))
              .sort((a, b) => a - b);
            
            if (validIds.length > 0) {
              const smallestDeletedId = validIds[0];
              
              // 从本地存储中移除这个ID
              const updatedIds = deletedIds.filter(id => parseInt(id) !== smallestDeletedId);
              localStorage.setItem('deletedParamIds', JSON.stringify(updatedIds));
              
              // 从服务实例中移除
              this.deletedParamIds.delete(String(smallestDeletedId));
              
              // 标记为已使用
              this.usedParamIds.add(String(smallestDeletedId));
              window.GLOBAL_PARAM_MANAGER.usedIds.add(String(smallestDeletedId));
              
              // 记录参数与面板的关联
              if (panelId) {
                this.associateParamWithPanel(String(smallestDeletedId), panelId);
              }
              
              return smallestDeletedId;
            }
          }
        }
      } catch (e) {
        console.error('从本地存储获取已删除参数ID失败:', e);
      }
      
      // 检查服务实例的已删除ID
      if (this.deletedParamIds.size > 0) {
        const deletedArray = Array.from(this.deletedParamIds)
          .map(id => parseInt(id))
          .filter(id => !isNaN(id))
          .sort((a, b) => a - b);
        
        if (deletedArray.length > 0) {
          const id = deletedArray[0];
          this.deletedParamIds.delete(String(id));
          this.usedParamIds.add(String(id));
          window.GLOBAL_PARAM_MANAGER.usedIds.add(String(id));
          
          // 记录参数与面板的关联
          if (panelId) {
            this.associateParamWithPanel(String(id), panelId);
          }
          
          return id;
        }
      }
      
      // 检查全局参数管理器的已删除ID
      if (window.GLOBAL_PARAM_MANAGER && window.GLOBAL_PARAM_MANAGER.deletedIds && window.GLOBAL_PARAM_MANAGER.deletedIds.size > 0) {
        const deletedArray = Array.from(window.GLOBAL_PARAM_MANAGER.deletedIds)
          .map(id => parseInt(id))
          .filter(id => !isNaN(id))
          .sort((a, b) => a - b);
        
        if (deletedArray.length > 0) {
          const smallestDeletedId = deletedArray[0];
          window.GLOBAL_PARAM_MANAGER.deletedIds.delete(String(smallestDeletedId));
          
          // 标记为已使用
          this.usedParamIds.add(String(smallestDeletedId));
          window.GLOBAL_PARAM_MANAGER.usedIds.add(String(smallestDeletedId));
          
          // 记录参数与面板的关联
          if (panelId) {
            this.associateParamWithPanel(String(smallestDeletedId), panelId);
          }
          
          return smallestDeletedId;
        }
      }
      
      // 如果没有可重用的ID，生成新的ID
      let paramId;
      
      // 从全局参数管理器获取下一个ID
      if (window.GLOBAL_PARAM_MANAGER && window.GLOBAL_PARAM_MANAGER.usedIds) {
        const usedArray = Array.from(window.GLOBAL_PARAM_MANAGER.usedIds)
          .map(id => parseInt(id))
          .filter(id => !isNaN(id));
        
        const maxUsed = usedArray.length > 0 ? Math.max(...usedArray) : 0;
        paramId = maxUsed + 1;
      } else {
        // 回退方法：基于服务实例的已使用ID
        const usedArray = Array.from(this.usedParamIds)
          .map(id => parseInt(id))
          .filter(id => !isNaN(id));
        
        const maxUsed = usedArray.length > 0 ? Math.max(...usedArray) : 0;
        paramId = maxUsed + 1;
      }
      
      // 如果计算出的ID仍然为0或负数，强制设为1
      if (paramId <= 0) {
        paramId = 1;
      }
      
      // 标记为已使用
      this.usedParamIds.add(String(paramId));
      if (window.GLOBAL_PARAM_MANAGER) {
        window.GLOBAL_PARAM_MANAGER.usedIds.add(String(paramId));
      }
      
      // 记录参数与面板的关联
      if (panelId) {
        this.associateParamWithPanel(String(paramId), panelId);
      }
      
      return paramId;
    } catch (error) {
      console.error('获取下一个参数ID失败:', error);
      // 出错时返回一个安全的默认值
      return 1;
    }
  }

  /**
   * 将参数与面板关联
   * @param {string} paramId - 参数ID
   * @param {string} panelId - 面板ID
   */
  associateParamWithPanel(paramId, panelId) {
    if (!paramId || !panelId) return;
    
    this.paramPanelMap.set(paramId, panelId);
  }
  
  /**
   * 创建参数HTML
   * @param {string} paramId - 参数ID
   * @param {string} panelId - 面板ID
   * @returns {string} - 参数HTML
   */
  createParamHTML(paramId, panelId) {
    if (!paramId) return '';
    
    // 记录参数使用
    this.recordParamUsage(paramId);
    
    // 关联参数与面板
    if (panelId) {
      this.associateParamWithPanel(paramId, panelId);
    }
    
    // 构建参数HTML - 使用input标签而不是span，以保持与原有代码兼容
    return `<input type="button" class="j-btn param-input" value="{#param${paramId}#}" data-param-id="${paramId}" data-panel-id="${panelId || ''}" readonly="readonly">`;
  }
  
  /**
   * 记录参数使用情况
   * @param {string} paramId - 参数ID
   */
  recordParamUsage(paramId) {
    if (!paramId) return;
    
    const id = parseInt(paramId);
    if (!isNaN(id)) {
      this.usedParamIds.add(id);
      
      // 同步到全局参数管理器
      if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.recordParamUsage === 'function') {
        window.GLOBAL_PARAM_MANAGER.recordParamUsage(paramId);
      }
    }
  }
  
  /**
   * 格式化带参数的内容
   * @param {string} content - 原始内容
   * @param {string} panelId - 面板ID
   * @returns {string} - 格式化后的HTML
   */
  formatContentWithParams(content, panelId) {
    if (!content) return '';
    
    // 如果内容中已有HTML标签，直接返回
    if (content.includes('<span class="j-btn"') || content.includes('<input type="button" class="j-btn"')) {
      return content;
    }
    
    // 参数正则表达式
    const paramRegex = /({#param\d+#})/g;
    
    // 没有参数，直接返回
    if (!paramRegex.test(content)) {
      return content;
    }
    
    // 替换参数
    return content.replace(paramRegex, (match) => {
      const paramId = match.match(/{#param(\d+)#}/)[1];
      
      // 记录参数使用
      this.recordParamUsage(paramId);
      
      // 关联参数与面板
      if (panelId) {
        this.associateParamWithPanel(paramId, panelId);
      }
      
      // 返回参数HTML，使用input标签而不是span，保持一致性
      return `<input type="button" class="j-btn param-input" value="${match}" data-param-id="${paramId}" data-panel-id="${panelId || ''}" readonly="readonly">`;
    });
  }
  
  /**
   * 从HTML中提取纯文本和参数标记
   * @param {string} html - HTML内容
   * @returns {string} - 提取的文本和参数标记
   */
  extractTextWithParams(html) {
    if (!html) return '';
    
    // 创建临时容器
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // 保存最终结果
    let result = '';
    
    // 遍历所有子节点
    const processNode = (node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        // 文本节点，直接添加
        result += node.textContent;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 元素节点，检查是否是参数
        if (node.classList.contains('j-btn') || node.hasAttribute('data-param-id')) {
          // 是参数，获取参数ID
          const paramId = node.getAttribute('data-param-id');
          if (paramId) {
            // 添加参数标记
            result += `{#param${paramId}#}`;
          } else {
            // 尝试从内容中提取参数ID
            const match = node.textContent.match(/{#param(\d+)#}/);
            if (match && match[1]) {
              result += `{#param${match[1]}#}`;
            } else {
              // 无法识别的参数，使用文本内容
              result += node.textContent;
            }
          }
        } else {
          // 不是参数，递归处理子节点
          Array.from(node.childNodes).forEach(processNode);
        }
      }
    };
    
    // 处理所有子节点
    Array.from(tempDiv.childNodes).forEach(processNode);
    
    return result;
  }
  
  /**
   * 从文本中提取纯文本（不包含参数）
   * @param {string} text - 文本内容
   * @returns {string} - 提取的纯文本
   */
  extractTextWithoutParams(text) {
    if (!text) return '';
    
    // 参数正则表达式
    const paramRegex = /{#param\d+#}/g;
    
    // 替换参数为空字符串
    return text.replace(paramRegex, '');
  }
  
  /**
   * 从DOM中收集参数元素
   * @param {HTMLElement} [searchRoot=document.body] - 搜索的根元素
   * @returns {Array<string>} - 收集到的参数ID数组
   */
  collectParamsFromDOM(searchRoot = document.body) {
    try {
      // 优先使用模板编辑区域作为搜索根元素
      const templateDialogRoot = document.querySelector('.template-dialog-root');
      const actualRoot = searchRoot || templateDialogRoot || document.body;
      
      // 收集参数元素
      const paramElements = actualRoot.querySelectorAll('input.j-btn[data-param-id], span.j-btn[data-param-id]');
      const collectedIds = new Set();
      
      // 处理每个参数元素
      paramElements.forEach(element => {
        const paramId = element.getAttribute('data-param-id');
        const panelId = element.getAttribute('data-panel-id') || element.getAttribute('data-owner-panel');
        
        if (paramId) {
          // 添加到已使用ID集合
          this.recordParamUsage(paramId);
          collectedIds.add(paramId);
          
          // 如果有面板ID，关联参数到面板
          if (panelId) {
            this.associateParamWithPanel(paramId, panelId);
          }
        }
      });
      
      // 在文本内容中查找参数格式
      const paramRegex = /{#param(\d+)#}/g;
      const allElements = actualRoot.querySelectorAll('*');
      
      allElements.forEach(element => {
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE') return;
        
        const content = element.innerHTML || element.value || element.textContent || '';
        
        if (typeof content === 'string' && content.includes('{#param')) {
          let match;
          const regex = new RegExp(paramRegex);
          while ((match = regex.exec(content)) !== null) {
            if (match[1]) {
              const paramId = match[1];
              this.recordParamUsage(paramId);
              collectedIds.add(paramId);
            }
          }
        }
      });
      
      return Array.from(collectedIds);
    } catch (error) {
      console.error('从DOM收集参数失败:', error);
      return [];
    }
  }

  /**
   * 标记参数为已删除
   * @param {string} paramId - 参数ID
   */
  markParamAsDeleted(paramId) {
    if (!paramId) return;
    
    try {
      const id = parseInt(paramId);
      if (isNaN(id)) return;
      
      // 从已使用集合中移除
      this.usedParamIds.delete(id);
      
      // 确保全局变量存在
      if (!window.DELETED_PARAM_IDS) {
        window.DELETED_PARAM_IDS = [];
      }
      
      // 添加到本地和全局的已删除集合
      const addToArray = (arr, value) => {
        if (!arr.includes(value)) {
          arr.push(value);
          arr.sort((a, b) => a - b); // 排序以便优先使用小ID
          return true;
        }
        return false;
      };
      
      // 添加到服务的删除数组
      const addedToLocal = addToArray(this.deletedParamIds, id);
      
      // 添加到全局删除数组
      const addedToGlobal = addToArray(window.DELETED_PARAM_IDS, id);
      
      // 保存到本地存储，确保页面刷新后仍可用
      try {
        let storedDeletedIds = [];
        try {
          storedDeletedIds = JSON.parse(localStorage.getItem('DELETED_PARAM_IDS') || '[]');
        } catch (e) {
          console.warn('读取本地存储的已删除参数ID失败，将重置为空数组');
          storedDeletedIds = [];
        }
        
        const addedToStorage = addToArray(storedDeletedIds, id);
        
        if (addedToStorage) {
          localStorage.setItem('DELETED_PARAM_IDS', JSON.stringify(storedDeletedIds));
        } else {
        }
      } catch (e) {
        console.error('保存已删除参数ID到本地存储失败:', e);
      }
      
      // 同步到全局参数管理器
      if (window.GLOBAL_PARAM_MANAGER) {
        try {
          // 首先移除ID
          if (window.GLOBAL_PARAM_MANAGER.usedIds) {
            window.GLOBAL_PARAM_MANAGER.usedIds.delete(id);
          }
          
          // 然后加入到删除列表
          if (window.GLOBAL_PARAM_MANAGER.deletedIds) {
            addToArray(window.GLOBAL_PARAM_MANAGER.deletedIds, id);
          }
          
          // 如果有标记为删除的方法，也调用
          if (typeof window.GLOBAL_PARAM_MANAGER.markParamDeleted === 'function') {
            window.GLOBAL_PARAM_MANAGER.markParamDeleted(paramId);
          } else if (typeof window.GLOBAL_PARAM_MANAGER.markParamAsDeleted === 'function') {
            window.GLOBAL_PARAM_MANAGER.markParamAsDeleted(paramId);
          }
        } catch (e) {
          console.warn('更新全局参数管理器失败:', e);
        }
      }
      
      // 从DOM中移除与此参数相关的元素属性，防止误判
      try {
        document.querySelectorAll(`[data-param-id="${id}"]`).forEach(el => {
          el.removeAttribute('data-param-id');
        });
      } catch (e) {
        console.warn('清理DOM中的参数ID属性失败:', e);
      }
      
      return true;
    } catch (error) {
      console.error('标记参数为已删除时出错:', error);
      return false;
    }
  }
}

/**
 * 参数服务组合函数
 * @returns {ParamService} - 参数服务实例
 */
export function useParamService() {
  return new ParamService();
}