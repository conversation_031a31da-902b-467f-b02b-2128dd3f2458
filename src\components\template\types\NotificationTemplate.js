/**
 * 通知类模板处理器
 * 负责处理通知类模板的特殊逻辑
 */

export default {
  /**
   * 获取模板渲染器组件
   * @returns {string} 渲染器组件名称
   */
  getRenderer() {
    return 'NotificationTemplateRenderer';
  },

  /**
   * 处理模板数据
   * @param {Object} template 原始模板数据
   * @returns {Object} 处理后的模板数据
   */
  processTemplate(template) {
    if (!template) return template;

    // 只在调试模式下记录详细日志
    const isDebugMode = false; // 可以通过环境变量控制
    
    if (isDebugMode) {
      console.log('NotificationTemplate.processTemplate - 开始处理模板:', template);
    }

    // 确保模板有基本结构
    const processedTemplate = {
      ...template,
      tplType: template.tplType || '通知类'
    };

    // 解析pages数据
    let pages = template.pages;
    if (typeof pages === 'string') {
      try {
        pages = JSON.parse(pages);
      } catch (error) {
        console.error('NotificationTemplate.processTemplate - 解析pages失败:', error);
        pages = [];
      }
    }

    // 确保pages是数组
    if (!Array.isArray(pages)) {
      pages = [];
    }

    // 处理每一页的内容
    const processedPages = pages.map(page => {
      if (!page.contents || !Array.isArray(page.contents)) {
        return page;
      }

      // 处理页面内容，确保文本内容正确显示
      const processedContents = page.contents.map(content => {
        const processedContent = {
          ...content,
          // 确保内容文本正确显示，优先使用content字段，其次是text字段
          content: content.content || content.text || '',
          // 确保类型正确
          type: content.type || 'text',
          // 确保有contentId
          contentId: content.contentId || `${content.type || 'text'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          // 确保可见性
          visible: content.visible !== false,
          // 添加模板相关信息
          userId: template.userId || null,
          templateId: template.templateId || null,
          pageId: content.pageId || page.pageId || 1
        };

        // 只在调试模式下记录详细日志
        if (isDebugMode) {
          console.log('NotificationTemplate.processTemplate - 处理内容项:', {
            原始: content,
            处理后: processedContent
          });
        }

        return processedContent;
      });

      return {
        ...page,
        contents: processedContents
      };
    });

    processedTemplate.pages = processedPages;

    if (isDebugMode) {
      console.log('NotificationTemplate.processTemplate - 处理完成:', processedTemplate);
    }
    
    return processedTemplate;
  },

  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容数组
   */
  initializeContents(template) {
    if (!template) return [];

    // 确保模板有页面结构
    if (!template.pages || !Array.isArray(template.pages) || template.pages.length === 0) {
      template.pages = [{
        pageId: 1,
        contents: []
      }];
    }

    // 确保第一页有contents数组
    if (!template.pages[0].contents) {
      template.pages[0].contents = [];
    }

    // 获取第一页的内容
    const contents = template.pages[0].contents || [];
    
    // 如果有接口返回的内容，优先使用并处理
    if (contents.length > 0) {
      return contents.map(content => ({
        ...content,
        // 确保内容有正确的显示属性
        visible: content.visible !== false,
        // 确保内容文本正确显示
        content: content.content || content.text || '',
        // 确保类型正确
        type: content.type || 'text',
        // 确保有contentId
        contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        // 添加模板相关信息
        userId: template.userId || null,
        templateId: template.templateId || null
      }));
    }
    
    // 如果没有内容，返回空数组，让ensureDefaultContent处理
    return [];
  },

  /**
   * 确保有默认内容
   * @param {Array} contents 现有内容数组
   * @param {Object} template 模板数据（可选）
   * @returns {Array} 确保有默认内容的数组
   */
  ensureDefaultContent(contents, template = null) {
    // 如果已经有内容，直接返回
    if (contents && Array.isArray(contents) && contents.length > 0) {
      return contents;
    }

    // 如果没有内容，创建默认的通知类模板内容
    return this.createDefaultContents(template);
  },

  /**
   * 创建默认的通知类模板内容
   * @param {Object} template 模板数据（可选，用于从接口获取默认内容）
   * @returns {Array} 默认内容数组
   */
  createDefaultContents(template = null) {
    // 如果有模板数据且有页面内容，优先使用接口返回的内容
    if (template && template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
      const page = template.pages[0];
      if (page.contents && Array.isArray(page.contents) && page.contents.length > 0) {
        return page.contents.map(content => ({
          ...content,
          contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          userId: template.userId || null,
          templateId: template.templateId || null
        }));
      }
    }

    // 如果没有接口数据，创建默认的通知类模板内容
    const defaultContents = [
      // 通知标题
      {
        contentId: 'notification-title-' + Date.now(),
        type: 'text',
        isTextTitle: 1,
        content: '编辑标题，最多显示15个字',
        positionNumber: 1
      },
      // 通知内容
      {
        contentId: 'notification-content-' + Date.now(),
        type: 'text',
        isTextTitle: 0,
        content: '【国泰海通】编辑文本描述，最多显示20个字',
        positionNumber: 2
      }
    ];
    
    // 添加默认的2对参数
    for (let i = 0; i < 2; i++) {
      const lineNumber = i + 3; // 从第3行开始
      
      // 左侧参数名称
      defaultContents.push({
        contentId: `param-left-${i + 1}-${Date.now()}`,
        type: 'text', 
        content: '编辑名称',
        isTextTitle: 0,
        positionNumber: (i + 1) * 2 + 1
      });
      
      // 右侧参数值
      defaultContents.push({
        contentId: `param-right-${i + 1}-${Date.now()}`,
        type: 'text',
        content: `{#param${i + 1}#}`,
        isTextTitle: 0,
        positionNumber: (i + 1) * 2 + 2
      });
    }
    
    // 添加默认的2个按钮
    for (let i = 0; i < 2; i++) {
      defaultContents.push({
        contentId: `button-${i + 1}-${Date.now()}`,
        type: 'button',
        content: '编辑按钮',
        positionNumber: i + 7  // 从7开始，因为前面已经有6个内容了
      });
    }
    
    return defaultContents;
  },

  /**
   * 生成通知类模板的提交数据
   * @param {Array} contents 模板内容数组
   * @param {Object} settings 通知设置 { maxVisibleParams, maxVisibleButtons }
   * @param {Object} options 其他选项 { isEditMode, templateName }
   * @returns {Array} 处理后的提交数据
   */
  generateSubmitData(contents, settings = {}, options = {}) {
    console.log('NotificationTemplate.generateSubmitData - 开始处理提交数据:', {
      contents: contents.length,
      settings,
      options
    });

    const { maxVisibleParams = 2, maxVisibleButtons = 2 } = settings;
    const { isEditMode = false, templateName = '' } = options;

    // 分离不同类型的内容
    const textContents = contents.filter(c => c.type === 'text');
    const buttonContents = contents.filter(c => c.type === 'button');
    const imageContents = contents.filter(c => c.type === 'image');
    const otherContents = contents.filter(c => !['text', 'button', 'image'].includes(c.type));

    console.log('NotificationTemplate.generateSubmitData - 内容分类:', {
      text: textContents.length,
      button: buttonContents.length,
      image: imageContents.length,
      other: otherContents.length
    });

    // 识别参数对
    const paramPairs = this.identifyParamPairs(textContents);
    console.log('NotificationTemplate.generateSubmitData - 识别到的参数对:', paramPairs.map(pair => ({
      namePosition: pair.name.positionNumber,
      valuePosition: pair.value.positionNumber,
      nameContent: pair.name.content,
      valueContent: pair.value.content
    })));

    // 过滤参数对（根据用户设置）
    const visibleParamPairs = paramPairs.slice(0, maxVisibleParams);
    console.log('NotificationTemplate.generateSubmitData - 可见参数对数量:', visibleParamPairs.length);

    // 过滤按钮（根据用户设置）
    const visibleButtons = buttonContents.slice(0, maxVisibleButtons);
    console.log('NotificationTemplate.generateSubmitData - 可见按钮数量:', visibleButtons.length);

    // 收集所有需要提交的内容
    const submitContents = [];

    // 1. 添加非参数对的文本内容（标题、描述等）
    const nonParamTexts = textContents.filter(content => {
      // 排除参数对中的内容
      return !visibleParamPairs.some(pair => 
        pair.name.contentId === content.contentId || 
        pair.value.contentId === content.contentId
      );
    });
    submitContents.push(...nonParamTexts);

    // 2. 添加可见的参数对内容
    visibleParamPairs.forEach(pair => {
      submitContents.push(pair.name, pair.value);
    });

    // 3. 添加可见的按钮
    submitContents.push(...visibleButtons);

    // 4. 添加图片和其他内容
    submitContents.push(...imageContents, ...otherContents);

    // 处理每个内容项
    const processedContents = submitContents.map(content => {
      const processedContent = JSON.parse(JSON.stringify(content));

      // 在编辑模式下保留contentId，新建模式下删除
      if (!isEditMode) {
        delete processedContent.contentId;
      } else if (!processedContent.contentId) {
        processedContent.contentId = this.generateUniqueId();
      }

      // 处理可能有HTML标签的文本内容
      if (processedContent.type === 'text' && processedContent.editContent) {
        processedContent.content = this.cleanTextContent(processedContent.editContent);
      }

      // 如果是图片类型，可以不传content或传空
      if (processedContent.type === 'image') {
        processedContent.content = '';
      }

      // 清理不需要的字段，只保留必要的字段
      const cleanContent = {};
      const fieldsToKeep = [
        'type', 'content', 'src', 'positionNumber', 'isTextTitle'
      ];

      // 只为图片和按钮类型保留点击事件相关字段
      if (processedContent.type === 'image' || processedContent.type === 'button') {
        fieldsToKeep.push('actionType', 'actionJson');
      }

      // 在编辑模式下保留contentId
      if (isEditMode) {
        fieldsToKeep.push('contentId');
      }

      fieldsToKeep.forEach(field => {
        if (processedContent[field] !== undefined && processedContent[field] !== null && processedContent[field] !== '') {
          cleanContent[field] = processedContent[field];
        }
      });

      return cleanContent;
    });

    // 按 positionNumber 排序，确保数据顺序正确
    processedContents.sort((a, b) => {
      const posA = a.positionNumber !== undefined ? a.positionNumber : 999;
      const posB = b.positionNumber !== undefined ? b.positionNumber : 999;
      return posA - posB;
    });

    console.log('NotificationTemplate.generateSubmitData - 最终提交内容数量:', processedContents.length);
    console.log('NotificationTemplate.generateSubmitData - 排序后的内容:', processedContents.map(c => ({
      type: c.type,
      positionNumber: c.positionNumber,
      content: c.content
    })));
    
    return processedContents;
  },

  /**
   * 识别参数对
   * @param {Array} textContents 文本内容数组
   * @returns {Array} 参数对数组 [{name, value}, ...]
   */
  identifyParamPairs(textContents) {
    const paramPairs = [];
    const processedPositionNumbers = new Set();

    // 按positionNumber排序
    const sortedTexts = textContents
      .filter(content => content.positionNumber >= 3) // 从positionNumber 3开始
      .sort((a, b) => a.positionNumber - b.positionNumber);

    console.log('NotificationTemplate.identifyParamPairs - 排序后的文本内容:', sortedTexts.map(t => ({
      positionNumber: t.positionNumber,
      content: t.content,
      contentId: t.contentId
    })));

    // 遍历所有文本内容，找到参数对
    sortedTexts.forEach(content => {
      if (processedPositionNumbers.has(content.positionNumber)) {
        return; // 已经处理过
      }

      // 找到参数对的名称（positionNumber为奇数）
      if (content.positionNumber % 2 === 1) {
        const paramName = content;
        const paramValue = sortedTexts.find(c => 
          c.positionNumber === content.positionNumber + 1
        );

        if (paramValue) {
          paramPairs.push({ name: paramName, value: paramValue });
          processedPositionNumbers.add(content.positionNumber);
          processedPositionNumbers.add(content.positionNumber + 1);

          console.log(`NotificationTemplate.identifyParamPairs - 找到参数对: ${content.positionNumber}, ${content.positionNumber + 1}`);
        } else {
          console.log(`NotificationTemplate.identifyParamPairs - 警告: positionNumber ${content.positionNumber} 没有对应的值`);
        }
      }
    });

    // 按positionNumber排序，确保顺序正确
    paramPairs.sort((a, b) => a.name.positionNumber - b.name.positionNumber);

    console.log('NotificationTemplate.identifyParamPairs - 最终识别到的参数对:', paramPairs.map(pair => ({
      namePosition: pair.name.positionNumber,
      valuePosition: pair.value.positionNumber,
      nameContent: pair.name.content,
      valueContent: pair.value.content
    })));

    return paramPairs;
  },

  /**
   * 清理文本内容，去除HTML标签，保留纯文本和参数
   * @param {string} htmlContent HTML内容
   * @returns {string} 清理后的文本内容
   */
  cleanTextContent(htmlContent) {
    if (!htmlContent) return '';

    // 创建临时容器解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;

    // 提取文本和参数
    let cleanedContent = '';

    // 处理所有子节点
    for (const node of tempDiv.childNodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        // 文本节点直接添加
        cleanedContent += node.textContent;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 如果是参数按钮元素
        if ((node.tagName === 'INPUT' || node.tagName === 'SPAN') && 
            (node.classList.contains('j-btn') || node.classList.contains('param-input'))) {
          // 添加参数值
          cleanedContent += node.value || node.textContent;
        } else {
          // 其他元素递归提取文本
          cleanedContent += node.textContent;
        }
      }
    }

    return cleanedContent;
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  },

  /**
   * 验证模板数据
   * @param {Object} template 模板数据
   * @returns {boolean} 是否有效
   */
  validateTemplate(template) {
    if (!template) return false;
    
    // 检查必要的字段
    if (!template.templateId || !template.templateName) {
      return false;
    }

    return true;
  },

  /**
   * 获取模板默认配置
   * @returns {Object} 默认配置
   */
  getDefaultConfig() {
    return {
      maxTextLength: 200,
      maxButtons: 2,
      maxImages: 1,
      allowedContentTypes: ['text', 'button', 'image']
    };
  }
}; 