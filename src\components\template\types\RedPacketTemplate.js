/**
 * 红包模板类
 * 处理红包样式的模板
 */

import BaseTemplate from './BaseTemplate';

class RedPacketTemplate extends BaseTemplate {
  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容
   */
  static initializeContents(template) {
    // 设置全局变量标记当前是红包模板
    window.TEMPLATE_IS_REDPACKET = true;
    
    // 如果模板已经有内容，使用基类方法初始化
    if (template && template.pages) {
      const contents = super.initializeContents(template);
      if (contents && contents.length > 0) {
        // 为内容添加红包特定的类名
        contents.forEach(content => {
          // 新增：背景类型内容初始化 aimOppoBackground
          if (content.type === 'background') {
            content.aimOppoBackground = content.aimOppoBackground || ''; // 已有值则保留，否则初始化为空
          }
          if (content.type === 'text' && content.isTextTitle === 1) {
            content.className = 'red-packet-title';
          } else if (content.type === 'text' && content.positionNumber === 2) {
            content.className = 'red-packet-name';
          } else if (content.type === 'text') {
            content.className = 'red-packet-desc';
          } else if (content.type === 'image' && content.positionNumber === 2) {
            content.className = 'circle-image';
          } else if (content.type === 'button') {
            content.className = 'red-packet-button';
          }
        });
        return contents;
      }
    }
    
    // 如果没有内容，创建默认内容
    return this.createDefaultContents(template);
  }
  
  /**
   * 创建红包模板默认内容
   * @param {Object} template 模板数据
   * @returns {Array} 默认内容数组
   */
  static createDefaultContents(template) {
    return [
      // 背景图片
      {
        contentId: 'bg-' + Date.now(),
        type: 'background',
        isBackground: 1,
        src: '',
        content: '',
        positionNumber: 1,
        className: 'red-packet-background',
        aimOppoBackground: '',
      },
      // 标题文本
      {
        contentId: 'title-' + Date.now(),
        type: 'text',
        isTextTitle: 1,
        content: '金猪送福',
        positionNumber: 3,
        className: 'red-packet-title'
      },
      // 名称文本
      {
        contentId: 'name-' + Date.now(),
        type: 'text',
        isTextTitle: 0,
        content: '红包名称',
        positionNumber: 2,
        className: 'red-packet-name'
      },
      // 用户头像
      {
        contentId: 'avatar-' + Date.now(),
        type: 'image',
        src: '',
        content: '',
        positionNumber: 2,
        className: 'circle-image'
      },
      // 领取按钮
      {
        contentId: 'btn-' + Date.now(),
        type: 'button',
        content: '立即领取',
        positionNumber: 4,
        className: 'red-packet-button',
        clickEvent: {
          type: 'OPEN_BROWSER',
          target: '',
          data: {}
        }
      }
    ];
  }
  
  /**
   * 验证红包模板内容
   * @param {Array} contents 模板内容数组
   * @returns {boolean} 验证结果
   */
  static validateContents(contents) {
    // 首先使用基类验证
    if (!super.validateContents(contents)) {
      return false;
    }
    
    // 红包模板必须有背景和按钮
    const hasBackground = contents.some(content => 
      (content.type === 'background' || (content.type === 'image' && content.isBackground === 1))
    );
    
    const hasButton = contents.some(content => 
      content.type === 'button'
    );
    
    return hasBackground && hasButton;
  }
  
  /**
   * 应用红包模板样式
   * 在DOM中添加相关的CSS样式
   */
  static applyTemplateStyles() {
    // 如果已经添加样式，不再重复添加
    if (document.getElementById('red-packet-template-styles')) {
      return;
    }
    
    // 创建样式元素
    const styleEl = document.createElement('style');
    styleEl.id = 'red-packet-template-styles';
    
    // 定义红包模板的CSS样式
    styleEl.innerHTML = `
      .red-packet-template {
        background-color: #dd2931;
        min-height: 460px;
      }
    `;
    
    // 添加样式到文档头部
    document.head.appendChild(styleEl);
  }
  
  /**
   * 移除红包模板样式
   * 清理添加的样式元素
   */
  static removeTemplateStyles() {
    // 移除添加的样式元素
    const styleEl = document.getElementById('red-packet-template-styles');
    if (styleEl) {
      document.head.removeChild(styleEl);
    }
    
    // 重置全局变量
    window.TEMPLATE_IS_REDPACKET = false;
  }
}

export default RedPacketTemplate; 