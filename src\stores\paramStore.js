import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useParamStore = defineStore('param', () => {
  // 保存所有已使用的参数ID
  const usedParamIds = ref(new Set());
  
  // 已删除/可重用的参数ID
  const deletedParamIds = ref(new Set());
  
  // 参数与面板的关联映射
  const paramPanelMap = ref(new Map());
  
  // 面板与参数的关联映射
  const panelParams = ref({});
  
  // 计算属性：获取所有已使用的参数ID数组（排序）
  const allUsedParamIds = computed(() => {
    return Array.from(usedParamIds.value)
      .sort((a, b) => parseInt(a) - parseInt(b));
  });
  
  // 计算属性：获取所有已删除的参数ID数组（排序）
  const allDeletedParamIds = computed(() => {
    return Array.from(deletedParamIds.value)
      .sort((a, b) => parseInt(a) - parseInt(b));
  });
  
  /**
   * 添加一个已使用的参数ID
   * @param {string} paramId - 参数ID
   */
  function addUsedParamId(paramId) {
    if (!paramId) return;
    
    // 添加到已使用集合
    usedParamIds.value.add(paramId.toString());
    
    // 如果在已删除集合中，需要移除
    if (deletedParamIds.value.has(paramId.toString())) {
      deletedParamIds.value.delete(paramId.toString());
    }
  }
  
  /**
   * 标记参数为已删除
   * @param {string} paramId - 参数ID
   */
  function markParamAsDeleted(paramId) {
    if (!paramId) return;
    
    const paramIdStr = paramId.toString();
    
    // 如果参数ID不在已使用集合中，直接返回
    if (!usedParamIds.value.has(paramIdStr)) {
      return;
    }
    
    // 从面板关联中移除
    if (paramPanelMap.value.has(paramIdStr)) {
      const panelId = paramPanelMap.value.get(paramIdStr);
      if (panelId && panelParams.value[panelId]) {
        panelParams.value[panelId] = panelParams.value[panelId].filter(id => id !== paramIdStr);
      }
      paramPanelMap.value.delete(paramIdStr);
    }
    
    // 标记为已删除
    deletedParamIds.value.add(paramIdStr);
  }
  
  /**
   * 将参数关联到面板
   * @param {string} paramId - 参数ID
   * @param {string} panelId - 面板ID
   */
  function associateParamWithPanel(paramId, panelId) {
    if (!paramId || !panelId) return;
    
    const paramIdStr = paramId.toString();
    
    // 更新参数到面板的映射
    paramPanelMap.value.set(paramIdStr, panelId);
    
    // 更新面板到参数的映射
    if (!panelParams.value[panelId]) {
      panelParams.value[panelId] = [];
    }
    
    // 确保不会重复添加
    if (!panelParams.value[panelId].includes(paramIdStr)) {
      panelParams.value[panelId].push(paramIdStr);
    }
    
    // 确保参数被标记为已使用
    addUsedParamId(paramIdStr);
  }
  
  /**
   * 获取下一个可用的参数ID
   * @returns {string} 下一个可用的参数ID
   */
  function getNextParamId() {
    try {
      // 强制清理删除的参数ID，移除已在使用中的ID
      cleanupDeletedParamIds();
      
      // 检查是否正在创建模板
      const isCreatingTemplate = (() => {
        const dialogHeaders = document.querySelectorAll('.dialog-header .dialog-title');
        for (let i = 0; i < dialogHeaders.length; i++) {
          if (dialogHeaders[i].textContent.includes('5G阅信')) {
            return true;
          }
        }
        return false;
      })();
      
      // 在创建模板时，如果这是第一个参数，强制从1开始
      if (isCreatingTemplate) {
        // 查找模板对话框的根元素
        const templateDialogRoot = document.querySelector('.template-dialog-root');
        
        // 检查当前模板中已分配的参数ID
        const currentTemplateParamIds = [];
        if (templateDialogRoot) {
          const paramElements = templateDialogRoot.querySelectorAll('[data-param-id]');
          paramElements.forEach(elem => {
            const paramId = elem.getAttribute('data-param-id');
            if (paramId) {
              currentTemplateParamIds.push(parseInt(paramId));
            }
          });
        }
        
        // 如果当前模板还没有参数且没有已删除的参数，从1开始
        if (currentTemplateParamIds.length === 0 && deletedParamIds.value.size === 0) {
          return '1';
        }
        
        // 如果有已删除的参数ID，优先使用已删除的最小ID
        if (deletedParamIds.value.size > 0) {
          // 将已删除的参数ID转换为数字并排序
          const sortedDeletedIds = Array.from(deletedParamIds.value)
            .map(id => parseInt(id))
            .sort((a, b) => a - b);
          
          if (sortedDeletedIds.length > 0) {
            const smallestDeletedId = sortedDeletedIds[0].toString();
            
            // 从删除集合中移除该ID
            deletedParamIds.value.delete(smallestDeletedId);
            
            // 添加到已使用集合
            usedParamIds.value.add(smallestDeletedId);
            
            return smallestDeletedId;
          }
        }
        
        // 如果没有可用的已删除ID，则使用最大ID+1
        const nextId = (Math.max(...currentTemplateParamIds, 0) + 1).toString();
        return nextId;
      }
      
      // 检查是否存在真实的参数
      const realParamsExist = checkIfRealParamsExist();
      
      // 如果没有找到真实参数，从1开始
      if (!realParamsExist) {
        usedParamIds.value.clear();
        deletedParamIds.value.clear();
        usedParamIds.value.add('1');
        return '1';
      }
      
      // 如果有已删除的参数ID，优先重用
      if (deletedParamIds.value.size > 0) {
        // 找出数值最小的已删除ID
        const sortedDeletedIds = Array.from(deletedParamIds.value)
          .map(id => parseInt(id))
          .sort((a, b) => a - b);
        
        if (sortedDeletedIds.length > 0) {
          const smallestDeletedId = sortedDeletedIds[0].toString();
          
          // 从删除集合中移除该ID
          deletedParamIds.value.delete(smallestDeletedId);
          
          // 添加到已使用集合
          usedParamIds.value.add(smallestDeletedId);
          
          return smallestDeletedId;
        }
      }
      
      // 查找从1开始的最小可用ID
      let nextId = 1;
      while (usedParamIds.value.has(nextId.toString())) {
        nextId++;
      }
      
      const nextParamId = nextId.toString();
      usedParamIds.value.add(nextParamId);
      
      return nextParamId;
    } catch (error) {
      return '1'; // 出错时返回安全值
    }
  }
  
  /**
   * 检查页面中是否存在真实的参数
   * @returns {boolean} 是否存在真实参数
   */
  function checkIfRealParamsExist() {
    try {
      // 检查DOM中是否存在标准格式参数
      const bodyContent = document.body.innerHTML || '';
      const hasStandardFormatParams = /{#param\d+#}/.test(bodyContent);
      
      // 检查是否有带参数ID属性的元素
      const hasParamElements = document.querySelector('[data-param-id]') !== null;
      
      // 检查是否有关联到面板的参数
      const hasPanelParams = paramPanelMap.value.size > 0;
      
      // 如果三个条件都不满足，则认为没有真实参数
      const realParamsExist = hasStandardFormatParams || hasParamElements || hasPanelParams;
      
      return realParamsExist;
    } catch (error) {
      return true; // 出错时默认返回true，保守处理
    }
  }
  
  /**
   * 清理已删除的参数ID集合
   * 确保已删除集合中不包含已使用的ID
   */
  function cleanupDeletedParamIds() {
    const toRemove = [];
    
    // 查找同时存在于已删除和已使用集合的ID
    deletedParamIds.value.forEach(id => {
      if (usedParamIds.value.has(id)) {
        toRemove.push(id);
      }
    });
    
    // 从已删除集合中移除
    if (toRemove.length > 0) {
      toRemove.forEach(id => deletedParamIds.value.delete(id));
    }
  }
  
  /**
   * 重置参数存储
   */
  function resetStore() {
    // 清空已使用参数ID集合
    usedParamIds.value.clear();
    
    // 清空面板参数关联
    paramPanelMap.value.clear();
    panelParams.value = {};
    
    // 不清空已删除参数ID集合，允许重用
  }
  
  /**
   * 从DOM收集所有参数ID
   * @param {Element} [searchRoot=document] - 搜索的根元素，默认为document
   * @returns {Array} 收集到的参数ID数组
   */
  function collectParamsFromDOM(searchRoot = document) {
    const collectedIds = new Set();
    
    try {
      // 查找并排除模板列表区域
      const templateListBox = document.querySelector('.template-box');
      
      // 判断元素是否在搜索范围内，且不在模板列表中
      const isInSearchScope = (element) => {
        if (!element) return false;
        
        // 检查元素是否在搜索范围内
        const isInScope = searchRoot.contains(element);
        
        // 如果找到了模板列表，确保元素不在列表中
        const isInListBox = templateListBox && templateListBox.contains(element);
        
        return isInScope && !isInListBox;
      };
      
      // 1. 收集所有参数按钮元素
      const paramSelectors = [
        '.j-btn[data-param-id]',
        '.param-input[data-param-id]',
        'input.j-btn[value*="{#param"]',
        'input.param-input[value*="{#param"]',
        'button.j-btn[value*="{#param"]',
        '*[data-param-id]'
      ];
      
      // 查找所有参数元素，包括隐藏的，但限制在搜索范围内
      const paramElements = searchRoot.querySelectorAll(paramSelectors.join(', '));
      
      // 处理每个参数元素，确保它在搜索范围内
      paramElements.forEach(element => {
        if (!isInSearchScope(element)) {
          return; // 跳过不在搜索范围内的元素
        }
        
        // 尝试获取参数ID
        let paramId = element.getAttribute('data-param-id');
        
        if (!paramId) {
          // 尝试从值中提取
          const valueText = element.value || element.textContent || '';
          // 只匹配标准格式 {#paramX#}，避免误识别纯文本"参数X"
          const match = valueText.match(/{#param(\d+)#}/);
          if (match && match[1]) {
            paramId = match[1];
            
            // 设置data-param-id属性，方便未来快速访问
            element.setAttribute('data-param-id', paramId);
          }
        }
        
        if (paramId) {
          collectedIds.add(paramId);
          
          // 查找参数所属面板
          const panelId = element.getAttribute('data-owner-panel') ||
                         element.closest('[data-panel-id]')?.getAttribute('data-panel-id') ||
                         element.closest('.setting-panel, .setting-group')?.getAttribute('id');
          
          // 关联参数到面板
          if (panelId) {
            associateParamWithPanel(paramId, panelId);
          }
        }
      });
      
      // 2. 从所有可编辑元素中搜索参数 - 只匹配标准格式
      const editableElements = searchRoot.querySelectorAll('[contenteditable="true"]');
      editableElements.forEach(element => {
        if (!isInSearchScope(element)) {
          return; // 跳过不在搜索范围内的元素
        }
        
        const content = element.innerHTML || '';
        // 只匹配标准格式 {#paramX#}，避免误识别纯文本"参数X"
        const regex = /{#param(\d+)#}/g;
        let match;
        
        while ((match = regex.exec(content)) !== null) {
          if (match[1]) {
            const paramId = match[1];
            collectedIds.add(paramId);
            
            // 查找参数所属面板
            const panelId = element.getAttribute('data-owner-panel') ||
                          element.closest('[data-panel-id]')?.getAttribute('data-panel-id') ||
                          element.closest('.setting-panel, .setting-group')?.getAttribute('id');
            
            // 关联参数到面板
            if (panelId) {
              associateParamWithPanel(paramId, panelId);
            }
          }
        }
      });
      
      // 3. 查找所有直接包含参数格式文本的元素，但限制在搜索范围内
      try {
        // 使用递归遍历节点，查找文本节点
        const searchTextNodes = (node) => {
          if (!node) return;
          
          // 检查节点是否在搜索范围内
          if (!isInSearchScope(node)) return;
          
          if (node.nodeType === Node.TEXT_NODE) {
            const content = node.textContent;
            if (content && content.includes('{#param') && content.includes('#}')) {
              // 只匹配标准格式 {#paramX#}，避免误识别纯文本"参数X"
              const regex = /{#param(\d+)#}/g;
              let match;
              
              while ((match = regex.exec(content)) !== null) {
                if (match[1]) {
                  collectedIds.add(match[1]);
                }
              }
            }
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            // 排除脚本和样式元素
            if (node.tagName === 'SCRIPT' || node.tagName === 'STYLE') return;
            
            // 递归处理子节点
            for (let i = 0; i < node.childNodes.length; i++) {
              searchTextNodes(node.childNodes[i]);
            }
          }
        };
        
        // 从搜索根节点开始递归查找
        searchTextNodes(searchRoot);
      } catch (e) {
      }
      
      // 4. 从全局缓存中查找参数，但仅当搜索整个文档时
      if (searchRoot === document && window.CLICK_EVENT_CACHE) {
        Object.values(window.CLICK_EVENT_CACHE).forEach(data => {
          if (data && data.actionUrl && data.actionUrl.includes('{#param')) {
            // 只匹配标准格式 {#paramX#}，避免误识别纯文本"参数X"
            const regex = /{#param(\d+)#}/g;
            let match;
            
            while ((match = regex.exec(data.actionUrl)) !== null) {
              if (match[1]) {
                collectedIds.add(match[1]);
              }
            }
          }
        });
      }
      
      // 5. 从当前模板内容中查找参数，但仅当搜索整个文档时
      if (searchRoot === document && window.TEMPLATE_CONTENTS && Array.isArray(window.TEMPLATE_CONTENTS)) {
        const regex = /{#param(\d+)#}/g;
        window.TEMPLATE_CONTENTS.forEach(content => {
          // 检查内容的各种属性
          Object.keys(content).forEach(key => {
            const value = content[key];
            if (typeof value === 'string' && value.includes('{#param')) {
              let match;
              while ((match = regex.exec(value)) !== null) {
                if (match[1]) {
                  collectedIds.add(match[1]);
                }
              }
            }
          });
        });
      }
      
      // 6. 将所有收集到的参数添加到已使用集合
      const newParamIds = [];
      collectedIds.forEach(id => {
        if (!usedParamIds.value.has(id)) {
          newParamIds.push(id);
        }
        usedParamIds.value.add(id);
      });
      
      if (newParamIds.length > 0) {
      }
      
      // 7. 移除可能存在的已删除但现在已经在DOM中重新出现的参数ID
      const removeFromDeleted = [];
      deletedParamIds.value.forEach(id => {
        if (collectedIds.has(id)) {
          removeFromDeleted.push(id);
        }
      });
      
      if (removeFromDeleted.length > 0) {
        removeFromDeleted.forEach(id => {
          deletedParamIds.value.delete(id);
        });
      }
      
      // 返回排序后的收集到的参数ID
      return Array.from(collectedIds).sort((a, b) => parseInt(a) - parseInt(b));
    } catch (error) {
      return [];
    }
  }
  
  /**
   * 检测并清理面板参数关联
   * 清除DOM中不存在的面板参数
   */
  function cleanupPanelAssociations() {
    // 先获取当前所有面板元素
    const allPanels = document.querySelectorAll('.setting-panel, .setting-group');
    const activePanelIds = new Set();
    
    // 收集所有存在的面板ID
    allPanels.forEach(panel => {
      const panelId = panel.id || panel.getAttribute('data-panel-id');
      if (panelId) {
        activePanelIds.add(panelId);
      }
    });
    
    // 当前激活的面板ID，如果有
    const activePanelId = document.querySelector('.setting-panel.active')?.id ||
                        document.querySelector('.setting-panel.active')?.getAttribute('data-panel-id');
    
    // 如果找到活动面板，记录它
    if (activePanelId) {
    }
    
    // 检查paramPanelMap中的所有面板关联
    const panelsToRemove = new Set();
    
    // 查找不再存在的面板
    paramPanelMap.value.forEach((panelId, paramId) => {
      if (!activePanelIds.has(panelId)) {
        // 面板已不存在，记录需要移除的关联
        panelsToRemove.add(panelId);
      }
    });
    
    if (panelsToRemove.size > 0) {
      // 对每个不存在的面板，移除与之关联的参数
      panelsToRemove.forEach(panelId => {
        // 查找与该面板关联的所有参数
        const paramsInPanel = [];
        paramPanelMap.value.forEach((pId, paramId) => {
          if (pId === panelId) {
            paramsInPanel.push(paramId);
          }
        });
        
        // 移除这些参数的面板关联
        paramsInPanel.forEach(paramId => {
          paramPanelMap.value.delete(paramId);
        });
        
        // 删除面板的参数集合
        if (panelParams.value[panelId]) {
          delete panelParams.value[panelId];
        }
      });
    }
  }
  
  /**
   * 强制完全重置所有参数
   * 注意：这将清空所有参数ID，包括已删除的
   */
  function forceFullReset() {
    // 清空参数ID集合
    usedParamIds.value.clear();
    deletedParamIds.value.clear();
    paramPanelMap.value.clear();
    panelParams.value = {};
    
    // 强制清除全局缓存
    if (window.GLOBAL_PARAM_MANAGER) {
      try {
        window.GLOBAL_PARAM_MANAGER.usedParamIds.clear();
        window.GLOBAL_PARAM_MANAGER.deletedParamIds.clear();
        window.GLOBAL_PARAM_MANAGER.paramDefinitions.clear();
        window.GLOBAL_PARAM_MANAGER.paramPanelMap.clear();
        window.GLOBAL_PARAM_MANAGER.panelParams = {};
        window.GLOBAL_PARAM_MANAGER.paramGroups = { 'global': new Set() };
      } catch (error) {
      }
    }
    
    // 清除DOM中的参数标记
    try {
      // 查找所有参数元素
      const paramElements = document.querySelectorAll('[data-param-id]');
      
      // 执行DOM清理
      if (paramElements.length > 0) {
        paramElements.forEach(element => {
          // 保留元素但清除其参数属性
          element.removeAttribute('data-param-id');
          element.removeAttribute('data-owner-panel');
        });
      }
      
      // 显式清理内存中的参数表示
      // 查找所有带标准格式参数的元素
      const allElements = document.querySelectorAll('*');
      const elementsByParam = new Map();
      
      // 正则表达式查找所有标准格式参数，如 {#paramX#}
      const paramRegex = /{#param(\d+)#}/g;
      
      allElements.forEach(element => {
        const content = element.innerHTML || '';
        let match;
        
        // 重置正则状态
        paramRegex.lastIndex = 0;
        
        // 在内容中查找所有参数
        while ((match = paramRegex.exec(content)) !== null) {
          if (match[1]) {
            const paramId = match[1];
            
            // 记录包含此参数的元素
            if (!elementsByParam.has(paramId)) {
              elementsByParam.set(paramId, []);
            }
            elementsByParam.get(paramId).push(element);
          }
        }
      });
      
      // 清理所有包含参数的元素内容
      if (elementsByParam.size > 0) {
        elementsByParam.forEach((elements, paramId) => {
        });
      }
    } catch (domError) {
    }
    
    // 立即触发参数变更事件，通知系统重置
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.emit('params-reset');
    }
  }
  
  return {
    usedParamIds,
    deletedParamIds,
    paramPanelMap,
    panelParams,
    allUsedParamIds,
    allDeletedParamIds,
    addUsedParamId,
    markParamAsDeleted,
    associateParamWithPanel,
    getNextParamId,
    resetStore,
    collectParamsFromDOM,
    forceFullReset,
    cleanupPanelAssociations
  };
});
