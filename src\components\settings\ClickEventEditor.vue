<template>
  <div class="click-event-editor">
    <!-- 输入区域 -->
    <div 
      v-if="inputType === 'contenteditable'"
      class="editable-content"
      contenteditable="true"
      spellcheck="false"
      ref="inputRef"
      :placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
    ></div>
    
    <el-input
      v-else-if="inputType === 'input'"
      v-model="inputValue"
      :placeholder="placeholder"
      @focus="handleFocus"
      @blur="handleBlur"
      @change="handleChange"
      ref="inputRef"
    />
    
    <el-input
      v-else-if="inputType === 'textarea'"
      v-model="inputValue"
      type="textarea"
      :placeholder="placeholder"
      :rows="3"
      @focus="handleFocus"
      @blur="handleBlur"
      @change="handleChange"
      ref="inputRef"
    />
    
    <!-- 事件提示信息 -->
    <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>
    
    <!-- 参数操作区 -->
    <div v-if="showParamOperations" class="param-operations">
      <ParamOperations
        :source="componentType"
        :component="componentType"
        :panel-id="panelId"
        @insert-param="handleParamInsert"
        @manage-param="handleManageParams"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import ParamOperations from '@/components/common/ParamOperations.vue';
import { useParamService } from '@/services/ParamService';
import { ElMessage } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  inputType: {
    type: String,
    default: 'contenteditable', // 可选值: input, textarea, contenteditable
    validator: (value) => ['input', 'textarea', 'contenteditable'].includes(value)
  },
  eventType: {
    type: String,
    default: 'OPEN_BROWSER'
  },
  placeholder: {
    type: String,
    default: ''
  },
  componentType: {
    type: String,
    default: 'click-event'
  },
  showInsertButton: {
    type: Boolean,
    default: false
  },
  showParamOperations: {
    type: Boolean,
    default: true
  },
  maxLength: {
    type: Number,
    default: 3000
  }
});

const emit = defineEmits([
  'update:modelValue', 
  'focus', 
  'blur', 
  'change', 
  'param-inserted',
  'manage-param'
]);

// 状态变量
const inputRef = ref(null);
const inputValue = ref(props.modelValue || '');
const errorMessage = ref('');
const focused = ref(false);
const isComposing = ref(false);

// 生成唯一面板ID
const panelId = computed(() => {
  return `click-event-editor-${props.componentType}-${Date.now()}`;
});

// 使用参数服务
const paramService = useParamService();

// 监听输入值的变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== inputValue.value) {
    inputValue.value = newValue;
    
    // 如果是contenteditable模式，需要更新DOM
    if (props.inputType === 'contenteditable' && inputRef.value) {
      updateContentEditableValue(newValue);
    }
  }
});

// 更新contenteditable元素的内容
const updateContentEditableValue = (value) => {
  if (!inputRef.value) return;
  
  const formattedContent = paramService.formatContentWithParams(value, panelId.value);
  
  // 仅在内容不同时更新，避免光标跳动
  if (inputRef.value.innerHTML !== formattedContent) {
    inputRef.value.innerHTML = formattedContent || '';
  }
};

// 验证输入内容
const validateInput = (value) => {
  if (!value) {
    errorMessage.value = '';
    return true;
  }
  
  // 提取纯文本内容（无参数）
  const plainText = paramService.extractTextWithoutParams(value);
  
  // 内容长度验证
  if (plainText.length > props.maxLength) {
    errorMessage.value = `内容长度超过${props.maxLength}个字符`;
    return false;
  }
  
  // 根据事件类型进行验证
  if (props.eventType === 'OPEN_BROWSER' || props.eventType === 'OPEN_URL') {
    // 检查是否为有效URL
    const urlPattern = /^https?:\/\//;
    if (!urlPattern.test(plainText)) {
      errorMessage.value = '链接必须以http://或https://开头';
      return false;
    }
  } else if (props.eventType === 'OPEN_APP') {
    // 检查APP链接格式
    const schemePattern = /^[a-zA-Z0-9.]+:\/\//;
    if (!schemePattern.test(plainText)) {
      errorMessage.value = '链接必须以[scheme]://开头，如mwcm://www';
      return false;
    }
  } else if (props.eventType === 'OPEN_QUICK') {
    // 检查快应用链接格式
    const quickAppPattern = /^hap:\/\/app\//;
    if (!quickAppPattern.test(plainText)) {
      errorMessage.value = '链接必须以hap://app/开头';
      return false;
    }
  }
  
  errorMessage.value = '';
  return true;
};

// 处理contenteditable的输入事件
const handleInput = (event) => {
  if (isComposing.value) return;
  
  const html = event.target.innerHTML;
  const value = paramService.extractTextWithParams(html);
  
  // 更新内部值
  inputValue.value = value;
  
  // 通知外部值更新
  emit('update:modelValue', value);
  emit('change', value);
  
  // 对于URL类型的输入，立即触发参数插入事件，确保数据同步
  if (props.eventType === 'OPEN_BROWSER' || props.eventType === 'OPEN_URL') {
    console.log('URL输入变化，立即发送更新:', value);
    emit('param-inserted', value);
  } else {
    // 对于其他类型，使用普通的参数插入事件
    emit('param-inserted', value);
  }
  
  // 验证输入
  validateInput(value);
};

// 处理输入法开始事件
const handleCompositionStart = () => {
  isComposing.value = true;
};

// 处理输入法结束事件
const handleCompositionEnd = (event) => {
  isComposing.value = false;
  
  // 输入法编辑结束后，手动触发内容更新
  setTimeout(() => {
    if (inputRef.value) {
      handleInput({ target: inputRef.value });
    }
  }, 0);
};

// 处理焦点事件
const handleFocus = (event) => {
  focused.value = true;
  emit('focus', event);
};

// 处理失焦事件
const handleBlur = (event) => {
  focused.value = false;
  
  if (props.inputType === 'contenteditable') {
    const html = event.target.innerHTML;
    const value = paramService.extractTextWithParams(html);
    inputValue.value = value;
    emit('update:modelValue', value);
  }
  
  validateInput(inputValue.value);
  emit('blur', event);
};

// 处理输入变化
const handleChange = (value) => {
  inputValue.value = value;
  emit('update:modelValue', value);
  emit('change', value);
  validateInput(value);
};

// 处理参数插入
const handleParamInsert = async (event) => {
  if (!inputRef.value) return;
  
  try {
    // 获取参数ID
    const paramId = paramService.getNextParamId(panelId.value);
    
    if (!paramId) {
      ElMessage.warning('无法获取参数ID，请稍后重试');
      return;
    }
    
    // contenteditable模式
    if (props.inputType === 'contenteditable') {
      // 确保元素获得焦点
      inputRef.value.focus();
      
      // 检查当前参数数量是否超过限制
      const currentParams = inputRef.value.querySelectorAll('.j-btn, [data-param-id]').length;
      if (currentParams >= 7) {
        ElMessage.warning('每个输入框最多可插入7个参数，当前输入框已达上限');
        return;
      }
      
      // 创建参数按钮HTML
      const paramHtml = `<input type="button" class="j-btn param-input" value="{#param${paramId}#}" data-param-id="${paramId}" data-panel-id="${panelId.value}" readonly="readonly">`;
      
      // 获取选区
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        
        // 检查选区是否在编辑区域内
        if (inputRef.value.contains(range.commonAncestorContainer)) {
          // 创建文档片段
          const fragment = document.createRange().createContextualFragment(paramHtml);
          
          // 在光标位置插入
          range.deleteContents();
          range.insertNode(fragment);
          
          // 添加零宽空格，确保参数后有光标位置
          const zeroWidthSpace = document.createTextNode('\u200B');
          range.collapse(false);
          range.insertNode(zeroWidthSpace);
          
          // 将光标移到参数和零宽空格后面
          range.setStartAfter(zeroWidthSpace);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
          
          // 触发内容更新
          handleInput({ target: inputRef.value });
        } else {
          // 光标不在编辑区域内，添加到末尾
          inputRef.value.focus();
          document.execCommand('insertHTML', false, paramHtml + '\u200B');
          handleInput({ target: inputRef.value });
        }
      } else {
        // 没有选区，添加到末尾
        inputRef.value.focus();
        
        // 创建选区到末尾
        const range = document.createRange();
        range.selectNodeContents(inputRef.value);
        range.collapse(false);
        selection.removeAllRanges();
        selection.addRange(range);
        
        // 插入参数
        document.execCommand('insertHTML', false, paramHtml + '\u200B');
        handleInput({ target: inputRef.value });
      }
    } else {
      // 输入框模式 - 直接在当前光标位置或末尾添加参数
      const paramText = `{#param${paramId}#}`;
      
      if (inputRef.value.$el && inputRef.value.$el.querySelector('input, textarea')) {
        const inputElement = inputRef.value.$el.querySelector('input, textarea');
        const start = inputElement.selectionStart;
        const end = inputElement.selectionEnd;
        
        // 拼接新值
        const beforeText = inputValue.value.substring(0, start);
        const afterText = inputValue.value.substring(end);
        const newValue = beforeText + paramText + afterText;
        
        // 更新值
        inputValue.value = newValue;
        emit('update:modelValue', newValue);
        emit('change', newValue);
        emit('param-inserted', newValue);
        
        // 验证
        validateInput(newValue);
        
        // 更新光标位置
        nextTick(() => {
          inputElement.focus();
          const newPosition = start + paramText.length;
          inputElement.setSelectionRange(newPosition, newPosition);
        });
      } else {
        // 直接添加到末尾
        const newValue = (inputValue.value || '') + paramText;
        inputValue.value = newValue;
        emit('update:modelValue', newValue);
        emit('change', newValue);
        emit('param-inserted', newValue);
        validateInput(newValue);
      }
    }
  } catch (error) {
    console.error('插入参数失败:', error);
    ElMessage.error('插入参数失败');
  }
};

// 管理参数
const handleManageParams = () => {
  emit('manage-param');
};

// 获取当前编辑器中的参数IDs
const getParamIds = () => {
  if (props.inputType === 'contenteditable' && inputRef.value) {
    const paramButtons = inputRef.value.querySelectorAll('.j-btn[data-param-id]');
    const ids = Array.from(paramButtons).map(el => el.getAttribute('data-param-id'));
    return ids;
  }
  
  // 从文本内容中提取参数ID
  const paramMatches = (inputValue.value || '').match(/{#param(\d+)#}/g) || [];
  return paramMatches.map(match => {
    const matches = match.match(/{#param(\d+)#}/);
    return matches ? matches[1] : null;
  }).filter(id => id);
};

// 聚焦到输入框
const focus = () => {
  if (inputRef.value) {
    if (typeof inputRef.value.focus === 'function') {
      inputRef.value.focus();
    } else if (inputRef.value.$el && typeof inputRef.value.focus === 'function') {
      inputRef.value.focus();
    }
  }
};

// 清空输入内容
const clear = () => {
  inputValue.value = '';
  errorMessage.value = '';
  
  // 清空DOM内容
  if (props.inputType === 'contenteditable' && inputRef.value) {
    inputRef.value.innerHTML = '';
  }
  
  emit('update:modelValue', '');
};

// 组件挂载后的初始化
onMounted(() => {
  // 初始化验证
  validateInput(inputValue.value);
  
  // 如果是contenteditable模式并且有初始值，需要格式化显示
  if (props.inputType === 'contenteditable' && props.modelValue && inputRef.value) {
    updateContentEditableValue(props.modelValue);
  }
});

// 暴露方法给父组件
defineExpose({
  focus,
  clear,
  getParamIds,
  insertParam: handleParamInsert
});
</script>

<style lang="scss" scoped>
.click-event-editor {
  display: flex;
  flex-direction: column;
  width: 100%;
  
  .error-message {
    color: #f56c6c;
    font-size: 12px;
    line-height: 1;
    margin-top: 4px;
  }
  
  .param-operations {
    display: flex;
    gap: 8px;
    margin-top: 8px;
  }
  
  .editable-content {
    width: 100%;
    min-height: 60px;
    padding: 8px 11px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    background-color: #fff;
    transition: border-color 0.2s;
    overflow: auto;
    max-height: 120px;
    line-height: 1.5;
    outline: none;
    word-break: break-all;
    font-size: 14px;
    color: #606266;
    
    &:focus {
      border-color: #409eff;
    }
    
    &:empty:before {
      content: attr(placeholder);
      color: #999;
      pointer-events: none;
    }
  }
  
  :deep(input.j-btn), :deep(span.j-btn) {
    border: none !important;
    border-radius: 0 !important;
    color: #dcbb84;
    background: none !important;
    margin: 0 3px;
    font-size: inherit;
    line-height: inherit;
    cursor: default;
    padding: 0;
    display: inline-block;
    vertical-align: baseline;
    height: auto;
    min-height: auto;
    box-shadow: none !important;
  }

  :deep(input.j-btn:hover), 
  :deep(input.j-btn:focus),
  :deep(span.j-btn:hover),
  :deep(span.j-btn:focus) {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
    background: none !important;
  }
}
</style> 