import { getMediaUrl } from '@/utils/mediaUtils';

// 厂商映射表
export const FACTORY_MAP = {
  'HUAWEI': { name: '华为', icon: 'huawei-icon.png' },
  'HONOR': { name: '荣耀', icon: 'honor-icon.png' },
  'XIAOMI': { name: '小米', icon: 'xiaomi-icon.png' },
  'OPPO': { name: 'OPPO', icon: 'oppo-icon.png' },
  'VIVO': { name: 'vivo', icon: 'vivo-icon.png' },
  'SAMSUNG': { name: '三星', icon: 'samsung-icon.png' },
  'ASUS': { name: '华硕', icon: 'asus-icon.png' },
  'COOLPAD': { name: '酷派', icon: 'coolpad-icon.png' }
};

/**
 * 获取厂商图标URL
 * @param {string} code 厂商编码
 * @returns {string} 厂商图标URL
 */
export const getFactoryIcon = (code) => {
  if (!code) return ''; 
  const factory = FACTORY_MAP[code.toUpperCase()];
  if (!factory) return '';
  
  // 使用mediaUtils中的方法获取完整URL
  return getMediaUrl(`/src/assets/images/${factory.icon}`);
};

/**
 * 获取厂商中文名称
 * @param {string} code 厂商编码
 * @returns {string} 厂商中文名称
 */
export const getFactoryName = (code) => {
  if (!code) return ''; 
  const factory = FACTORY_MAP[code.toUpperCase()];
  return factory ? factory.name : '';
};

/**
 * 解析厂商信息
 * @param {Object} template 模板对象
 * @returns {Array} 厂商编码数组
 */
export const parseFactoryInfos = (template) => {
  if (!template) return [];
  
  let factoryInfos = template.channels?.[0]?.factoryinfos || template.factoryInfos;
  
  if (!factoryInfos) return [];
  
  // 处理字符串类型的厂商信息
  if (typeof factoryInfos === 'string') {
    try {
      // 尝试解析为JSON
      const parsed = JSON.parse(factoryInfos);
      if (Array.isArray(parsed)) {
        return parsed.map(item => item.factoryType).filter(Boolean);
      }
    } catch (error) {
      // 非JSON格式字符串，按逗号分隔
      return factoryInfos.split(',').map(code => code.trim()).filter(Boolean);
    }
  }
  
  // 处理数组类型的厂商信息
  if (Array.isArray(factoryInfos)) {
    return factoryInfos.map(item => 
      typeof item === 'object' ? item.factoryType : item
    ).filter(Boolean);
  }
  
  return [];
}; 