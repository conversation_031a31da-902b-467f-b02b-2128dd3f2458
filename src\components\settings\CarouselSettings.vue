<template>
  <div class="carousel-settings">
    <!-- 轮播图数量控制 -->
    <div class="carousel-controls">
      <!-- 减号按钮 -->
      <button 
        class="control-btn minus-btn" 
        :disabled="images.length <= 1"
        @click="removeImage"
      >
        −
      </button>
      
      <!-- 数字按钮组（指示器） -->
      <div class="number-buttons">
        <button
          v-for="(image, index) in images"
          :key="index"
          :class="['number-btn', { active: currentIndex === index }]"
          @click="selectImage(index)"
        >
          {{ index + 1 }}
        </button>
      </div>
      
      <!-- 加号按钮 -->
      <button 
        class="control-btn plus-btn" 
        :disabled="images.length >= 5"
        @click="addDefaultImage"
      >
        +
      </button>
    </div>
    
    <!-- 轮播图预览区域 -->
    <div class="carousel-preview-container">
      <div class="carousel-wrapper">
        <!-- Element Plus 轮播图组件，与CarouselElement保持一致 -->
        <el-carousel
          v-if="images.length > 0"
          :key="carouselKey"
          :height="carouselHeight"
          :autoplay="false"
          :initial-index="currentIndex"
          :indicator-position="images.length > 1 ? 'outside' : 'none'"
          :arrow="images.length > 1 ? 'hover' : 'never'"
          ref="carouselRef"
          @change="handleCarouselChange"
          :loop="images.length > 1"
          trigger="click"
        >
          <el-carousel-item 
            v-for="(image, index) in images" 
            :key="`carousel-${index}-${image.src}-${carouselKey}`"
            @click.stop="selectImageFile"
          >
            <div class="carousel-image-wrapper">
              <img 
                v-if="image && image.src" 
                :src="getMediaUrl(image.src)" 
                :alt="image.alt || `图片${index + 1}`"
                class="carousel-image"
                @error="handleImageError(image, index)"
                @load="handleImageLoad(image, index)"
              />
              <div v-else class="no-image">
                <el-icon><Picture /></el-icon>
                <span>点击选择图片</span>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <!-- 当前图片描述 -->
      <div class="setting-desc" v-if="images[currentIndex] && (images[currentIndex].content || props.content.content)">
        {{ images[currentIndex].content || props.content.content }}
      </div>
      <el-button 
        class="select-btn"
        @click="selectImageFile"
      >
        选择图片
      </el-button>
    </div>

    <!-- 当前选中图片的点击事件设置 -->
    <ClickEventSettings 
      v-if="currentImage"
      :content="currentImageContent"
      @update:content="handleClickEventUpdate"
      @insert-param="handleParamInsert"
      @manage-param="handleParamManage"
    />
  </div>
  
  <!-- 图片选择弹框 -->
  <MediaSelectorDialog 
    v-model="dialogVisible"
    mediaType="image"
    @confirm="handleImageSelect"
    :filter-app-key="appKey"
  />
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick, inject } from 'vue';
import { Picture, Plus } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import ClickEventSettings from './ClickEventSettings.vue';
import { getMediaUrl } from '@/utils/mediaUtils';

// 导入统一的点击事件管理工具
import {
  CLICK_EVENT_TYPES,
  ActionJsonGenerator
} from '@/utils/clickEventManager';

// 辅助函数：生成统一的clickEvent对象
const createDefaultClickEvent = (type = CLICK_EVENT_TYPES.OPEN_BROWSER) => {
  // 创建默认的ClickEventSettings格式对象
  const defaultSettings = {
    actionType: type,
    actionUrl: '',
    actionPath: '',
    packageName: [],
    floorType: '0',
    emailAddress: '',
    emailSubject: '',
    emailBody: '',
    scheduleStartTimeString: '',
    scheduleEndTimeString: '',
    popupTitle: '',
    popupContent: '',
    popupButtonText: '',
    copyType: '1',
    selectedParamId: '',
    fixedContent: ''
  };
  
  // 使用统一的转换方法
  return ActionJsonGenerator.fromClickEventSettings(defaultSettings);
};

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  appKey: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['update:content', 'insert-param', 'manage-param']);

// 响应式数据
const dialogVisible = ref(false);
const currentIndex = ref(0);
const carouselContainer = ref(null);
const carouselRef = ref(null);

// 初始化图片数组
const images = ref([]);

// 注入当前模板信息
const currentTemplate = inject('currentTemplate', null);

// 计算轮播图高度
const carouselHeight = computed(() => {
  let height = '192px'; // 默认高度
  
  if (currentTemplate && currentTemplate.value) {
    const template = currentTemplate.value;
    
    // 根据cardId判断模板类型并设置对应高度
    if (template.cardId === 'com.hbm.carouselQuareImage') {
      // 图片轮播1:1模板
      height = '340px';
      console.log('CarouselSettings - 检测到1:1轮播图模板，设置高度为340px');
    } else if (template.cardId === 'com.hbm.carouselVerticalImage') {
      // 图片轮播48:65模板
      height = '440px';
      console.log('CarouselSettings - 检测到48:65轮播图模板，设置高度为460px');
    } else {
      console.log('CarouselSettings - 使用默认轮播图高度192px');
    }
  }
  
  console.log('CarouselSettings - 轮播图高度:', height);
  return height;
});

// 轮播图唯一标识，用于强制重新渲染（与CarouselElement保持一致）
const carouselKey = computed(() => {
  const imageCount = images.value.length;
  // 只在图片数量变化时更新key，避免过度重新渲染
  const key = `carousel-settings-${imageCount}`;
  console.log('CarouselSettings - 生成轮播图key:', key);
  return key;
});

// 处理轮播图切换（与CarouselElement保持一致）
const handleCarouselChange = (index) => {
  console.log('CarouselSettings - 轮播图切换到:', index);
  currentIndex.value = index;
  
  // 同步更新内容中的currentImageIndex，但不触发完整更新
  // 使用nextTick确保UI更新完成
  nextTick(() => {
    // 仅更新必要的字段，避免触发大量重新渲染
    if (props.content.currentImageIndex !== index) {
      emit('update:content', {
        ...props.content,
        currentImageIndex: index,
        _indexUpdate: Date.now()
      });
    }
  });
};

// 轮播图导航方法
const prevImage = () => {
  if (images.value.length <= 1) return;
  const newIndex = currentIndex.value === 0 ? images.value.length - 1 : currentIndex.value - 1;
  if (carouselRef.value) {
    carouselRef.value.setActiveItem(newIndex);
  }
};

const nextImage = () => {
  if (images.value.length <= 1) return;
  const newIndex = currentIndex.value === images.value.length - 1 ? 0 : currentIndex.value + 1;
  if (carouselRef.value) {
    carouselRef.value.setActiveItem(newIndex);
  }
};

// 初始化数据
const initializeCarouselData = () => {
  console.log('初始化轮播图数据:', props.content);
  
  if (props.content.carouselImages && Array.isArray(props.content.carouselImages) && props.content.carouselImages.length > 0) {
    // 如果已有轮播图数据，使用现有数据
    images.value = [...props.content.carouselImages];
    
    // 从content中同步currentImageIndex
    if (typeof props.content.currentImageIndex === 'number' && 
        props.content.currentImageIndex >= 0 && 
        props.content.currentImageIndex < images.value.length) {
      currentIndex.value = props.content.currentImageIndex;
    } else {
      currentIndex.value = Math.min(currentIndex.value, images.value.length - 1);
    }
    
    console.log('使用现有轮播图数据:', images.value);
    console.log('设置当前索引为:', currentIndex.value);
    
    // 确保每张图片都有完整的点击事件配置和positionNumber
    images.value.forEach((image, index) => {
      if (!image.clickEvent) {
        image.clickEvent = createDefaultClickEvent();
      } else {
        // 确保现有的clickEvent包含所有字段
        const defaultEvent = createDefaultClickEvent();
        image.clickEvent = {
          ...defaultEvent,
          ...image.clickEvent,
          type: image.clickEvent.type || defaultEvent.type
        };
      }
      if (!image.alt) {
        image.alt = `图片${index + 1}`;
      }
      // 确保图片有src
      if (!image.src) {
        image.src = '/aim_files/aim_defult/defaultImg.jpg';
      }
      // 修正positionNumber分配逻辑：第一张图片为1，后续从5开始
      if (typeof image.positionNumber === 'undefined') {
        if (index === 0) {
          image.positionNumber = 1; // 第一张图片为1
        } else {
          image.positionNumber = 4 + index; // 第二张为5，第三张为6，以此类推
        }
      }
    });
    
    // 立即更新内容，确保数据持久化
    updateContent();
    
    // 确保轮播图组件显示正确的索引
    nextTick(() => {
      if (carouselRef.value && currentIndex.value > 0) {
        console.log('CarouselSettings - 初始化时设置轮播图索引:', currentIndex.value);
        carouselRef.value.setActiveItem(currentIndex.value);
      }
    });
    return;
  }
  
  // 如果没有轮播图数据，但有单张图片数据，转换为轮播图格式
  if (props.content.src) {
    const singleImage = { 
      src: props.content.src, 
      alt: '图片1',
      positionNumber: 1, // 第一张图片为1
      clickEvent: createDefaultClickEvent()
    };
    
    images.value = [singleImage];
    currentIndex.value = 0;
    console.log('从单张图片创建轮播图数据:', images.value);
    
    // 立即更新内容，确保数据持久化
    updateContent();
    return;
  }
  
  // 默认至少有一张图片，使用统一的默认图片
  const defaultImage = { 
    src: '/aim_files/aim_defult/defaultImg.jpg', 
    alt: '图片1',
    positionNumber: 1, // 第一张图片为1
    clickEvent: createDefaultClickEvent()
  };
  
  images.value = [defaultImage];
  currentIndex.value = 0;
  console.log('创建默认轮播图数据:', images.value);
  
  // 立即更新内容，确保数据持久化
  updateContent();
};

// 当前选中的图片
const currentImage = computed(() => {
  return images.value[currentIndex.value] || { 
    src: '/aim_files/aim_defult/defaultImg.jpg', 
    alt: '',
    positionNumber: 5,
    clickEvent: createDefaultClickEvent()
  };
});

// 当前选中图片的内容对象（用于点击事件设置组件）
const currentImageContent = computed(() => {
  const image = currentImage.value;
  console.log('CarouselSettings - 计算currentImageContent:', {
    currentIndex: currentIndex.value,
    image: image,
    clickEvent: image.clickEvent
  });

  // 使用统一的ActionJsonGenerator.toClickEventSettings方法进行转换
  const baseContent = ActionJsonGenerator.toClickEventSettings(
    image.clickEvent || { type: 'browser' },
    `carousel-image-${currentIndex.value}`,
    'image'
  );

  // 添加轮播图特有的字段
  const result = {
    ...baseContent,
    src: image.src,
    alt: image.alt,
    positionNumber: image.positionNumber
  };

  console.log('CarouselSettings - currentImageContent结果:', result);
  return result;
});

// 选择图片位置
const selectImage = (index) => {
  console.log('CarouselSettings - 手动选择图片索引:', index);
  currentIndex.value = index;
  
  // 同步轮播图组件的显示
  nextTick(() => {
    if (carouselRef.value) {
      carouselRef.value.setActiveItem(index);
    }
    
    // 更新内容中的索引
    emit('update:content', {
      ...props.content,
      currentImageIndex: index,
      _indexUpdate: Date.now()
    });
  });
};

// 添加图片
const addDefaultImage = () => {
  if (images.value.length >= 5) {
    ElMessage.warning('最多只能添加5张图片');
    return;
  }
  
  const newImage = {
    src: '/aim_files/aim_defult/defaultImg.jpg',
    alt: `图片${images.value.length + 1}`,
    positionNumber: 4 + images.value.length, // 新图片从5开始递增
    clickEvent: createDefaultClickEvent()
  };
  
  images.value.push(newImage);
  const newIndex = images.value.length - 1;
  currentIndex.value = newIndex;
  
  console.log('添加新图片后的数组:', images.value);
  console.log('当前选中索引:', currentIndex.value);
  
  // 立即更新内容
  updateContent();
  
  // 多次尝试确保轮播图切换到新添加的图片
  nextTick(() => {
    if (carouselRef.value) {
      console.log('CarouselSettings - 第一次尝试切换到新图片:', newIndex);
      carouselRef.value.setActiveItem(newIndex);
      
      // 延迟再次尝试，确保组件完全渲染
      setTimeout(() => {
        if (carouselRef.value) {
          console.log('CarouselSettings - 第二次尝试切换到新图片:', newIndex);
          carouselRef.value.setActiveItem(newIndex);
        }
      }, 200);
      
      // 最后一次尝试
      setTimeout(() => {
        if (carouselRef.value) {
          console.log('CarouselSettings - 第三次尝试切换到新图片:', newIndex);
          carouselRef.value.setActiveItem(newIndex);
        }
      }, 500);
    }
  });
  
  ElMessage.success(`已添加第${images.value.length}张图片`);
};

// 删除图片
const removeImage = () => {
  if (images.value.length <= 1) {
    ElMessage.warning('至少需要保留一张图片');
    return;
  }
  
  images.value.splice(currentIndex.value, 1);
  
  // 重新分配positionNumber：第一张图片为1，后续从5开始
  images.value.forEach((image, index) => {
    if (index === 0) {
      image.positionNumber = 1; // 第一张图片为1
    } else {
      image.positionNumber = 4 + index; // 第二张为5，第三张为6，以此类推
    }
  });
  
  // 调整当前选中索引
  if (currentIndex.value >= images.value.length) {
    currentIndex.value = images.value.length - 1;
  }
  
  updateContent();
};

// 选择图片文件
const selectImageFile = () => {
  dialogVisible.value = true;
};

// 处理图片选择
const handleImageSelect = (media) => {
  if (media && media.mediaUrl) {
    const relativePath = `/aim_files/${media.appKey}/${media.path}`;
    
    // 更新当前选中的图片，保持原有的点击事件配置和positionNumber
    images.value[currentIndex.value] = {
      ...images.value[currentIndex.value],
      src: relativePath,
      alt: `图片${currentIndex.value + 1}`
    };
    
    updateContent();
    ElMessage.success('图片选择成功');
  }
};

// 处理点击事件更新
const handleClickEventUpdate = (updatedContent) => {
  console.log('轮播图片点击事件更新:', updatedContent);
  
  // 使用统一的ActionJsonGenerator.fromClickEventSettings方法转换
  const clickEvent = ActionJsonGenerator.fromClickEventSettings(updatedContent);
  
  console.log('CarouselSettings - 转换后的clickEvent:', clickEvent);
  
  // 更新当前选中图片的点击事件
  images.value[currentIndex.value] = {
    ...images.value[currentIndex.value],
    clickEvent
  };
  
  updateContent();
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  emit('insert-param', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('manage-param');
};

// 更新内容到父组件
const updateContent = () => {
  const updatedContent = {
    ...props.content,
    carouselImages: [...images.value],
    isCarousel: true,
    currentImageIndex: currentIndex.value,
    // 保持原有的src字段，用于兼容
    src: images.value[currentIndex.value]?.src || '/aim_files/aim_defult/defaultImg.jpg',
    // 添加时间戳确保数据变化能被检测到
    _updateTime: Date.now()
  };
  
  console.log('更新轮播图内容:', updatedContent);
  console.log('轮播图数组长度:', updatedContent.carouselImages.length);
  console.log('当前选中索引:', updatedContent.currentImageIndex);
  
  // 强制触发响应式更新
  nextTick(() => {
    emit('update:content', updatedContent);
    console.log('CarouselSettings - 已发送内容更新事件');
    
    // 确保轮播图显示正确的索引
    setTimeout(() => {
      if (carouselRef.value) {
        console.log('CarouselSettings - 延迟同步轮播图索引到:', currentIndex.value);
        carouselRef.value.setActiveItem(currentIndex.value);
      }
    }, 100);
  });
};

// 监听props.content变化
watch(() => props.content, (newContent, oldContent) => {
  console.log('CarouselSettings - props.content变化:', {
    新内容: newContent,
    旧内容: oldContent,
    contentId变化: newContent?.contentId !== oldContent?.contentId,
    updateSource: newContent?._updateSource,
    currentImageIndex变化: newContent?.currentImageIndex !== oldContent?.currentImageIndex
  });
  
  // 只有在contentId变化时才重新初始化数据
  if (newContent?.contentId !== oldContent?.contentId) {
    console.log('CarouselSettings - contentId变化，重新初始化数据');
    initializeCarouselData();
  } else if (newContent && oldContent) {
    // 检查currentImageIndex是否变化（重点处理来自CarouselElement的更新）
    if (typeof newContent.currentImageIndex === 'number' && 
        newContent.currentImageIndex !== oldContent.currentImageIndex) {
      
      console.log('CarouselSettings - 外部索引变化:', {
        旧索引: oldContent.currentImageIndex,
        新索引: newContent.currentImageIndex,
        当前本地索引: currentIndex.value,
        updateSource: newContent._updateSource
      });
      
      // 如果是来自CarouselElement的更新，立即同步
      if (newContent._updateSource === 'CarouselElement' || newContent._updateSource === 'CarouselElement-retry') {
        console.log('CarouselSettings - 检测到来自CarouselElement的索引更新，立即同步');
        
        if (newContent.currentImageIndex >= 0 && newContent.currentImageIndex < images.value.length) {
          currentIndex.value = newContent.currentImageIndex;
          
          // 立即同步轮播图显示
          nextTick(() => {
            if (carouselRef.value) {
              console.log('CarouselSettings - 立即同步轮播图显示到索引:', newContent.currentImageIndex);
              carouselRef.value.setActiveItem(newContent.currentImageIndex);
            }
          });
        }
      } else if (newContent.currentImageIndex !== currentIndex.value) {
        // 处理其他来源的索引变化
        console.log('CarouselSettings - 处理其他来源的索引变化');
        
        if (newContent.currentImageIndex >= 0 && newContent.currentImageIndex < images.value.length) {
          currentIndex.value = newContent.currentImageIndex;
          
          // 同步轮播图显示
          nextTick(() => {
            if (carouselRef.value) {
              console.log('CarouselSettings - 同步轮播图显示到索引:', newContent.currentImageIndex);
              carouselRef.value.setActiveItem(newContent.currentImageIndex);
            }
          });
        }
      }
    }
    
    // 检查是否有新的轮播图数据需要同步（仅在数据确实不同且不是用户编辑产生的更新时才同步）
    if (newContent.carouselImages && Array.isArray(newContent.carouselImages)) {
      // 检查是否是用户编辑产生的更新（通过时间戳判断）
      const isUserUpdate = newContent._updateTime && (Date.now() - newContent._updateTime < 1000);
      
      if (!isUserUpdate) {
        // 检查轮播图数据是否真的发生了变化
        const oldImagesStr = JSON.stringify(oldContent.carouselImages || []);
        const newImagesStr = JSON.stringify(newContent.carouselImages);
        
        if (oldImagesStr !== newImagesStr) {
          console.log('CarouselSettings - 外部轮播图数组发生变化，检查是否需要同步');
          // 只有在外部数据变化且与当前本地数据不同时才同步
          const currentImagesStr = JSON.stringify(images.value);
          if (currentImagesStr !== newImagesStr) {
            console.log('CarouselSettings - 外部数据与本地数据不同，但这可能是用户正在编辑，谨慎同步');
            
            // 只有在图片数量变化时才强制同步，其他情况保护用户编辑的数据
            if (newContent.carouselImages.length !== images.value.length) {
              console.log('CarouselSettings - 图片数量变化，执行同步');
              images.value = [...newContent.carouselImages];
              
              // 确保当前索引在有效范围内
              if (currentIndex.value >= images.value.length) {
                currentIndex.value = Math.max(0, images.value.length - 1);
              }
            } else {
              console.log('CarouselSettings - 图片数量未变化，保护用户编辑数据，跳过同步');
            }
          } else {
            console.log('CarouselSettings - 外部数据与本地数据相同，跳过同步');
          }
        }
      } else {
        console.log('CarouselSettings - 检测到用户编辑产生的更新，跳过外部同步');
      }
    }
  }
}, { 
  immediate: true,
  // 移除deep监听，避免循环更新
  deep: false
});

// 保证 images.value 和 props.content.carouselImages 保持同步
watch(
  () => props.content.carouselImages,
  (newVal) => {
    if (Array.isArray(newVal)) {
      images.value = [...newVal];
    }
  },
  { immediate: true, deep: true }
);

// 组件挂载时初始化数据
onMounted(() => {
  console.log('CarouselSettings - 组件挂载，初始化数据');
  initializeCarouselData();
});

// 处理图片加载错误
const handleImageError = (image, index) => {
  console.error(`图片加载错误: 索引 ${index}`, image);
};

// 处理图片加载成功
const handleImageLoad = (image, index) => {
  console.log(`图片加载成功: 索引 ${index}`, image);
};
</script>

<style scoped lang="scss">
.carousel-settings {
  padding: 16px;
  
  .carousel-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 20px;
    
    .control-btn {
      width: 32px;
      height: 32px;
      border: 1px solid #dcdfe6;
      background: #fff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 18px;
      transition: all 0.3s;
      
      &:hover:not(:disabled) {
        border-color: #409eff;
        color: #409eff;
      }
      
      &:disabled {
        background: #f5f7fa;
        color: #c0c4cc;
        cursor: not-allowed;
      }
      
      &.minus-btn {
        color: #606266;
        
        &:hover:not(:disabled) {
          border-color: #409eff;
          color: #409eff;
        }
      }
      
      &.plus-btn {
        color: #606266;
        
        &:hover:not(:disabled) {
          border-color: #409eff;
          color: #409eff;
        }
      }
    }
    
    .number-buttons {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
      
      .number-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #dcdfe6;
        background: #fff;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          color: #409eff;
        }
        
        &.active {
          background: #409eff;
          border-color: #409eff;
          color: #fff;
        }
      }
    }
  }
  
  .carousel-preview-container {
    margin-bottom: 20px;
    
    .carousel-wrapper {
      position: relative;
      width: 100%;
      overflow: hidden;
      
      :deep(.el-carousel) {
        .el-carousel__container {
          height: 192px;
        }
        
        .el-carousel__item {
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
        }
        
        // 指示器样式
        .el-carousel__indicators {
          position: absolute !important;
          bottom: 0px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          z-index: 10 !important;
          
          .el-carousel__indicator {
            margin: 0 4px !important;
            
            .el-carousel__button {
              width: 8px !important;
              height: 8px !important;
              border-radius: 50% !important;
              background-color: rgba(255, 255, 255, 0.6) !important;
              border: 1px solid rgba(255, 255, 255, 0.8) !important;
              opacity: 1 !important;
              transition: all 0.3s ease !important;
            }
            
            &.is-active .el-carousel__button {
              background-color: #409eff !important;
              border-color: #409eff !important;
              transform: scale(1.2) !important;
            }
            
            &:hover .el-carousel__button {
              background-color: rgba(255, 255, 255, 0.8) !important;
            }
          }
        }
        
        // 箭头样式
        .el-carousel__arrow {
          background-color: rgba(0, 0, 0, 0.5) !important;
          color: #fff !important;
          border: none !important;
          width: 32px !important;
          height: 32px !important;
          border-radius: 50% !important;
          
          &:hover {
            background-color: rgba(0, 0, 0, 0.7) !important;
          }
          
          &.el-carousel__arrow--left {
            left: 10px !important;
          }
          
          &.el-carousel__arrow--right {
            right: 10px !important;
          }
        }
      }
    }
    .setting-desc{
      margin-top: 10px;
    }
    .carousel-image-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
      cursor: pointer;
      
      .carousel-image {
        width: 100%;
        height: 100%;
        display: block;
      }
      
      .no-image {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        width: 100%;
        height: 100%;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 8px;
        }
        
        span {
          font-size: 14px;
        }
      }
    }
    
    .select-btn {
      margin-top: 10px;
    }
  }
}
</style>