[{"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100926233510024.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，1", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮1", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_BROWSER", "actionJson": {"target": "https://ww"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100925014010023.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，2", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮2", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_APP", "actionJson": {"target": "https://app", "packageName": ["apk"], "floorType": "0"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100923797710022.png", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，3", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮3", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_URL", "actionJson": {"target": "https://lianjie"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100922626810021.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，4", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。4", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮4", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_QUICK", "actionJson": {"target": "hap://app/"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100921745510020.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，5", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮5", "positionNumber": 4, "isTextTitle": 0, "actionType": "DIAL_PHONE", "actionJson": {"target": "18600001111"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100920182710019.png", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，6", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮6", "positionNumber": 4, "isTextTitle": 0, "actionType": "COPY_PARAMETER", "actionJson": {"target": "复制1"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100918139310018.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，7", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮7", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_SMS", "actionJson": {"target": "13600001111", "body": "短信内容"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100916966310017.jpeg", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，8", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮8", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_EMAIL", "actionJson": {"target": "<EMAIL>", "subject": "邮箱标题", "body": "邮箱正文"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175099249533410050.png", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，9", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮9", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_SCHEDULE", "actionJson": {"target": "日程标题", "description": "日程内容", "beginTime": "2025-07-01 00:00:00", "endTime": "2025-07-25 00:00:00"}}]}, {"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175099244508410049.png", "positionNumber": 1, "isTextTitle": 0, "actionType": "", "actionJson": ""}, {"type": "text", "content": "编辑标题，10", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多显示30个字。编辑文本，最多显示30个字。", "positionNumber": 3, "isTextTitle": 0}, {"type": "button", "content": "编辑按钮10", "positionNumber": 4, "isTextTitle": 0, "actionType": "OPEN_POPUP", "actionJson": {"target": "弹窗标题", "content": "弹窗内容", "textButton": "弹窗按钮", "mode": 0}}]}]