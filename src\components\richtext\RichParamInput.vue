<template>
  <div class="rich-param-input">
    <div 
      ref="editableElement"
      class="editable-content" 
      contenteditable="true"
      :placeholder="placeholder"
      @input="handleContentChange"
      @focus="handleFocus"
      @blur="handleBlur"
      @compositionstart="handleCompositionStart"
      @compositionend="handleCompositionEnd"
      @keydown="handleKeyDown"
      @paste="handlePaste"
    ></div>
    
    <!-- <div class="param-operations" v-if="showParamButtons">
      <el-button type="primary" size="small" @click="handleParamButtonClick">
        插入参数
      </el-button>
      <el-button size="small" @click="$emit('manage-param')">
        管理参数
      </el-button>
    </div> -->
  </div>
</template>

<script setup>
import { enhancedInsertParamToContentEditable, initGlobalParamManager, forceGlobalParamCollectionFromAllComponents, getNextParamIdForGroup, extractTextWithParams } from '@/utils/paramUtils';
import { initParamEventBus } from '@/utils/templateEvents';
import { ElMessage } from 'element-plus';
import { ref, watch, nextTick, onMounted, onBeforeUnmount } from 'vue';

// 定义组件属性
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  maxLength: {
    type: Number,
    default: 3000
  },
  showParamButtons: {
    type: Boolean,
    default: true
  },
  componentType: {
    type: String,
    default: 'default'
  },
  panelId: {
    type: String,
    default: ''
  },
  maxParams: {
    type: Number,
    default: 7
  }
});

// 定义事件
const emit = defineEmits([
  'update:modelValue', 
  'input', 
  'change', 
  'focus', 
  'blur', 
  'manage-param',
  'param-inserted'
]);

// 组件状态
const editableElement = ref(null);
const isComposing = ref(false);      // 输入法编辑状态
const isProcessingInput = ref(false); // 处理输入状态
const shouldUpdateDOM = ref(true);   // 是否应该更新DOM
const isInsertingParam = ref(false); // 是否正在插入参数
const undoStack = ref([]);           // 撤销栈
const redoStack = ref([]);           // 重做栈

// 处理内容变化
const handleContentChange = () => {
  // 如果DOM更新被禁用或正在使用输入法，不处理
  if (!shouldUpdateDOM.value || isComposing.value || isProcessingInput.value) {
    return;
  }
  
  try {
    isProcessingInput.value = true;
    
    if (!editableElement.value) return;
    
    // 提取文本和参数标记
    const extractedContent = extractTextWithParams(editableElement.value.innerHTML);
    
    // 检查内容长度
    if (props.maxLength && extractedContent.length > props.maxLength) {
      // 恢复上一个有效状态
      if (undoStack.value.length > 0) {
        const prevState = undoStack.value[undoStack.value.length - 1];
        editableElement.value.innerHTML = prevState.html;
      }
      
      ElMessage.warning(`内容长度不能超过${props.maxLength}个字符`);
      setTimeout(() => {
        isProcessingInput.value = false;
      }, 10);
      return;
    }
    
    // 保存当前状态到撤销栈
    saveUndoState();
    
    // 触发值更新
    emit('update:modelValue', extractedContent);
    emit('input', extractedContent);
    emit('change', extractedContent);
    
  } catch (error) {
    console.error('处理内容变化时出错:', error);
  } finally {
    setTimeout(() => {
      isProcessingInput.value = false;
    }, 10);
  }
};

// 处理输入法开始
const handleCompositionStart = () => {
  isComposing.value = true;
};

// 处理输入法结束
const handleCompositionEnd = () => {
  isComposing.value = false;
  
  // 在输入法结束后，延迟处理内容变化
  // 这确保中文输入法完成后，能正确处理内容
  setTimeout(() => {
    handleContentChange();
  }, 0);
};

// 处理获得焦点
const handleFocus = (event) => {
  emit('focus', event);
};

// 处理失去焦点
const handleBlur = (event) => {
  emit('blur', event);
  
  // 如果此时在使用中文输入法，不处理
  if (isComposing.value) return;
  
  // 处理内容变化
  handleContentChange();
};

// 处理键盘事件
const handleKeyDown = (event) => {
  // 处理撤销重做快捷键
  if ((event.ctrlKey || event.metaKey) && event.key === 'z' && !event.shiftKey) {
    // 撤销: Ctrl+Z 或 Command+Z
    event.preventDefault();
    undo();
  } else if (((event.ctrlKey || event.metaKey) && event.key === 'z' && event.shiftKey) || 
     ((event.ctrlKey || event.metaKey) && event.key === 'y')) {
    // 重做: Ctrl+Shift+Z 或 Command+Shift+Z 或 Ctrl+Y
    event.preventDefault();
    redo();
  }
};

// 处理粘贴事件
const handlePaste = (event) => {
  event.preventDefault();
  
  // 获取剪贴板文本
  const text = event.clipboardData.getData('text/plain');
  
  // 检查粘贴后长度是否超过限制
  const currentContent = extractTextWithParams(editableElement.value.innerHTML);
  
  if (props.maxLength && (currentContent.length + text.length) > props.maxLength) {
    ElMessage.warning(`粘贴后内容长度将超过${props.maxLength}个字符`);
    return;
  }
  
  // 使用insertText命令插入纯文本
  document.execCommand('insertText', false, text);
};

// 处理参数插入
const insertParam = (paramId) => {
  if (isInsertingParam.value) {
    console.log('已有参数插入操作进行中，忽略当前请求');
    return false;
  }
  
  try {
    isInsertingParam.value = true;
    
    // 检查输入框是否存在
    if (!editableElement.value) {
      console.error('可编辑元素不存在，无法插入参数');
      return false;
    }
    
    // 检查当前参数数量是否超过限制
    const currentParams = editableElement.value.querySelectorAll('.j-btn, [data-param-id]').length;
    if (currentParams >= props.maxParams) {
      ElMessage.warning(`最多只能插入${props.maxParams}个参数`);
      return false;
    }
    
    // 确保元素获取焦点
    editableElement.value.focus();
    
    // 处理参数ID格式
    let formattedParamId = paramId;
    
    // 如果没有传入参数ID或参数ID格式不正确，使用全局参数管理器获取
    if (!formattedParamId || (typeof formattedParamId === 'string' && !formattedParamId.match(/^\d+$/))) {
      // 如果传入的是完整的参数格式，提取ID
      if (typeof paramId === 'string' && paramId.includes('{#param')) {
        const match = paramId.match(/{#param(\d+)#}/);
        if (match && match[1]) {
          formattedParamId = match[1];
        }
      } else {
        // 否则从全局参数管理器获取新ID
        console.log('从全局参数管理器获取新的参数ID');
        formattedParamId = window.GLOBAL_PARAM_MANAGER?.getNextId() || 
                            window.getNextAvailableParamId() || '1';
      }
    }
    
    // 格式化参数文本
    const paramFormatted = `{#param${formattedParamId}#}`;
    
    console.log(`准备插入参数: ${paramFormatted}`);
    
    // 备份当前选区
    const selection = window.getSelection();
    const hasSelection = selection.rangeCount > 0;
    let originalRange = null;
    
    if (hasSelection) {
      originalRange = selection.getRangeAt(0).cloneRange();
    }
    
    // 参数插入成功回调
    const onSuccess = (button, paramId, panelId) => {
      console.log(`参数插入成功: ID=${paramId}, 面板=${panelId}`);
      
      // 确保记录参数使用情况
      if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.recordParamUsage === 'function') {
        window.GLOBAL_PARAM_MANAGER.recordParamUsage(paramId);
      }
      
      // 触发参数插入事件
      emit('param-inserted', {
        paramId,
        panelId,
        button
      });
    };
    
    // 手动在光标位置插入参数
    const insertAtCursor = () => {
      try {
        if (!editableElement.value) return false;
        
        // 获取当前选区
        const sel = window.getSelection();
        
        if (sel.rangeCount === 0) {
          // 没有选区，创建一个新的范围插入到末尾
          const range = document.createRange();
          range.selectNodeContents(editableElement.value);
          range.collapse(false); // 移动到末尾
          sel.removeAllRanges();
          sel.addRange(range);
        }
        
        const range = sel.getRangeAt(0);
        
        // 创建参数按钮
        const paramButton = document.createElement('input');
        paramButton.type = 'button';
        paramButton.className = 'j-btn param-input';
        paramButton.value = paramFormatted;
        paramButton.setAttribute('data-param-id', formattedParamId);
        paramButton.setAttribute('readonly', 'readonly');
        paramButton.setAttribute('unselectable', 'on');
        
        // 插入按钮
        range.deleteContents();
        range.insertNode(paramButton);
        
        // 添加零宽空格，确保光标可以放在按钮后面
        const zeroWidthSpace = document.createTextNode('\u200B');
        const newRange = document.createRange();
        newRange.setStartAfter(paramButton);
        newRange.setEndAfter(paramButton);
        newRange.insertNode(zeroWidthSpace);
        
        // 移动光标到零宽空格后
        newRange.setStartAfter(zeroWidthSpace);
        newRange.setEndAfter(zeroWidthSpace);
        sel.removeAllRanges();
        sel.addRange(newRange);
        
        return true;
      } catch (error) {
        console.error('手动插入参数失败:', error);
        return false;
      }
    };
    
    // 尝试使用增强的插入函数
    let success = false;
    
    if (typeof paramUtils.enhancedInsertParamToContentEditable === 'function') {
      success = paramUtils.enhancedInsertParamToContentEditable(
        editableElement.value, 
        paramFormatted, 
        editableElement.value,
        {
          addZeroWidthSpace: true,
          collectParams: true,
          onSuccess
        }
      );
    }
    
    // 如果增强插入失败，回退到手动插入
    if (!success) {
      console.log('增强插入失败，尝试手动插入');
      success = insertAtCursor();
    }
    
    if (success) {
      // 延迟处理内容变化，确保DOM更新完成
      setTimeout(() => {
        // 保存当前状态到撤销栈
        saveUndoState();
        
        // 处理内容变化
        handleContentChange();
        
        // 确保焦点仍在编辑器中
        editableElement.value.focus();
      }, 50);
      
      console.log('参数插入成功');
      return true;
    } else {
      console.error('参数插入失败');
      
      // 尝试恢复原始选区
      if (originalRange) {
        selection.removeAllRanges();
        selection.addRange(originalRange);
      }
      
      return false;
    }
  } catch (error) {
    console.error('插入参数时出错:', error);
    return false;
  } finally {
    setTimeout(() => {
      isInsertingParam.value = false;
    }, 300);
  }
};

// 处理参数插入按钮点击
const handleParamButtonClick = async () => {
  try {
    // 防止重复处理
    if (isInsertingParam.value) {
      console.log('正在处理参数插入，忽略重复操作');
      return false;
    }
    
    // 获取参数所属面板ID
    let panelId = getPanelId();
    if (!panelId) {
      panelId = generatePanelId();
    }
    
    // 强制从所有组件收集参数，确保全局唯一性
    if (typeof paramUtils.forceGlobalParamCollectionFromAllComponents === 'function') {
      paramUtils.forceGlobalParamCollectionFromAllComponents();
    }
    
    console.log('需要从全局参数管理器获取唯一ID');
    let paramId = null;
    
    // 首先尝试从全局参数管理器获取
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.getNextId === 'function') {
      try {
        paramId = window.GLOBAL_PARAM_MANAGER.getNextId();
        console.log('从全局参数管理器获取参数ID:', paramId);
      } catch (error) {
        console.error('从全局参数管理器获取ID失败:', error);
      }
    }
    
    // 如果全局参数管理器获取失败，使用全局函数
    if (!paramId && typeof window.getNextAvailableParamId === 'function') {
      try {
        paramId = window.getNextAvailableParamId();
        console.log('使用全局函数获取参数ID:', paramId);
      } catch (error) {
        console.error('使用全局函数获取ID失败:', error);
      }
    }
    
    // 如果以上方法都失败，作为最后的手段尝试获取ID
    if (!paramId) {
      try {
        // 尝试使用工具函数获取
        paramId = paramUtils.getNextParamIdForGroup?.(panelId) || '1';
        console.log('使用工具函数获取参数ID:', paramId);
      } catch (error) {
        console.error('使用工具函数获取ID失败:', error);
        paramId = '1'; // 默认ID为1
      }
    }
    
    // 确保有有效的参数ID
    if (!paramId) {
      paramId = '1';
    }
    
    // 插入参数
    return insertParam(paramId);
  } catch (error) {
    console.error('处理参数插入按钮点击时出错:', error);
    return false;
  }
};

// 保存撤销状态
const saveUndoState = () => {
  if (!editableElement.value) return;
  
  // 将当前HTML状态添加到撤销栈
  undoStack.value.push({
    html: editableElement.value.innerHTML,
    content: extractTextWithParams(editableElement.value.innerHTML)
  });
  
  // 如果撤销栈太大，移除最旧的状态
  if (undoStack.value.length > 20) {
    undoStack.value.shift();
  }
  
  // 清空重做栈
  redoStack.value = [];
};

// 撤销操作
const undo = () => {
  if (undoStack.value.length === 0 || !editableElement.value) return;
  
  // 保存当前状态到重做栈
  redoStack.value.push({
    html: editableElement.value.innerHTML,
    content: extractTextWithParams(editableElement.value.innerHTML)
  });
  
  // 获取上一个状态
  const prevState = undoStack.value.pop();
  
  // 应用上一个状态
  if (prevState) {
    // 临时禁用DOM更新
    shouldUpdateDOM.value = false;
    
    // 恢复HTML
    editableElement.value.innerHTML = prevState.html;
    
    // 触发值更新
    emit('update:modelValue', prevState.content);
    emit('input', prevState.content);
    
    // 恢复DOM更新
    shouldUpdateDOM.value = true;
  }
};

// 重做操作
const redo = () => {
  if (redoStack.value.length === 0 || !editableElement.value) return;
  
  // 保存当前状态到撤销栈
  undoStack.value.push({
    html: editableElement.value.innerHTML,
    content: extractTextWithParams(editableElement.value.innerHTML)
  });
  
  // 获取下一个状态
  const nextState = redoStack.value.pop();
  
  // 应用下一个状态
  if (nextState) {
    // 临时禁用DOM更新
    shouldUpdateDOM.value = false;
    
    // 恢复HTML
    editableElement.value.innerHTML = nextState.html;
    
    // 触发值更新
    emit('update:modelValue', nextState.content);
    emit('input', nextState.content);
    
    // 恢复DOM更新
    shouldUpdateDOM.value = true;
  }
};

// 创建格式化内容片段
const createFormattedContentFragment = (content) => {
  // 创建文档片段
  const fragment = document.createDocumentFragment();
  
  if (!content) return fragment;
  
  // 参数正则表达式
  const paramRegex = /({#param\d+#})/g;
  
  // 分割内容
  const parts = content.split(paramRegex);
  
  // 处理每一部分
  for (const part of parts) {
    // 检查是否是参数
    if (paramRegex.test(part)) {
      // 是参数，创建按钮元素
      const paramButton = createParamButton(part);
      fragment.appendChild(paramButton);
    } else if (part) {
      // 是普通文本，创建文本节点
      const textNode = document.createTextNode(part);
      fragment.appendChild(textNode);
    }
  }
  
  return fragment;
};

// 创建参数按钮元素
const createParamButton = (param) => {
  // 创建按钮元素
  const input = document.createElement('input');
  input.type = 'button';
  input.className = 'j-btn param-input';
  input.readOnly = true;
  input.setAttribute('unselectable', 'on');
  
  // 提取参数ID
  let paramId = null;
  const match = param.match(/{#param(\d+)#}/);
  if (match && match[1]) {
    paramId = match[1];
  } else {
    // 尝试从"参数X"格式提取
    const altMatch = param.match(/参数(\d+)/);
    if (altMatch && altMatch[1]) {
      paramId = altMatch[1];
    }
  }
  
  // 设置参数ID和值
  if (paramId) {
    // 设置data-param-id属性
    input.setAttribute('data-param-id', paramId);
    
    // 设置值为标准格式 {#paramX#}
    input.value = `{#param${paramId}#}`;
    
    // 获取面板ID
    const panelId = getPanelId();
    if (panelId) {
      input.setAttribute('data-owner-panel', panelId);
    }
  } else {
    // 未能提取到ID，直接使用原始值
    input.value = param;
  }
  
  // 添加点击删除功能
  input.addEventListener('click', (e) => {
    // Ctrl+点击 或 Command+点击 删除参数
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      e.stopPropagation();
      
      // 获取参数ID
      const paramIdToDelete = e.target.getAttribute('data-param-id');
      if (!paramIdToDelete) return;
      
      try {
        // 使用paramUtils中的markParamAsDeleted函数标记参数为已删除
        const paramUtils = window.app?.config?.globalProperties?.$paramUtils || 
                        (window.paramUtils || {});
        
        if (typeof paramUtils.markParamAsDeleted === 'function') {
          paramUtils.markParamAsDeleted(paramIdToDelete);
          console.log(`参数 ${paramIdToDelete} 已标记为已删除，将优先被重用`);
        } else {
          // 尝试使用全局参数存储
          const paramStore = window.app?._instance?.appContext?.app?.config?.globalProperties?.$pinia?.state?.value?.param;
          if (paramStore && typeof paramStore.markParamAsDeleted === 'function') {
            paramStore.markParamAsDeleted(paramIdToDelete);
            console.log(`使用Pinia参数存储标记参数 ${paramIdToDelete} 为已删除`);
          }
        }
      } catch (err) {
        console.error('标记参数为已删除时出错:', err);
      }
      
      // 从DOM中移除按钮
      if (e.target.parentElement) {
        e.target.parentElement.removeChild(e.target);
        
        // 触发内容变化
        handleContentChange();
      }
    }
  });
  
  return input;
};

// 获取当前面板ID
const getPanelId = () => {
  // 优先使用props中的panelId
  if (props.panelId) {
    return props.panelId;
  }
  
  let panelId = '';
  
  // 1. 从可编辑元素获取
  if (editableElement.value?.hasAttribute('data-owner-panel')) {
    panelId = editableElement.value.getAttribute('data-owner-panel');
  }
  
  // 2. 从最近的面板元素获取
  if (!panelId && editableElement.value) {
    const closestPanel = editableElement.value.closest('[data-panel-id], .setting-panel, .setting-group');
    if (closestPanel) {
      panelId = closestPanel.id || closestPanel.getAttribute('data-panel-id');
    }
  }
  
  return panelId;
};

// 生成新的面板ID
const generatePanelId = () => {
  const panelId = `${props.componentType}-panel-${Date.now()}`;
  
  // 设置面板ID到可编辑元素
  if (editableElement.value) {
    editableElement.value.setAttribute('data-owner-panel', panelId);
  }
  
  return panelId;
};

// 初始化内容
const initContent = () => {
  if (!editableElement.value) return;
  
  // 保存当前光标位置
  let savedSelection = null;
  const isActiveElement = editableElement.value === document.activeElement;
  
  if (isActiveElement) {
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      savedSelection = selection.getRangeAt(0).cloneRange();
    }
  }
  
  // 临时禁用DOM更新
  shouldUpdateDOM.value = false;
  
  try {
    // 清空现有内容
    editableElement.value.innerHTML = '';
    
    // 如果有初始内容，设置内容
    if (props.modelValue) {
      // 创建格式化内容片段
      const fragment = createFormattedContentFragment(props.modelValue);
      editableElement.value.appendChild(fragment);
    }
    
    // 设置面板ID
    const panelId = getPanelId() || generatePanelId();
    editableElement.value.setAttribute('data-owner-panel', panelId);
    
    // 设置组件类型
    editableElement.value.setAttribute('data-component-type', props.componentType);
    
    // 恢复光标位置
    if (isActiveElement && savedSelection) {
      try {
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(savedSelection);
      } catch (error) {
        console.warn('恢复光标位置失败:', error);
        // 如果恢复失败，将光标移动到末尾
        const range = document.createRange();
        range.selectNodeContents(editableElement.value);
        range.collapse(false);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
    
  } catch (error) {
    console.error('初始化内容时出错:', error);
  } finally {
    // 恢复DOM更新
    shouldUpdateDOM.value = true;
  }
};

// 监听值变化
watch(() => props.modelValue, (newValue) => {
  // 如果DOM更新被禁用，不处理
  if (!shouldUpdateDOM.value) return;
  
  // 如果正在处理输入或正在插入参数，不处理外部更新
  if (isProcessingInput.value || isInsertingParam.value) return;
  
  // 如果元素当前处于焦点状态，不处理外部更新（避免输入时闪烁）
  if (editableElement.value === document.activeElement) return;
  
  // 如果可编辑元素还未渲染，等待下一个周期
  if (!editableElement.value) {
    nextTick(() => {
      initContent();
    });
    return;
  }
  
  // 获取当前内容
  const currentContent = extractTextWithParams(editableElement.value.innerHTML);
  
  // 如果内容没有变化，不更新DOM
  if (currentContent === newValue) return;
  
  // 初始化内容
  initContent();
}, { flush: 'post' }); // 使用post flush确保在DOM更新后执行

// 组件挂载时
onMounted(() => {
  // 初始化内容
  nextTick(() => {
    initContent();
  });
  
  // 确保全局参数管理器初始化
  initGlobalParamManager();
  
  // 初始化全局参数事件总线
  const paramEventBus = initParamEventBus();
  
  // 监听参数插入事件
  const unsubscribe = paramEventBus.on('insert-param', (paramId) => {
    console.log(`富文本参数输入框收到参数插入事件: ${paramId}`);
    
    // 只有在当前元素处于活动状态时处理参数插入
    if (document.activeElement === editableElement.value) {
      console.log('富文本输入框处于活动状态，插入参数');
      insertParam(paramId);
    }
  });
});

// 组件卸载前
onBeforeUnmount(() => {
  // 取消事件监听
  if (typeof unsubscribe === 'function') {
    unsubscribe();
  }
  
  // 清理可能的事件监听
  if (editableElement.value) {
    // 可以添加需要清理的事件
  }
});

// 对外暴露方法
defineExpose({
  focus: () => {
    if (editableElement.value) {
      editableElement.value.focus();
    }
  },
  blur: () => {
    if (editableElement.value) {
      editableElement.value.blur();
    }
  },
  clear: () => {
    if (editableElement.value) {
      editableElement.value.innerHTML = '';
      emit('update:modelValue', '');
      emit('input', '');
      emit('change', '');
    }
  },
  insertParam,
  handleParamButtonClick,
  getElement: () => editableElement.value
});
</script>

<style scoped lang="scss">
.rich-param-input {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.editable-content {
  width: 100%;
  // min-height: 60px;
  padding: 8px 11px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: border-color 0.2s;
  margin-bottom: 5px;
  overflow: auto;
  max-height: 200px;
  line-height: 1.5;
  outline: none;
  word-break: break-all;
  font-size: 14px;
  color: #606266;
}

.editable-content:focus {
  border-color: #409eff;
}

.editable-content:empty:before {
  content: attr(placeholder);
  color: #999;
}

.editable-content .param-input,
.editable-content .j-btn {
  display: inline-block;
  background-color: #fff6e5;
  color: #dcbb84;
  border: 1px solid #fce8c1;
  margin: 0 2px;
  padding: 0 5px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.5;
  height: 24px;
  cursor: default;
  vertical-align: middle;
  user-select: none;
  outline: none;
}

.param-operations {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}
</style> 