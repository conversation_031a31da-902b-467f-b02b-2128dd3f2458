/**
 * 标准模板类
 * 处理基础图文模板
 */

import BaseTemplate from './BaseTemplate';

class StandardTemplate extends BaseTemplate {
  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容数组
   */
  static initializeContents(template) {
    // 标准模板不是红包模板
    window.TEMPLATE_IS_REDPACKET = false;
    
    // 如果模板已经有内容，使用基类方法初始化
    if (template && template.pages) {
      const contents = super.initializeContents(template);
      if (contents && contents.length > 0) {
        return contents;
      }
    }
    
    // 如果没有内容，创建默认内容
    return this.createDefaultContents(template);
  }
  
  /**
   * 创建默认模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 默认内容数组
   */
  static createDefaultContents(template) {
    // 创建默认内容项
    return [
      // 背景图
      {
        contentId: 'bg-' + Date.now(),
        type: 'background',
        isBackground: 1,
        src: '',
        content: '',
        positionNumber: 1,
        aimOppoBackground: '', // 显式初始化
      },
      // 标题
      {
        contentId: 'title-' + Date.now(),
        type: 'text',
        isTextTitle: 1,
        content: '编辑标题',
        positionNumber: 2
      },
      // 描述
      {
        contentId: 'desc-' + Date.now(),
        type: 'text',
        isTextTitle: 0,
        content: '编辑描述',
        positionNumber: 3
      },
      // 按钮
      {
        contentId: 'btn-' + Date.now(),
        type: 'button',
        content: '编辑按钮',
        positionNumber: 4,
        clickEvent: {
          type: 'OPEN_BROWSER',
          target: '',
          data: {}
        }
      }
    ];
  }
  
  /**
   * 校验内容是否符合标准模板要求
   * @param {Array} contents 模板内容数组
   * @returns {boolean} 是否通过验证
   */
  static validateContents(contents) {
    // 首先使用基类验证
    if (!super.validateContents(contents)) {
      return false;
    }
    
    // 检查是否有必要的内容项：背景图、标题、按钮
    const hasBackground = contents.some(content => 
      content.type === 'background' && content.isBackground === 1
    );
    
    const hasButton = contents.some(content => 
      content.type === 'button'
    );
    
    return hasBackground && hasButton;
  }
}

export default StandardTemplate; 