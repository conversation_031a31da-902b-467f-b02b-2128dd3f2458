<template>
  <div class="horizontal-swipe-container">
    <div class="swipe-container" ref="swipeContainer">
      <div 
        v-for="(page, pageIndex) in pages" 
        :key="`page-${page.pageIndex}`"
        class="swipe-card"
        :class="{ active: isCardSelected(pageIndex) }"
        @click="handleCardClick(pageIndex, $event)"
      >
        <!-- 渲染页面中的每个内容元素 -->
        <div class="card-content" @click="handleContentAreaClick(pageIndex, $event)">
          <component
            v-for="content in page.contents"
            :key="content.contentId"
            :is="getElementComponent(content.type)"
            :content="content"
            :usage="usage"
            :is-selected="selectedContent && selectedContent.contentId === content.contentId"
            :get-media-url="needsMediaUrl(content.type) ? getMediaUrl : undefined"
            class="content-item"
            @select="handleContentSelect(content, $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
import ImageElement from './ImageElement.vue';
import TextElement from './TextElement.vue';
import ButtonElement from './ButtonElement.vue';

const props = defineProps({
  pages: {
    type: Array,
    default: () => []
  },
  usage: {
    type: String,
    default: 'editor'
  },
  editable: {
    type: Boolean,
    default: true
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  selectedContent: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['select']);

// 当前选中的卡片索引
const selectedCardIndex = ref(null);

// 滚动容器引用
const swipeContainer = ref(null);

// 滚动到指定卡片位置
const scrollToCard = (cardIndex) => {
  console.log('scrollToCard被调用，目标索引:', cardIndex);
  
  nextTick(() => {
    console.log('nextTick中执行滚动逻辑');
    
    if (!swipeContainer.value) {
      console.log('swipeContainer为空，无法滚动');
      return;
    }
    
    const cards = swipeContainer.value.children;
    console.log('找到卡片数量:', cards?.length || 0);
    
    if (!cards || cardIndex >= cards.length) {
      console.log('卡片索引超出范围或没有卡片');
      // 如果卡片还没有渲染完成，延迟重试
      if (cardIndex < props.pages.length) {
        console.log('卡片可能还在渲染中，200ms后重试');
        setTimeout(() => scrollToCard(cardIndex), 200);
      }
      return;
    }
    
    const targetCard = cards[cardIndex];
    if (!targetCard) {
      console.log('目标卡片不存在');
      return;
    }
    
    // 计算滚动位置
    const containerWidth = swipeContainer.value.clientWidth;
    const cardWidth = targetCard.offsetWidth;
    const cardOffsetLeft = targetCard.offsetLeft;
    
    // 计算使卡片居中的滚动位置
    const scrollLeft = cardOffsetLeft - (containerWidth - cardWidth) / 2;
    
    console.log('滚动参数 - 容器宽度:', containerWidth, '卡片宽度:', cardWidth, '卡片偏移:', cardOffsetLeft, '计算滚动位置:', scrollLeft);
    
    // 平滑滚动到目标位置
    swipeContainer.value.scrollTo({
      left: Math.max(0, scrollLeft),
      behavior: 'smooth'
    });
    
    console.log('滚动到卡片位置:', cardIndex, '滚动位置:', Math.max(0, scrollLeft));
  });
};

// 计算属性：判断指定卡片是否被选中
const isCardSelected = (pageIndex) => {
  // 如果选中的是横滑设置，检查当前卡片是否是被选中的卡片
  if (props.selectedContent && 
      (props.selectedContent.type === 'horizontalSwipeSettings' || 
       props.selectedContent.isHorizontalSwipeSettings)) {
    // 如果横滑设置包含了选中的卡片索引信息，则显示对应卡片的选中状态
    const selectedIndex = props.selectedContent.selectedCardIndex ?? selectedCardIndex.value;
    return selectedIndex === pageIndex;
  }
  
  // 如果当前有选中的卡片索引，则只有对应的卡片显示选中状态
  return selectedCardIndex.value === pageIndex;
};

// 根据内容类型获取对应的元素组件
const getElementComponent = (type) => {
  const componentMap = {
    'text': TextElement,
    'image': ImageElement,
    'background': ImageElement,
    'video': ImageElement,
    'button': ButtonElement
  };
  
  return componentMap[type] || TextElement;
};

// 判断组件是否需要getMediaUrl属性
const needsMediaUrl = (type) => {
  return ['image', 'background', 'video'].includes(type);
};

// 处理卡片点击 - 选中整个卡片组而不是单个内容
const handleCardClick = (pageIndex, event) => {
  console.log('横滑卡片被点击，卡片索引:', pageIndex);
  console.log('点击事件目标:', event.target);
  console.log('点击事件当前目标:', event.currentTarget);
  
  if (props.editable) {
    // 检查是否已经选中了相同的卡片
    if (selectedCardIndex.value === pageIndex) {
      console.log('卡片已经被选中，跳过重复处理');
      return;
    }
    
    // 设置当前选中的卡片索引
    selectedCardIndex.value = pageIndex;
    
    // 滚动到选中的卡片
    scrollToCard(pageIndex);
    
    // 创建一个虚拟的横滑设置内容对象 - 确保selectedCardIndex正确
    const horizontalSwipeSettings = {
      type: 'horizontalSwipeSettings',
      contentId: 'horizontalswipe-settings',
      content: '横滑设置',
      isHorizontalSwipeSettings: true,
      selectedCardIndex: pageIndex  // 确保使用正确的pageIndex
    };
    console.log('发出横滑设置选择事件:', horizontalSwipeSettings);
    emit('select', horizontalSwipeSettings);
  }
};

// 处理内容区域点击（空白区域）
const handleContentAreaClick = (pageIndex, event) => {
  console.log('内容区域被点击，卡片索引:', pageIndex);
  console.log('点击的元素:', event.target);
  console.log('点击的当前目标:', event.currentTarget);
  
  // 检查点击的是否是空白区域（card-content本身）或者是空白区域的伪元素
  const isBlankArea = event.target.classList.contains('card-content') || 
                     event.target === event.currentTarget;
  
  console.log('是否点击空白区域:', isBlankArea);
  
  if (isBlankArea) {
    console.log('点击了卡片空白区域，触发卡片选择');
    // 阻止事件继续冒泡，避免重复触发
    event.stopPropagation();
    // 直接调用卡片点击处理
    handleCardClick(pageIndex, event);
  }
  // 如果不是空白区域，让事件正常传播给子组件
};

// 处理内容选择 - 让事件冒泡到卡片层级
const handleContentSelect = (content, event) => {
  console.log('=== HorizontalSwipeElement.handleContentSelect 被调用 ===');
  console.log('内容类型:', content.type);
  console.log('内容ID:', content.contentId);
  console.log('事件对象:', event);
  console.log('当前props.editable:', props.editable);
  
  if (!props.editable) {
    console.log('组件不可编辑，跳过处理');
    return;
  }
  
  // 找出当前内容属于哪个卡片
  let cardIndex = -1;
  for (let i = 0; i < props.pages.length; i++) {
    if (props.pages[i].contents && props.pages[i].contents.some(c => c.contentId === content.contentId)) {
      cardIndex = i;
      break;
    }
  }
  
  console.log('确定的卡片索引:', cardIndex);
  
  if (cardIndex >= 0) {
    // 检查是否已经选中了相同的卡片
    if (selectedCardIndex.value === cardIndex) {
      console.log('卡片已经被选中，跳过重复处理');
      // 仍然需要发出选择事件，因为可能是首次选择横滑设置
      const horizontalSwipeSettings = {
        type: 'horizontalSwipeSettings',
        contentId: 'horizontalswipe-settings',
        content: '横滑设置',
        isHorizontalSwipeSettings: true,
        selectedCardIndex: cardIndex  // 确保使用正确的cardIndex
      };
      
      console.log('发出横滑设置选择事件（无需滚动），卡片索引:', cardIndex);
      emit('select', horizontalSwipeSettings);
      return;
    }
    
    // 设置当前选中的卡片索引
    selectedCardIndex.value = cardIndex;
    console.log('设置本地selectedCardIndex为:', cardIndex);
    
    // 滚动到选中的卡片
    scrollToCard(cardIndex);
    
    // 创建横滑设置对象，包含选中的卡片信息 - 确保selectedCardIndex正确
    const horizontalSwipeSettings = {
      type: 'horizontalSwipeSettings',
      contentId: 'horizontalswipe-settings',
      content: '横滑设置',
      isHorizontalSwipeSettings: true,
      selectedCardIndex: cardIndex  // 确保使用正确的cardIndex
    };
    
    console.log('发出横滑设置选择事件，卡片索引:', cardIndex);
    console.log('横滑设置对象:', horizontalSwipeSettings);
    emit('select', horizontalSwipeSettings);
  } else {
    console.log('未找到对应的卡片，发出原始内容选择事件');
    // 如果找不到对应的卡片，则选择具体的内容
    emit('select', content);
  }
};

// 监听selectedContent变化，同步本地的selectedCardIndex
watch(
  () => props.selectedContent,
  (newContent, oldContent) => {
    console.log('=== HorizontalSwipeElement watch selectedContent ===')
    console.log('新的selectedContent:', newContent)
    console.log('旧的selectedContent:', oldContent)
    console.log('当前本地selectedCardIndex:', selectedCardIndex.value)
    
    if (!newContent) {
      console.log('selectedContent为null，但保持当前选中状态')
      // 不重置selectedCardIndex，保持当前选中状态
      console.log('=== HorizontalSwipeElement watch 结束 ===')
      return
    }
    
    if (newContent.type === 'horizontalSwipeSettings') {
      console.log('横滑设置更新，检查是否需要处理')
      console.log('当前本地selectedCardIndex:', selectedCardIndex.value)
      console.log('newContent.selectedCardIndex:', newContent.selectedCardIndex)
      console.log('newContent.isNewCard:', newContent.isNewCard)
      console.log('newContent.fromSettingsPanel:', newContent.fromSettingsPanel)
      console.log('newContent.fromCardRemoval:', newContent.fromCardRemoval)
      console.log('newContent.timestamp:', newContent.timestamp)
      
      // 检测是否是新增卡片
      if (newContent.isNewCard) {
        console.log('检测到新增卡片，强制同步和滚动')
        selectedCardIndex.value = newContent.selectedCardIndex
        console.log('设置selectedCardIndex为:', newContent.selectedCardIndex)
        // 延迟滚动以确保DOM已更新
        nextTick(() => {
          setTimeout(() => {
            scrollToCard(newContent.selectedCardIndex)
            console.log('新增卡片滚动完成')
          }, 100)
        })
      }
      // 检测是否来自删除卡片操作
      else if (newContent.fromCardRemoval) {
        console.log('检测到来自删除卡片操作，强制同步和滚动')
        selectedCardIndex.value = newContent.selectedCardIndex
        console.log('设置selectedCardIndex为:', newContent.selectedCardIndex)
        // 删除卡片后滚动到新选中的卡片
        nextTick(() => {
          setTimeout(() => {
            scrollToCard(newContent.selectedCardIndex)
            console.log('删除卡片后滚动完成')
          }, 100)
        })
      }
      // 检测是否来自设置面板的选择
      else if (newContent.fromSettingsPanel || newContent.timestamp) {
        console.log('检测到来自设置面板的选择，同步和滚动')
        selectedCardIndex.value = newContent.selectedCardIndex
        console.log('设置selectedCardIndex为:', newContent.selectedCardIndex)
        // 立即滚动到对应卡片
        nextTick(() => {
          scrollToCard(newContent.selectedCardIndex)
          console.log('设置面板选择滚动完成')
        })
      }
      // 从null到横滑设置的情况，需要同步到正确的索引
      else if (!oldContent) {
        console.log('从null到横滑设置，同步到正确的卡片索引')
        if (newContent.selectedCardIndex !== undefined && 
            newContent.selectedCardIndex !== null && 
            newContent.selectedCardIndex < props.pages.length) {
          selectedCardIndex.value = newContent.selectedCardIndex
          console.log('同步selectedCardIndex为:', newContent.selectedCardIndex)
          // 滚动到选中的卡片
          nextTick(() => {
            scrollToCard(newContent.selectedCardIndex)
          })
        }
      } 
      // 横滑设置间的切换，确保索引同步
      else if (oldContent && oldContent.type === 'horizontalSwipeSettings') {
        // 确保设置面板和预览区域的索引一致
        if (newContent.selectedCardIndex !== undefined && 
            newContent.selectedCardIndex !== null && 
            newContent.selectedCardIndex < props.pages.length &&
            newContent.selectedCardIndex !== selectedCardIndex.value) {
          console.log('同步设置面板的卡片索引到预览区域')
          selectedCardIndex.value = newContent.selectedCardIndex
          // 滚动到新卡片
          nextTick(() => {
            scrollToCard(newContent.selectedCardIndex)
          })
        } else {
          console.log('索引已同步，无需更新')
        }
      }
    }
    
    console.log('=== HorizontalSwipeElement watch 结束 ===')
  },
  { immediate: true }
)

// 添加专门处理新增卡片的方法
const handleNewCardAdded = (cardIndex) => {
  console.log('handleNewCardAdded被调用，新卡片索引:', cardIndex)
  
  // 设置选中的卡片索引
  selectedCardIndex.value = cardIndex
  
  // 等待DOM更新后滚动到新卡片
  nextTick(() => {
    // 延迟一下确保新卡片已经渲染完成
    setTimeout(() => {
      console.log('新卡片渲染完成，滚动到位置:', cardIndex)
      scrollToCard(cardIndex)
    }, 200)
  })
}

// 暴露方法给父组件调用
defineExpose({
  handleNewCardAdded,
  scrollToCard
})
</script>

<style scoped lang="scss">
.horizontal-swipe-container {
  width: 100%;
  height: 100%;
  position: relative;
  margin-bottom: 10px;
  :deep(.preview-title),
  :deep(.preview-desc) {
    margin: 0 12px;
    text-indent: 0 !important;
    position: relative;
    
    // 移除企业签名前缀，横滑模板不需要
    &:before {
      display: none !important;
    }
  }
  
  :deep(.preview-title) {
    font-size: 15px;
    font-weight: 600;
    color: #333;
  }
  
  :deep(.preview-desc) {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    height: 40px;
  }
  
  .swipe-container {
    width: 100%;
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 16px;
    padding: 16px 1px 6px;
    margin-bottom: 16px;
  }
  .swipe-card {
    flex-shrink: 0;
    width: 210px;
    min-height: 300px;
    background: #ffffff;
    border-radius: 8px;
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    
    &.active {
      border-color: #409eff;
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    }
  }
  .card-content {
    width: 100%;
    height: 100%;
    min-height: inherit;
    display: flex;
    flex-direction: column;
    padding: 0;
    position: relative;
    
    
    :deep(.preview-image) {
      margin-bottom: 10px;
      pointer-events: auto;
      position: relative;
      z-index: 2;
      img {
        height: 208px !important;
        max-height: 208px;
        object-fit: initial;
      }
      
      &:last-child {
        margin-bottom: 0;
      }
    }
    // 为按钮元素留出空白区域
    :deep(.preview-button) {
      margin-bottom: 16px;
    }
  }
}
</style> 