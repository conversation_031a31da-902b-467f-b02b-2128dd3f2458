<template>
  <div class="standard-template">
    <template v-for="(content, index) in sortedContents" :key="content.contentId || index">
      <ImageElement 
        v-if="content.type === 'image'"
        :content="content"
        :is-selected="content === selectedContent"
        :get-media-url="getMediaUrl"
        :usage="usage"
        @select="handleContentSelect"
        @imageClick="handleImageClick"
        @update:content="updateContent"
      />
      
      <ImageElement 
        v-else-if="content.type === 'video'"
        :content="content"
        :is-selected="content === selectedContent"
        :get-media-url="getMediaUrl"
        :usage="usage"
        @select="handleContentSelect"
        @imageClick="handleImageClick"
        @update:content="updateContent"
      />
      
      <TextElement 
        v-else-if="content.type === 'text' && content.isTextTitle === 1"
        :content="content"
        :is-selected="content === selectedContent"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="updateContent"
      />
      
      <TextElement 
        v-else-if="content.type === 'text' && content.isTextTitle === 0 && 
                 !(isRedPacket && content.positionNumber === 2)"
        :content="content"
        :is-selected="content === selectedContent"
        :editable="editable"
        :usage="usage"
        @select="handleContentSelect"
        @update:content="updateContent"
      />
      
      <!-- 按钮 -->
      <ButtonElement 
        v-else-if="content.type === 'button'"
        :content="content"
        :is-selected="content === selectedContent"
        @select="handleContentSelect"
      />
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import ImageElement from '../elements/ImageElement.vue';
import TextElement from '../elements/TextElement.vue';
import ButtonElement from '../elements/ButtonElement.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 判断是否为红包模板
const isRedPacket = computed(() => {
  return window.TEMPLATE_IS_REDPACKET === true;
});

// 按positionNumber排序的内容
const sortedContents = computed(() => {
  if (!props.contents || !Array.isArray(props.contents)) {
    return [];
  }
  
  // 按positionNumber排序，确保内容按正确顺序显示
  const sorted = [...props.contents].sort((a, b) => {
    const posA = a.positionNumber || 0;
    const posB = b.positionNumber || 0;
    return posA - posB;
  });
  
  console.log('StandardTemplateRenderer - 按positionNumber排序后的内容:', sorted);
  return sorted;
});

// 处理内容选择
const handleContentSelect = (content) => {
  if (!props.editable) return;
  
  emit('select-content', content);
};

// 处理内容更新
const updateContent = (newContent) => {
  emit('update:content', newContent);
};

// 处理图片点击
const handleImageClick = (data) => {
  console.log('StandardTemplateRenderer收到图片点击事件:', data);
  
  // 可以在这里处理轮播图中单张图片的特殊逻辑
  // 比如显示图片详情、设置链接等
  
  // 目前先选中整个轮播图内容
  if (props.editable && data.content) {
    emit('select-content', data.content);
  }
};
</script>

<style scoped lang="scss">
.standard-template {
  width: 100%;
  min-height: 364px;
  position: relative;
}
</style> 