<template>
  <div class="template-list">
    <el-row>
      <!-- 创建新模板按钮 -->
      <el-col :span="6" v-if="showCreateButton">
        <div class="grid-content create-template-card" @click="handleCreateTemplate">
          <div class="create-template">
            <el-icon class="icon-plus"><Plus /></el-icon>
            <p>创建新模板</p>
          </div>
        </div>
      </el-col>
      
      <!-- 模板卡片列表 -->
      <el-col :span="6" v-for="template in templates" :key="`${template.templateId}-${templateListVersion}`">
        <TemplateCard 
          :template="template"
          @edit="handleEdit"
          @delete="handleDelete"
          @select="handleSelect"
          @generate-short-link="handleGenerateShortLink"
          @view-details="handleViewDetails"
          @send-template="handleSendTemplate"
          @switch-change="handleSwitchChange"
        />
      </el-col>
      
      <!-- 空状态提示 -->
      <div v-if="templates.length === 0 && !loading" class="empty-placeholder">
        暂无相关模板
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-placeholder">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>加载中...</p>
      </div>
    </el-row>
    
    <!-- 分页器 -->
    <div v-if="showPagination && total > 0" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        :page-sizes="[10, 20, 30, 50]"
        background
        layout="total, prev, pager, next, jumper"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import TemplateCard from './template/list/TemplateCard.vue';

const props = defineProps({
  templates: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  showCreateButton: {
    type: Boolean,
    default: true
  },
  showPagination: {
    type: Boolean,
    default: false
  },
  total: {
    type: Number,
    default: 0
  },
  page: {
    type: Number,
    default: 1
  },
  pageSize: {
    type: Number,
    default: 10
  }
});

const emit = defineEmits([
  'create', 
  'edit', 
  'delete', 
  'select', 
  'page-change', 
  'size-change',
  'generate-short-link',
  'view-details',
  'send-template',
  'switch-change',
  'refresh-list'
]);

// 本地模板数据
const localTemplates = ref([]);

// 添加版本号，用于强制组件重新挂载
const templateListVersion = ref(0);

// 监听props.templates的变化
watch(() => props.templates, (newTemplates) => {
  console.log('TemplateList: 接收到新的模板数据:', newTemplates?.length);
  if (newTemplates && Array.isArray(newTemplates)) {
    // 深拷贝模板数据
    const templates = JSON.parse(JSON.stringify(newTemplates));
    
    // 处理每个模板的pages数据
    templates.forEach(template => {
      if (template.pages) {
        try {
          let pages = template.pages;
          if (typeof pages === 'string') {
            pages = JSON.parse(pages);
          }
          template.pages = pages;
        } catch (e) {
          console.error('解析模板pages数据失败:', e);
        }
      }
    });
    
    // 更新本地数据
    localTemplates.value = templates;
    // 增加版本号，强制组件重新挂载
    templateListVersion.value++;
  } else {
    localTemplates.value = [];
    templateListVersion.value++;
  }
}, { deep: true, immediate: true });

// 计算属性：返回要显示的模板列表
const templates = computed(() => {
  return localTemplates.value;
});

// 分页
const currentPage = ref(props.page || 1);

// 监听 props.page 的变化，确保分页组件和父组件同步
watch(() => props.page, (newPage) => {
  console.log('TemplateList: 父组件页码更新为:', newPage);
  if (newPage !== currentPage.value) {
    currentPage.value = newPage;
  }
}, { immediate: true });

// 处理创建新模板
const handleCreateTemplate = () => {
  emit('create');
};

// 处理编辑模板
const handleEdit = (template) => {
  // console.log('编辑模板:', template);
  emit('edit', template);
};

// 处理删除模板
const handleDelete = (template) => {
  emit('delete', template);
};

// 处理选择模板
const handleSelect = (template) => {
  emit('select', template);
};

// 处理生成短链
const handleGenerateShortLink = (template) => {
  emit('generate-short-link', template);
};

// 处理查看详情
const handleViewDetails = (template) => {
  emit('view-details', template);
};

// 处理发送模板
const handleSendTemplate = (template) => {
  emit('send-template', template);
};

// 处理启用/禁用切换
const handleSwitchChange = (template, value) => {
  emit('switch-change', template, value);
};

// 处理页码改变
const handleCurrentChange = (page) => {
  currentPage.value = page;
  emit('page-change', page);
};

// 处理页大小改变
const handleSizeChange = (size) => {
  emit('size-change', size);
};

// 处理模板更新
const handleTemplateUpdate = (event) => {
  const updatedTemplate = event.detail;
  console.log('TemplateList: 接收到模板更新事件:', updatedTemplate);
  
  if (updatedTemplate && updatedTemplate.templateId) {
    // 更新本地数据
    const index = localTemplates.value.findIndex(t => t.templateId === updatedTemplate.templateId);
    console.log('TemplateList: 找到模板索引:', index);
    
    if (index !== -1) {
      // 深度合并更新，确保模板名称和所有字段都正确更新
      const mergedTemplate = {
        ...localTemplates.value[index],
        ...updatedTemplate,
        templateName: updatedTemplate.templateName || localTemplates.value[index].templateName, // 确保模板名称更新
      };
      
      // 确保pages数据正确更新
      if (updatedTemplate.pages) {
        try {
          let newPages = updatedTemplate.pages;
          if (typeof newPages === 'string') {
            newPages = JSON.parse(newPages);
          }
          
          // 确保每个页面的内容都被正确解析
          if (Array.isArray(newPages)) {
            newPages = newPages.map(page => {
              if (typeof page === 'string') {
                try {
                  return JSON.parse(page);
                } catch (e) {
                  return page;
                }
              }
              
              // 如果是对象，确保点击事件数据正确
              if (page && typeof page === 'object') {
                if (page.actionJson) {
                  try {
                    if (typeof page.actionJson === 'string') {
                      page.actionJson = JSON.parse(page.actionJson);
                    }
                  } catch (e) {
                    console.error('解析actionJson失败:', e);
                  }
                }
              }
              return page;
            });
          }
          
          mergedTemplate.pages = newPages;
        } catch (e) {
          console.error('解析pages数据失败:', e);
          mergedTemplate.pages = updatedTemplate.pages;
        }
      }
      
      // 更新模板数据
      localTemplates.value[index] = mergedTemplate;
      
      // 强制更新视图
      nextTick(() => {
        // 创建一个新的数组引用来触发视图更新
        localTemplates.value = [...localTemplates.value];
        
        // 通知父组件刷新列表
        emit('refresh-list');
      });
    } else {
      console.warn('TemplateList: 未找到要更新的模板:', updatedTemplate.templateId);
    }
  }
};

// 在组件挂载时添加事件监听
onMounted(() => {
  // 监听模板更新事件
  window.addEventListener('template-updated', handleTemplateUpdate);
  
  // 如果有全局事件总线，也监听事件总线上的事件
  if (window.TEMPLATE_EVENT_BUS) {
    window.TEMPLATE_EVENT_BUS.on('template-updated', handleTemplateUpdate);
  }
});

// 在组件卸载时移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('template-updated', handleTemplateUpdate);
  
  if (window.TEMPLATE_EVENT_BUS) {
    window.TEMPLATE_EVENT_BUS.off('template-updated', handleTemplateUpdate);
  }
});

</script>

<style scoped lang="scss">
.template-list {
  padding: 20px 0;
  
  .create-template-card {
    width: 244px;
    height: 346px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transform: translateX(-50%);
    left: 50%;
  
    .create-template {
      text-align: center;
      
      .icon-plus {
        font-size: 42px;
        color: #afafaf;
        margin-bottom: 10px;
      }
      
      p {
        color: #999;
        font-size: 18px;
      }
    }
  }
  
  .empty-placeholder, .loading-placeholder {
    // width: 100%;
    height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 16px;
    flex: 1;
    .is-loading {
      font-size: 30px;
      margin-bottom: 15px;
      color: #409EFF;
    }
  }
  
  .pagination-container {
    margin-top: 30px;
    display: flex;
    justify-content: center;
  }
}
</style> 