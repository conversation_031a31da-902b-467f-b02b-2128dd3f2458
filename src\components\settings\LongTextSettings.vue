<template>
<div class="longtext-settings">
  <div class="setting-group">
    <h4>样式选择</h4>
    <div class="style-options">
      <div 
        v-for="style in styleOptions" 
        :key="style.id"
        class="style-option"
        :class="{ 'is-selected': selectedStyle === style.id }"
        @click="handleStyleChange(style.id)"
      >
        <img 
          :src="getStyleImage(style.image)" 
          :alt="style.name"
          class="style-image"
        />
        <span class="style-name">{{ style.name }}</span>
      </div>
    </div>
  </div>
</div>
</template>

<script setup>
import { ref, watch, inject, onMounted, onUnmounted } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';

const props = defineProps({
  content: {
    type: Object,
    default: null
  },
  settings: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:content', 'change', 'settings-change']);

// 注入长文本设置
const longTextSettings = inject('longTextSettings', null);

// 样式选项配置 - 只保留简单和一般
const styleOptions = ref([
  {
    id: 'simple',
    name: '简单',
    image: '/aim_files/aim_defult/simple.png'
  },
  {
    id: 'general',
    name: '一般',
    image: '/aim_files/aim_defult/general.png'
  }
]);

// 选中的样式
const selectedStyle = ref('simple');

// 监听注入的设置变化
watch(() => longTextSettings?.selectedStyle, (newValue) => {
  console.log('LongTextSettings - 监听到设置变化:', newValue, '当前值:', selectedStyle.value);
  if (newValue !== undefined && newValue !== selectedStyle.value) {
    selectedStyle.value = newValue;
    console.log('LongTextSettings - 更新本地状态为:', selectedStyle.value);
  }
}, { immediate: true, deep: true });

// 监听本地状态变化
watch(selectedStyle, (newValue, oldValue) => {
  console.log('LongTextSettings - selectedStyle变化:', oldValue, '->', newValue);
  
  // 如果本地状态变化且与注入的设置不一致，则同步到注入的设置
  if (longTextSettings && longTextSettings.selectedStyle !== newValue) {
    longTextSettings.selectedStyle = newValue;
    console.log('LongTextSettings - 同步到注入的设置:', newValue);
  }
});

// 监听全局事件总线的重置事件
onMounted(() => {
  // 监听全局重置事件
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.on('longtext-settings-reset', (data) => {
      console.log('LongTextSettings - 收到重置事件:', data);
      if (data.selectedStyle !== undefined && data.selectedStyle !== selectedStyle.value) {
        selectedStyle.value = data.selectedStyle;
        console.log('LongTextSettings - 通过事件重置为:', data.selectedStyle);
      }
    });
  }
});

// 组件销毁时清理事件监听
onUnmounted(() => {
  if (window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS.off('longtext-settings-reset');
  }
});

// 获取样式图片路径
const getStyleImage = (imagePath) => {
  try {
    // 使用服务器上的图片路径
    return getMediaUrl(imagePath);
  } catch (error) {
    // 返回一个简单的占位符数据URL
    return '/aim_files/aim_defult/simple.png';
  }
};

// 处理样式变化
const handleStyleChange = (styleId) => {
  console.log('LongTextSettings - 样式变化:', styleId);
  
  selectedStyle.value = styleId;
  
  // 发出变化事件
  emit('change', {
    selectedStyle: styleId
  });
  
  emit('settings-change', {
    selectedStyle: styleId
  });
  
  // 如果切换到一般样式，通知需要确保有两个按钮
  if (styleId === 'general') {
    console.log('LongTextSettings - 切换到一般样式，通知需要添加第二个按钮');
    // 通过事件总线通知需要添加按钮
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.emit('longtext-ensure-buttons', {
        selectedStyle: styleId,
        requiredButtons: 2,
        secondButtonPosition: 5 // 指定第二个按钮的位置
      });
    }
  }
};

// 组件挂载时初始化
onMounted(() => {
  console.log('LongTextSettings - 组件挂载，长文本设置:', longTextSettings);
  
  // 从注入的设置中获取初始值
  if (longTextSettings?.selectedStyle !== undefined) {
    selectedStyle.value = longTextSettings.selectedStyle;
  } else {
    // 如果没有设置，则使用默认值simple
    selectedStyle.value = 'simple';
    if (longTextSettings) {
      longTextSettings.selectedStyle = 'simple';
    }
  }
});

// 暴露方法给父组件
defineExpose({
  selectedStyle,
  handleStyleChange
});
</script>

<style scoped lang="scss">

.longtext-settings {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin: 14px
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.setting-group h4 {
  margin: 10px 0 4px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.style-options {
  display: flex;
  gap: 14px;
  flex-wrap: wrap;
}

.style-option .style-image{
  cursor: pointer;
  transition: all 0.2s ease;
}

.style-option.is-selected .style-image {
  border: 1px solid #1989fa;
}

.style-image {
  width: 101px;
  display: block;
}

.style-name {
  font-size: 14px;
  color: #666;
  text-align: center;
  display: block;
  margin-top: 10px;
}
</style> 