<template>
  <div class="carousel-element" @click.stop="handleCarouselClick">
    <!-- 轮播图容器 -->
    <div class="carousel-container">
      <el-carousel
        v-if="carouselImages.length > 0"
        :key="carouselKey"
        :height="carouselHeight"
        :autoplay="shouldAutoplay"
        :interval="autoplayInterval"
        :initial-index="currentImageIndex"
        :indicator-position="carouselImages.length > 1 ? 'outside' : 'none'"
        :arrow="carouselImages.length > 1 ? 'hover' : 'never'"
        ref="carouselRef"
        @change="handleCarouselChange"
        :loop="carouselImages.length > 1"
        trigger="click"
      >
        <el-carousel-item 
          v-for="(image, index) in carouselImages" 
          :key="`carousel-${index}-${image.src}-${carouselKey}`"
          @click.stop="handleImageClick(image, index)"
        >
          <div class="carousel-image-wrapper">
            <img 
              :src="getImageSrc(image)" 
              :alt="`轮播图片${index + 1}`"
              class="carousel-image"
            />
       
          </div>
        </el-carousel-item>
      </el-carousel>
      
      <!-- 无图片时显示接口提供的默认图片 -->
      <div v-else class="no-images">
        <img 
          :src="getMediaUrl(props.content.src || '/aim_files/aim_defult/defaultImg48_65.jpg')" 
          alt="默认图片"
          class="default-image"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick, inject } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import templateFactory from '@/factories/TemplateFactory.js';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor', // 'editor' | 'list'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  },
  customClass: {
    type: [String, Array],
    default: ''
  }
});

const emit = defineEmits(['select', 'imageClick', 'update:content']);

console.log('CarouselElement - 组件被加载，接收到的内容:', props.content);

// 获取轮播图引用
const carouselRef = ref(null);

// 注入当前模板信息 - 移到setup顶层
const currentTemplate = inject('currentTemplate', null);

// 轮播图片数组
const carouselImages = computed(() => {
  console.log('CarouselElement - 处理轮播图数据:', {
    content: props.content,
    contentType: props.content?.type,
    carouselImages: props.content?.carouselImages,
    src: props.content?.src
  });
  
  // 处理轮播图数据
  if (props.content.carouselImages && Array.isArray(props.content.carouselImages)) {
    // 过滤掉无效的图片
    const validImages = props.content.carouselImages.filter(img => img && (img.src || img.content));
    
    if (validImages.length > 0) {
      // 处理轮播图数据，确保每张图片都有有效的src
      const processedImages = validImages.map((img, index) => ({
        ...img,
        src: img.src || img.content || '/aim_files/aim_defult/defaultImg48_65.jpg',
        alt: img.alt || `图片${index + 1}`
      }));
      
      console.log('CarouselElement - 使用carouselImages字段:', processedImages);
      return processedImages;
    }
  }
  
  // 如果没有轮播图数据，但有普通图片src或content，创建单张轮播图
  if (props.content.src || props.content.content) {
    const singleImage = [{
      src: props.content.src || props.content.content || '/aim_files/aim_defult/defaultImg48_65.jpg',
      alt: '图片1',
      clickEvent: {
        type: 'OPEN_BROWSER',
        url: '',
        phone: '',
        text: ''
      }
    }];
    
    console.log('CarouselElement - 使用单张图片:', singleImage);
    return singleImage;
  }
  
  console.log('CarouselElement - 没有可用的图片数据');
  return [];
});

// 当前显示的图片索引
const currentImageIndex = computed(() => {
  const index = props.content.currentImageIndex || 0;
  return index;
});

// 轮播图高度
const carouselHeight = computed(() => {
  let height = '192px'; // 默认高度
  
  if (currentTemplate && currentTemplate.value) {
    const template = currentTemplate.value;
    
    // 根据cardId判断模板类型并设置对应高度
    if (template.cardId === 'com.hbm.carouselQuareImage') {
      // 图片轮播1:1模板
      height = '340px';
    } else if (template.cardId === 'com.hbm.carouselVerticalImage') {
      // 图片轮播48:65模板
      height = '440px';
    } else {
      height = '192px';
    }
  }
  
  return height;
});

// 轮播图唯一标识，用于强制重新渲染
const carouselKey = computed(() => {
  const imageCount = carouselImages.value.length;
  const imageSrcs = carouselImages.value.map(img => img.src).join('|');
  // 移除时间戳，避免过度重新渲染
  const key = `carousel-${imageCount}-${imageSrcs.slice(0, 50).replace(/[^a-zA-Z0-9]/g, '')}`;
  return key;
});

// 获取图片源
const getImageSrc = (image) => {
  if (!image || !image.src) {
    return getMediaUrl('/aim_files/aim_defult/defaultImg48_65.jpg');
  }
  return getMediaUrl(image.src);
};

// 处理轮播图整体点击
const handleCarouselClick = () => {
  emit('select', props.content);
};

// 处理单张图片点击
const handleImageClick = (image, index) => {
  console.log('点击了轮播图片:', index + 1, image);
  // 无论是否为预览模式，都发送图片点击事件供组件处理
  emit('imageClick', { image, index, content: props.content });
};

// 处理轮播图切换
const handleCarouselChange = (index) => {
  
  // 立即更新内容中的currentImageIndex，确保与设置区域同步
  const updatedContent = {
    ...props.content,
    currentImageIndex: index,
    _carouselChange: Date.now(), // 添加时间戳标记这是轮播图切换触发的更新
    _updateSource: 'CarouselElement' // 标记更新来源
  };
  
  // 立即通过事件通知父组件更新，确保设置区域能够同步
  emit('update:content', updatedContent);
  
  
  // 添加额外的延迟确保，防止某些情况下的同步失败
  nextTick(() => {
    setTimeout(() => {
      // 再次确认同步
      if (props.content.currentImageIndex !== index) {
        emit('update:content', {
          ...props.content,
          currentImageIndex: index,
          _carouselChange: Date.now(),
          _updateSource: 'CarouselElement-retry'
        });
      }
    }, 50);
  });
};

// 监听currentImageIndex变化，同步轮播图显示
watch(() => props.content.currentImageIndex, (newIndex, oldIndex) => {
  if (carouselRef.value && typeof newIndex === 'number' && newIndex >= 0 && newIndex < carouselImages.value.length) {
    nextTick(() => {
      carouselRef.value.setActiveItem(newIndex);
    });
  }
}, { immediate: true });

// 监听轮播图数组变化，确保索引正确
watch(() => carouselImages.value, (newImages) => {
  if (newImages.length > 0 && carouselRef.value) {
    // 更准确地获取当前索引，避免不必要的重置为0
    const currentIndex = typeof props.content.currentImageIndex === 'number' 
      ? props.content.currentImageIndex 
      : 0;
    
    if (currentIndex >= 0 && currentIndex < newImages.length) {
      nextTick(() => {
        try {
          carouselRef.value.setActiveItem(currentIndex);
        } catch (error) {
        }
      });
    }
  }
}, { immediate: true });

// 监听内容变化，强制重新渲染轮播图
watch(() => props.content, (newContent, oldContent) => {
  
  // 当轮播图数据变化时，强制重新渲染轮播图组件
  if (newContent && newContent.carouselImages) {
    nextTick(() => {
      if (carouselRef.value) {
        // 如果有多张图片，确保指示器显示
        if (newContent.carouselImages.length > 1) {
          // 强制重新设置轮播图配置
          carouselRef.value.$forceUpdate && carouselRef.value.$forceUpdate();
        }
      }
    });
  }
}, { deep: true, immediate: true });

// 监听模板变化，确保模板切换时清理数据
watch(() => currentTemplate?.value, (newTemplate, oldTemplate) => {
  if (newTemplate && oldTemplate && newTemplate.cardId !== oldTemplate.cardId) {
    // 如果从非轮播图模板切换到轮播图模板，或者轮播图模板间切换，重置轮播图状态
    const isNewCarousel = templateFactory.isCarouselTemplate(newTemplate);
    // 强制重新渲染组件
    nextTick(() => {
      if (carouselRef.value && isNewCarousel) {
        carouselRef.value.$forceUpdate && carouselRef.value.$forceUpdate();
      }
    });
  }
}, { immediate: true });

// 自动轮播相关
const shouldAutoplay = computed(() => {
  // 在模板列表页面且有多张图片时启用自动轮播
  return props.usage === 'list' && carouselImages.value.length > 1;
});

const autoplayInterval = computed(() => {
  // 自动轮播间隔时间（毫秒）
  return 3000;
});

// 判断内容是否为轮播图类型
const isCarouselContent = computed(() => {
  console.log('CarouselElement - 判断是否为轮播图内容:', {
    内容: props.content,
    carouselImages字段: props.content?.carouselImages,
    是否有carouselImages: !!props.content?.carouselImages,
    carouselImages长度: props.content?.carouselImages?.length
  });
  
  return props.content && props.content.carouselImages && Array.isArray(props.content.carouselImages) && props.content.carouselImages.length > 0;
});
</script>

<style scoped lang="scss">
.carousel-element {
  width: 100%;
  position: relative;
  margin-bottom: 10px;
  .carousel-container {
    width: 100%;
    overflow: hidden;
    
    :deep(.el-carousel) {
      .el-carousel__item {
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f7fa;
      }
      
      // 指示器样式
      .el-carousel__indicators {
        // 确保指示器容器正确显示
        position: absolute !important;
        bottom: 0px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 10 !important;
        
        .el-carousel__indicator {
          margin: 0 4px !important;
          
          .el-carousel__button {
            width: 8px !important;
            height: 8px !important;
            border-radius: 50% !important;
            background-color: rgba(255, 255, 255, 0.6) !important;
            border: 1px solid rgba(255, 255, 255, 0.8) !important;
            opacity: 1 !important;
            transition: all 0.3s ease !important;
          }
          
          &.is-active .el-carousel__button {
            background-color: #409eff !important;
            border-color: #409eff !important;
            transform: scale(1.2) !important;
          }
          
          &:hover .el-carousel__button {
            background-color: rgba(255, 255, 255, 0.8) !important;
          }
        }
      }
      
      // 箭头样式
      .el-carousel__arrow {
        background-color: rgba(0, 0, 0, 0.5) !important;
        color: #fff !important;
        border: none !important;
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.7) !important;
        }
        
        &.el-carousel__arrow--left {
          left: 10px !important;
        }
        
        &.el-carousel__arrow--right {
          right: 10px !important;
        }
      }
    }
  }
  
  .carousel-image-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    
    .carousel-image {
      width: 100%;
      height: 100%;
      // object-fit: cover;
      display: block;
    }
    
  }
  
  .no-images {
    width: 100%;
    height: 192px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border-radius: 8px;
    position: relative;
    
    .default-image {
      width: 100%;
      height: 100%;
      // object-fit: cover;
    }
  }
}

</style> 