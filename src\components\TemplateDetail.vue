<template>
  <el-dialog v-model="detailDialogVisible" title="模板详情" width="40%" align-center>
    <ul v-if="templateDetail" class="template-detail">
      <li class="tit">模板信息</li>
      <li>
        <span>模板名称：{{ templateDetail.templateName }}</span>
        <span>模板 ID：{{ templateDetail.templateId }}</span>
        <span>模板用途：{{ templateDetail.templatePurpose }}</span>
        <span>提交时间：{{ templateDetail.timeCreates }}</span>
        <span>短信签名：{{ templateDetail.aimSmsSigns }}</span>
      </li>
      <li class="tit">短信示例</li>
      <li><el-input type="textarea" class="textarea" v-model="templateDetail.smsExample" readonly /></li>
      <li class="tit">审核信息</li>
      <li class="flex-row-start"><em class="label">审核状态：</em>{{ AUDIT_STATE_MAP[templateDetail.channels[0].auditState] || templateDetail.channels[0].auditState }}</li>
      <li class="flex-row-start">
        <em class="label">渠道类型：</em> 
        <el-select v-model="selectedChannelType" placeholder="请选择渠道类型">
          <el-option
            v-for="channel in channelTypes"
            :key="channel"
            :label="CHANNEL_TYPE_MAP[channel] || channel"
            :value="channel"
          />
        </el-select>
      </li>
      <li class="flex-row-start"><em class="label">提交结果：</em>{{ templateDetail.channels[0].subMsg }}</li>
      <li class="flex-row-start"><em class="label">渠道模板ID：</em>{{ templateDetail.channels[0].tplId }}</li>
      <li class="flex-row-start"><em class="label">审核备注：</em>{{ templateDetail.channels[0].auditDesc }}</li>
      <li class="flex-row-start">
        <em class="label">可用厂商：</em>
        <div class="flex-row-start">
          <div class="factory-icon" v-for="factory in parsedFactoryInfos" :key="factory">
            <img :src="getFactoryIcon(factory)" />
            {{ getFactoryName(factory) }}
          </div>
        </div>
      </li>
    </ul>
  </el-dialog>
</template>

<script setup>
import { ref, defineProps, defineExpose } from 'vue';
import api from "@/api/index";
import { getFactoryIcon, getFactoryName, parseFactoryInfos as parseFactories } from '@/utils/factoryUtils';

// 定义审核状态映射对象
const AUDIT_STATE_MAP = {
  '1': '待审核',
  '2': '审核通过',
  '3': '审核未通过'
};
// 添加渠道类型映射（新增代码）
const CHANNEL_TYPE_MAP = {
  'AIM_SH_UNICOM': '阅信+上海联通'
};
const detailDialogVisible = ref(false);
const templateDetail = ref(null);
const selectedChannelType = ref('');
const channelTypes = ref([]);
const parsedFactoryInfos = ref([]);

// 解析厂商信息 - 使用公共方法，但增加额外兼容逻辑
const parseFactoryInfos = (template) => {
  return parseFactories(template);
};

// 打开详情弹窗
const open = async (template) => {
  try {
    const res = await api.getTemplate({ templateId: template.templateId });
    if (res.code === 0) {
      templateDetail.value = res.data;
      detailDialogVisible.value = true;
      // 解析 aimSmsSigns 字段
      if (templateDetail.value.aimSmsSigns) {
        let signs = [];
        try {
          // 尝试将其作为 JSON 解析
          signs = JSON.parse(templateDetail.value.aimSmsSigns);
          if (!Array.isArray(signs)) {
            // 若解析结果不是数组，抛出错误以便进入 catch 块处理
            throw new Error('解析结果不是数组');
          }
          // 对数组元素进行处理，转换为字符串并去除首尾空格
          signs = signs.map(sign => String(sign).trim()).filter(Boolean);
        } catch (jsonError) {
          // 解析失败，按逗号分隔处理
          console.warn('aimSmsSigns 不是有效的 JSON 字符串，按逗号分隔处理', jsonError);
          // 按逗号分割字符串，去除前后空格，同时处理可能存在的引号
          signs = templateDetail.value.aimSmsSigns
            .replace(/^["']|["']$/g, '') // 移除首尾可能存在的引号
            .split(',')
            .map(sign => sign.trim().replace(/^["']|["']$/g, '')) // 移除元素首尾可能存在的引号
            .filter(sign => sign !== '');
        }
        // 去重处理
        signs = [...new Set(signs)];
        // 将处理后的签名用逗号和空格拼接
        templateDetail.value.aimSmsSigns = signs.join(', ');
      }
      
      // 根据 useId 确定模板用途，统一转换为字符串进行比较
      templateDetail.value.templatePurpose = String(templateDetail.value.useId) === '1' ? '商用' : '试商用';

      // 确保 channels 数组存在且有元素
      if (!templateDetail.value.channels) {
        templateDetail.value.channels = [];
      }

      if (templateDetail.value.channels.length === 0) {
        // 如果没有 channels，添加一个默认的空对象
        templateDetail.value.channels.push({
          channelType: '未知',
          tplId: '未知',
          auditDesc: '',
          auditState: '0' // 默认状态
        });
      }

      // 获取所有渠道类型
      const allChannels = templateDetail.value.channels.map(channel => channel.channelType || '未知');
      channelTypes.value = [...new Set(allChannels)];

      // 初始化选择框
      selectedChannelType.value = templateDetail.value.channels[0].channelType || '未知';

      // 将 auditState 转换为字符串类型，确保存在
      if (templateDetail.value.channels[0].auditState !== undefined && templateDetail.value.channels[0].auditState !== null) {
        templateDetail.value.channels[0].auditState = String(templateDetail.value.channels[0].auditState);
      } else {
        templateDetail.value.channels[0].auditState = '0'; // 设置默认状态
      }

      // 复用解析函数
      parsedFactoryInfos.value = parseFactoryInfos(templateDetail.value);

      detailDialogVisible.value = true;
    } else {
      ElMessage.error(res.message || '获取模板详情失败');
    }
  } catch (error) {
    console.error('获取模板详情失败:', error);
    ElMessage.error('获取模板详情失败，请重试');
  }
};
// 获取当前模板详情数据
const getCurrentTemplateDetail = () => {
  return templateDetail.value;
};
defineExpose({
  open,
  getCurrentTemplateDetail
});
</script>

<style scoped lang="scss">
.template-detail{
  color: #606266;
  font-size: 14px;
  padding: 0 10px;
  li{
    margin-bottom: 16px;
    align-items: center;
    span{
      width: 50%;
      display: inline-block;
      margin-bottom: 16px;
    }
    .textarea{
      font-size: 13px;
    }
    .label{
      font-style: normal;
      flex: 0 0 86px;
    }
    .factory-icon{
      display: flex;
      font-size: 12px;
      color: #666;
      margin-right: 5px;
    }
    .factory-icon img{
      width: 18px;
      height: 18px;
      border-radius: 50%;
      margin-right: 5px;
    }
  }
  li.tit{
    font-weight: bold;
  }
}

.flex-row-start {
  display: flex;
  align-items: flex-start;
}
</style> 