<template>
  <div class="coupon-product-settings" @click.stop>
    <!-- 头部商品编辑 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑图片</h4>
      <ImageElement
        v-if="localSettings.headerProduct.image"
        :content="{
          type: 'image',
          src: localSettings.headerProduct.image,
          alt: '头部商品图片'
        }"
        :is-selected="false"
        :usage="'settings'"
        :get-media-url="getMediaUrl"
        class="media-setting head-image"
        @select="selectHeaderImageFile"
      />
      
      <!-- 头部商品图片描述 -->
      <div class="head-setting-desc">
        {{ localSettings.headerProduct?.content || '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内' }}
      </div>
      
      <el-button
        type="primary"
        class="head-select-btn"
        @click="selectHeaderImageFile"
      >
        选择图片
      </el-button>
      
      <div class="click-event-setting">
        <ClickEventSettings 
          :content="headerImageClickEventContent"
          @update:content="handleHeaderImageClickEventUpdate"
          @insert-param="handleParamInsert"
          @manage-param="handleParamManage"
        />
      </div>
    </div>

    <div class="setting-section setting-card">
      <h4 class="section-title">编辑主标题</h4>
      <RichParamInput
        v-model="localSettings.headerProduct.title"
        @input="handleSettingsChange"
      />
    </div>

    <div class="setting-section setting-card">
      <h4 class="section-title">编辑副标题</h4>
      <RichParamInput
        v-model="localSettings.headerProduct.description"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 编辑券 -->
    <div class="setting-section setting-card"> 
      <h4 class="section-title">编辑券</h4>
      <div class="setting-section setting-card setting-card-price setting-pd10">
        <h4 class="is-show-price-title">" ￥ " 图标显示/隐藏</h4>
        <el-switch 
          :model-value="showCouponCurrencySymbol"
          @update:model-value="handleCouponCurrencyDisplayChange"
        />
      </div>
      <div class="setting-section setting-card setting-pd10">
        <h4 class="section-title">券额值</h4>
        <RichParamInput
          v-model="localSettings.coupon.amount"
          @input="handleSettingsChange"
        />
      </div>
      <div class="setting-section setting-card setting-pd10">
        <h4 class="section-title">编辑主标题</h4>
        <RichParamInput
          v-model="localSettings.coupon.title"
          @input="handleSettingsChange"
        />
      </div>
      <div class="setting-section setting-card setting-pd10">
        <h4 class="section-title">编辑副标题</h4>
        <RichParamInput
          v-model="localSettings.coupon.description"
          @input="handleSettingsChange"
        />
      </div>
      <div class="setting-section setting-card setting-pd10">
        <h4 class="section-title">图片设置</h4>
        <div class="coupon-image-setting">
          <p>选择按钮图片：</p>
          <div class="coupon-image-setting-item">
            <img 
              v-for="image in couponButtonImages" 
              :key="image.src"
              :src="getMediaUrl(image.src)" 
              :alt="image.alt"
              :class="{ 'selected': isButtonImageSelected(image.src) }"
              @click="handleCouponButtonImageSelect(image.src)"
            />
          </div>
          <div class="button-animation-setting">
            <span>按钮形态：</span>
            <el-radio-group v-model="localSettings.coupon.buttonAnimation" @change="handleSettingsChange">
              <el-radio :value="false">静态</el-radio>
              <el-radio :value="true">动态</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="click-event-setting">
          <ClickEventSettings 
            :content="currentCouponButtonClickEventContent"
            @update:content="handleCouponButtonClickEventUpdate"
            @insert-param="handleParamInsert"
            @manage-param="handleParamManage"
          />
        </div>
      </div>
    </div>

    <!-- 编辑商品 -->
    <div class="setting-section setting-card">
      <h4 class="section-title">编辑商品</h4>
      <div class="product-count-control">
        <!-- 减号按钮 -->
        <button 
          class="control-btn minus-btn" 
          :disabled="productCount <= 1"
          @click="decreaseProductCount"
        >
          −
        </button>
        
        <!-- 数字按钮组（指示器） -->
        <div class="number-buttons">
          <button
            v-for="index in productCount"
            :key="index"
            :class="['number-btn', { active: currentProductIndex === index - 1 }]"
            @click="selectProduct(index - 1)"
          >
            {{ index }}
          </button>
        </div>
        
        <!-- 加号按钮 -->
        <button 
          class="control-btn plus-btn" 
          :disabled="productCount >= 3"
          @click="increaseProductCount"
        >
          +
        </button>
      </div>

      <div class="setting-section setting-card setting-pd10">
        <h4 class="section-title">商品图片</h4>
        <div class="image-upload-area">
          <ImageElement
            v-if="currentProduct.image"
            :content="{
              type: 'image',
              src: currentProduct.image,
              alt: currentProduct.title || `商品${currentProductIndex + 1}`
            }"
            :is-selected="false"
            :usage="'settings'"
            :get-media-url="getMediaUrl"
            class="media-setting"
            @select="selectProductImageFile"
          />
          
          <!-- 商品图片描述 -->
          <div class="setting-desc">
            {{ currentProduct?.content || '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内' }}
          </div>
          
          <el-button 
            type="primary"
            class="select-btn"
            @click="selectProductImageFile"
          >
            选择图片
          </el-button>
        </div>
      </div>

    <!-- 编辑商品名 -->
    <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑商品名称</h4>
      <RichParamInput
        v-model="currentProduct.title"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 编辑标签 -->
    <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑标签</h4>
      <RichParamInput
        v-model="currentProduct.tag"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 价格显示开关 -->
    <div class="setting-section setting-card setting-card-price setting-pd10">
      <h4 class="is-show-price-title">" ￥ " 图标显示/隐藏</h4>
      <el-switch 
        :model-value="showProductCurrencySymbol"
        @update:model-value="handleProductCurrencyDisplayChange"
      />
    </div>

    <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑价格</h4>
      <RichParamInput
        v-model="currentProduct.price"
        @input="handleSettingsChange"
      />
    </div>

    <!-- 按钮设置 -->
    <div class="setting-section setting-card setting-pd10">
      <h4 class="section-title">编辑按钮</h4>
      
      <h4 class="button-name-title">按钮名称：<span>（最多4位）</span></h4>
      <RichParamInput
        v-model="currentProduct.buttonText"
        @input="handleSettingsChange"
      />
      
      <div class="button-click-event-setting">
        <ClickEventSettings 
          :content="currentProductButtonContentWithIndex"
          @update:content="handleProductButtonClickEventUpdate"
          @insert-param="handleParamInsert"
          @manage-param="handleParamManage"
        />
      </div>
    </div>
    </div>

    <!-- 媒体选择对话框 -->
    <MediaSelectorDialog
      v-model="dialogVisible"
      :mediaType="dialogMediaType"
      :filter-app-key="appKey"
      @confirm="handleMediaSelect"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import RichParamInput from '../richtext/RichParamInput.vue';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import ImageElement from '../template/preview/elements/ImageElement.vue';
import ClickEventSettings from './ClickEventSettings.vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import { ClickEventTypeConverter, ActionJsonGenerator, CLICK_EVENT_TYPES, validateAndCompleteClickEvent, extractClickEventValidationFields} from '@/utils/clickEventManager';

const props = defineProps({
  content: {
    type: Object,
    required: true
  },
  template: {
    type: Object,
    default: null
  },
  appKey: {
    type: String,
    required: true
  }
});

const emit = defineEmits([
  'update:content',
  'input',
  'insert-param',
  'manage-param',
  'paramInsert',
  'paramManage',
  'settings-change'
]);

// 响应式数据
const dialogVisible = ref(false);
const dialogMediaType = ref('image');
const currentProductIndex = ref(0);
const productCount = ref(3);

// 添加初始化标记
const isInitialized = ref(false);

// 添加一个标记来区分当前是在选择头部图片还是商品图片
const isSelectingHeaderImage = ref(false);

// 本地设置数据
const localSettings = ref({
  // 头部商品信息
  headerProduct: {
    image: '',
    title: '',
    description: '',
    imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' }
  },
  // 券信息
  coupon: {
    amount: '',
    title: '',
    description: '',
    buttonText: '',
    buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
    aimCurrencyDisplay: 1,
    buttonImage: '/aim_files/aim_defult/neck.png', // 默认使用第一个图片
    buttonAnimation: false // 默认为静态
  },
  // 商品列表（最多3个）
  products: []
});

// 券的货币符号显示状态
const showCouponCurrencySymbol = computed({
  get: () => {
    return localSettings.value.coupon.aimCurrencyDisplay === 1;
  },
  set: (value) => {
    localSettings.value.coupon.aimCurrencyDisplay = value ? 1 : 0;
    handleSettingsChange();
  }
});

// 当前商品的货币符号显示状态
const showProductCurrencySymbol = computed({
  get: () => {
    const currentProduct = localSettings.value.products[currentProductIndex.value];
    return currentProduct?.aimCurrencyDisplay === 1;
  },
  set: (value) => {
    const currentProduct = localSettings.value.products[currentProductIndex.value];
    if (currentProduct) {
      currentProduct.aimCurrencyDisplay = value ? 1 : 0;
      handleSettingsChange();
    }
  }
});

// 添加券按钮图片选项
const couponButtonImages = [
  {
    src: '/aim_files/aim_defult/neck.png',
    alt: '按钮样式1'
  },
  {
    src: '/aim_files/aim_defult/rob.png',
    alt: '按钮样式2'
  }
];

// 处理券按钮图片选择
const handleCouponButtonImageSelect = (imageSrc) => {
  console.log('选择券按钮图片:', imageSrc);
  localSettings.value.coupon.buttonImage = imageSrc;
  
  // 确保立即触发更新
  nextTick(() => {
    handleSettingsChange();
  });
};

// 判断图片是否被选中
const isButtonImageSelected = (imageSrc) => {
  return localSettings.value.coupon?.buttonImage === imageSrc;
};

// 重置为默认值
const resetToDefaults = () => {
  console.log('CouponProductSettings - 重置为默认值');
  
  const defaultProducts = Array.from({ length: 3 }, (_, index) => ({
    id: `product-${index + 1}`,
    image: '',
    title: '',
    tag: '',
    price: '',
    buttonText: '',
    imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
    buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
    actionJson: { target: '' },
    imageActionJson: { target: '' },
    aimCurrencyDisplay: 1,
    hidden: false
  }));

  localSettings.value = {
    headerProduct: {
      image: '',
      title: '',
      description: '',
      imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' }
    },
    coupon: {
      amount: '',
      title: '',
      description: '',
      buttonText: '',
      buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
      aimCurrencyDisplay: 1,
      buttonImage: '/aim_files/aim_defult/neck.png',
      buttonAnimation: false
    },
    products: defaultProducts
  };
  
  productCount.value = 3;
  currentProductIndex.value = 0;
  
  console.log('CouponProductSettings - 重置完成，默认设置:', localSettings.value);
};

// 初始化设置
const initializeSettings = (data) => {
  console.log('CouponProductSettings - 初始化设置，传入数据:', data);
  
  if (!data || Object.keys(data).length === 0) {
    console.warn('数据为空，重置为默认值');
    resetToDefaults();
    return;
  }

  // 初始化头部商品
  if (data.headerProduct) {
    localSettings.value.headerProduct = {
      image: data.headerProduct.image || '',
      content: data.headerProduct.content || '',
      title: data.headerProduct.title || '',
      description: data.headerProduct.description || '',
      imageClickEvent: data.headerProduct.imageClickEvent || { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' }
    };
  }
  
  // 初始化券信息
  if (data.coupon) {
    localSettings.value.coupon = {
      amount: data.coupon.amount || '',
      title: data.coupon.title || '',
      description: data.coupon.description || '',
      buttonText: data.coupon.buttonText || '',
      buttonClickEvent: data.coupon.buttonClickEvent || { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
      aimCurrencyDisplay: data.coupon.aimCurrencyDisplay !== undefined ? data.coupon.aimCurrencyDisplay : 1,
      buttonImage: data.coupon.buttonImage || '/aim_files/aim_defult/neck.png',
      buttonAnimation: data.coupon.buttonAnimation !== undefined ? data.coupon.buttonAnimation : false
    };
  } else {
    // 确保即使没有券数据也初始化基本结构
    localSettings.value.coupon = {
      amount: '',
      title: '',
      description: '',
      buttonText: '',
      buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
      aimCurrencyDisplay: 1,
      buttonImage: '/aim_files/aim_defult/neck.png',
      buttonAnimation: false
    };
  }
  
  // 初始化商品列表
  if (data.products && Array.isArray(data.products)) {
    // 先计算实际有内容的商品数量
    const productsWithContent = data.products.filter(product => 
      product.title || product.image || product.price || product.tag
    );
    const actualProductCount = Math.min(Math.max(productsWithContent.length, 2), 3); // 最少2个，最多3个
    productCount.value = actualProductCount;
    
    localSettings.value.products = data.products.slice(0, 3).map((product, index) => ({
      id: product.id || `product-${index + 1}`,
      image: product.image || '',
      content: product.content || '',
      title: product.title || '',
      tag: product.tag || '',
      price: product.price || '',
      buttonText: product.buttonText || '',
      // imageClickEvent: product.imageClickEvent || { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
      buttonClickEvent: product.buttonClickEvent || { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
      actionJson: product.actionJson || { target: '' },
      imageActionJson: product.imageActionJson || { target: '' },
      aimCurrencyDisplay: product.aimCurrencyDisplay !== undefined ? product.aimCurrencyDisplay : 1,
      hidden: index >= actualProductCount // 根据实际商品数量设置隐藏状态
    }));
    
    // 如果商品不足3个，补充默认商品
    while (localSettings.value.products.length < 3) {
      localSettings.value.products.push({
        id: `product-${localSettings.value.products.length + 1}`,
        image: '',
        title: '',
        tag: '',
        price: '',
        buttonText: '',
        // imageClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
        buttonClickEvent: { actionType: CLICK_EVENT_TYPES.OPEN_BROWSER, actionUrl: '' },
        actionJson: { target: '' },
        imageActionJson: { target: '' },
        aimCurrencyDisplay: 1,
        hidden: true
      });
    }
  } else {
    resetToDefaults();
  }

  // 设置选中的商品索引
  if (data.selectedProductIndex !== undefined) {
    currentProductIndex.value = Math.max(0, Math.min(data.selectedProductIndex, localSettings.value.products.length - 1));
    localSettings.value.selectedProductIndex = currentProductIndex.value;
  } else {
    currentProductIndex.value = 0;
    localSettings.value.selectedProductIndex = 0;
  }
  
  // 设置产品数量
  if (data.productCount !== undefined) {
    productCount.value = Math.max(1, Math.min(data.productCount, 3));
  }
  
  console.log('CouponProductSettings - 初始化完成，当前设置:', localSettings.value);
};

// 商品操作方法 - 移到 watch 之前定义
const selectProduct = (index) => {
  if (index === currentProductIndex.value) return;
  
  console.log('切换到商品:', index);
  currentProductIndex.value = index;
  localSettings.value.selectedProductIndex = index;
  handleSettingsChange();
};

// 监听内容变化
watch(() => props.content, (newVal) => {
  console.log('CouponProductSettings - Props内容变化:', newVal);
  
  if (!newVal) return;

  if (newVal.isCouponProductSettings && newVal.currentData) {
    if (newVal.currentData.selectedProductIndex !== undefined && 
        newVal.currentData.selectedProductIndex !== currentProductIndex.value) {
      console.log('检测到selectedProductIndex变化，切换商品:', newVal.currentData.selectedProductIndex);
      selectProduct(newVal.currentData.selectedProductIndex);
    }
    
    if (!isInitialized.value || 
        !localSettings.value || 
        !localSettings.value.products || 
        localSettings.value.products.length === 0) {
      console.log('需要初始化设置');
      initializeSettings(newVal.currentData);
      isInitialized.value = true;
    }
  }
}, { 
  immediate: true, 
  deep: true 
});

// 当前选中的商品
const currentProduct = computed(() => {
  if (!localSettings.value || !localSettings.value.products || !Array.isArray(localSettings.value.products)) {
    console.warn('localSettings.value.products 不存在，触发重置');
    resetToDefaults();
    return localSettings.value.products[0];
  }
  
  const index = Math.max(0, Math.min(currentProductIndex.value || 0, localSettings.value.products.length - 1));
  const product = localSettings.value.products[index];
  
  if (!product) {
    console.warn(`商品索引${index}不存在，使用第一个商品`);
    return localSettings.value.products[0];
  }
  
  return product;
});


// 当前券按钮点击事件内容
const currentCouponButtonClickEventContent = computed(() => {
  const clickEvent = localSettings.value.coupon.buttonClickEvent;

  // 如果已经是标准格式（包含actionType），直接返回
  if (clickEvent && clickEvent.actionType) {
    return {
      contentId: 'coupon-button-click-event',
      type: 'coupon-button',
      actionType: clickEvent.actionType,
      actionUrl: clickEvent.actionUrl || '',
      actionPath: clickEvent.actionPath || clickEvent.smsBody || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      // 添加短信相关字段
      smsBody: clickEvent.smsBody || '',
      // 添加邮箱相关字段
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      // 添加日程相关字段
      scheduleTitle: clickEvent.scheduleTitle || '',
      scheduleContent: clickEvent.scheduleContent || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      // 添加弹窗相关字段
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      // 添加复制参数相关字段
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
  }

  // 如果是旧格式（包含type），使用转换方法
  return ActionJsonGenerator.toClickEventSettings(
    clickEvent,
    'coupon-button-click-event',
    'coupon-button'
  );
});

// 处理券按钮点击事件更新
const handleCouponButtonClickEventUpdate = (clickEventData) => {
  const standardClickEvent = ActionJsonGenerator.fromClickEventSettings(clickEventData);
  console.log('处理券按钮点击事件更新:', standardClickEvent);

  // 使用统一的字段提取方法
  const validationContent = extractClickEventValidationFields({
    ...standardClickEvent,
    actionType: ClickEventTypeConverter.toActionType(standardClickEvent.type)
  });

  // 使用统一的验证和完善方法
  const success = validateAndCompleteClickEvent(validationContent);
  if (success) {
    // 更新点击事件
    localSettings.value.coupon.buttonClickEvent = validationContent;
    // actionJson 已经由 validateAndCompleteClickEvent 自动生成
    localSettings.value.coupon.actionJson = validationContent.actionJson;
  } else {
    console.error('券按钮点击事件验证失败');
  }

  handleSettingsChange();
};

// 当前商品按钮内容
const currentProductButtonContent = computed(() => {
  const currentProduct = localSettings.value.products[currentProductIndex.value];
  if (!currentProduct) return {};

  const clickEvent = currentProduct.buttonClickEvent;

  // 如果已经是标准格式（包含actionType），直接返回
  if (clickEvent && clickEvent.actionType) {
    return {
      contentId: `product-${currentProductIndex.value}-button-click-event`,
      type: 'product-button',
      actionType: clickEvent.actionType,
      actionUrl: clickEvent.actionUrl || '',
      actionPath: clickEvent.actionPath || clickEvent.smsBody || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      // 添加短信相关字段
      smsBody: clickEvent.smsBody || '',
      // 添加邮箱相关字段
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      // 添加日程相关字段
      scheduleTitle: clickEvent.scheduleTitle || '',
      scheduleContent: clickEvent.scheduleContent || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      // 添加弹窗相关字段
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      // 添加复制参数相关字段
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
  }

  // 如果是旧格式（包含type），使用转换方法
  return ActionJsonGenerator.toClickEventSettings(
    clickEvent,
    `product-${currentProductIndex.value}-button-click-event`,
    'product-button'
  );
});

const currentProductButtonContentWithIndex = computed(() => {
  const baseContent = currentProductButtonContent.value;
  return {
    ...baseContent,
    contentId: `coupon-product-settings-product-${currentProductIndex.value}`,
    type: 'coupon-product-settings-button',
    productIndex: currentProductIndex.value
  };
});

// 处理商品按钮点击事件更新
const handleProductButtonClickEventUpdate = (clickEventData) => {
  const standardClickEvent = ActionJsonGenerator.fromClickEventSettings(clickEventData);
  console.log('处理商品按钮点击事件更新:', standardClickEvent);

  if (localSettings.value.products[currentProductIndex.value]) {
    // 使用统一的字段提取方法
    const validationContent = extractClickEventValidationFields({
      ...standardClickEvent,
      actionType: ClickEventTypeConverter.toActionType(standardClickEvent.type)
    });

    // 使用统一的验证和完善方法
    const success = validateAndCompleteClickEvent(validationContent);
    if (success) {
      // 更新点击事件
      localSettings.value.products[currentProductIndex.value].buttonClickEvent = validationContent;
      // actionJson 已经由 validateAndCompleteClickEvent 自动生成
      localSettings.value.products[currentProductIndex.value].actionJson = validationContent.actionJson;
    } else {
      console.error('商品按钮点击事件验证失败');
    }
  }

  handleSettingsChange();
  console.log('商品按钮事件已存入localSettings:', localSettings.value.products[currentProductIndex.value].buttonClickEvent);
};

// 处理设置变化
const handleSettingsChange = () => {
  console.log('CouponProductSettings - 处理设置变化，当前设置:', localSettings.value);
  
  const currentData = {
    headerProduct: localSettings.value.headerProduct,
    coupon: {
      ...localSettings.value.coupon,
      amount: localSettings.value.coupon.amount || '', // 确保amount字段存在
      aimCurrencyDisplay: localSettings.value.coupon.aimCurrencyDisplay !== undefined ? 
        localSettings.value.coupon.aimCurrencyDisplay : 1
    },
    products: localSettings.value.products.map((product, index) => ({
      ...product,
      hidden: index >= productCount.value
    })),
    selectedProductIndex: currentProductIndex.value,
    productCount: productCount.value
  };

  const updatedContent = {
    type: 'coupon-product-settings',
    contentId: props.content.contentId,
    isCouponProductSettings: true,
    currentData
  };
  
  console.log('发送更新数据:', updatedContent);
  emit('update:content', updatedContent);
  emit('settings-change', updatedContent);
};

// 商品操作方法
const increaseProductCount = () => {
  if (productCount.value < 3) {
    productCount.value++;
    if (localSettings.value.products[productCount.value - 1]) {
      localSettings.value.products[productCount.value - 1].hidden = false;
    }
    // 自动切换到新增的商品
    selectProduct(productCount.value - 1);
    
    nextTick(() => {
      handleSettingsChange();
    });
  }
};

const decreaseProductCount = () => {
  if (productCount.value > 1) {
    if (currentProductIndex.value >= productCount.value - 1) {
      // 如果当前选中的是要被隐藏的商品，切换到前一个商品
      selectProduct(productCount.value - 2);
    }
    
    if (localSettings.value.products[productCount.value - 1]) {
      localSettings.value.products[productCount.value - 1].hidden = true;
    }
    
    productCount.value--;
    
    nextTick(() => {
      handleSettingsChange();
    });
  }
};

// 修改选择头部图片的方法
const selectHeaderImageFile = () => {
  isSelectingHeaderImage.value = true; // 设置标记而不是清除商品索引
  dialogMediaType.value = 'image';
  dialogVisible.value = true;
};

const selectProductImageFile = () => {
  isSelectingHeaderImage.value = false;
  dialogMediaType.value = 'image';
  dialogVisible.value = true;
};

const handleMediaSelect = (mediaData) => {
  console.log('CouponProductSettings - 选择媒体文件:', mediaData);
  
  const relativePath = `/aim_files/${mediaData.appKey}/${mediaData.path}`;
  
  if (dialogMediaType.value === 'image') {
    if (!isSelectingHeaderImage.value) {
      // 更新商品图片
      localSettings.value.products[currentProductIndex.value].image = relativePath;
    } else {
      // 更新头部商品图片
      localSettings.value.headerProduct.image = relativePath;
    }
  }
  
  handleSettingsChange();
  dialogVisible.value = false;
  isSelectingHeaderImage.value = false; // 重置标记
  ElMessage.success('媒体文件设置成功');
};

// 处理货币符号显示变化
const handleCouponCurrencyDisplayChange = (value) => {
  showCouponCurrencySymbol.value = value;
};

const handleProductCurrencyDisplayChange = (value) => {
  showProductCurrencySymbol.value = value;
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  emit('insert-param', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('manage-param');
};

// 头部图片点击事件内容
const headerImageClickEventContent = computed(() => {
  const clickEvent = localSettings.value.headerProduct.imageClickEvent;

  // 如果已经是标准格式（包含actionType），直接返回
  if (clickEvent && clickEvent.actionType) {
    return {
      contentId: 'header-image-click-event',
      type: 'header-image',
      actionType: clickEvent.actionType,
      actionUrl: clickEvent.actionUrl || '',
      actionPath: clickEvent.actionPath || clickEvent.smsBody || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      // 添加短信相关字段
      smsBody: clickEvent.smsBody || '',
      // 添加邮箱相关字段
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      // 添加日程相关字段
      scheduleTitle: clickEvent.scheduleTitle || '',
      scheduleContent: clickEvent.scheduleContent || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      // 添加弹窗相关字段
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      // 添加复制参数相关字段
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
  }

  // 如果是旧格式（包含type），使用转换方法
  return ActionJsonGenerator.toClickEventSettings(
    clickEvent,
    'header-image-click-event',
    'header-image'
  );
});

// 处理头部图片点击事件更新
const handleHeaderImageClickEventUpdate = (event) => {
  const standardClickEvent = ActionJsonGenerator.fromClickEventSettings(event);
  console.log('处理头部图片点击事件更新:', standardClickEvent);

  // 使用统一的字段提取方法
  const validationContent = extractClickEventValidationFields({
    ...standardClickEvent,
    actionType: ClickEventTypeConverter.toActionType(standardClickEvent.type)
  });

  // 使用统一的验证和完善方法
  const success = validateAndCompleteClickEvent(validationContent);
  if (success) {
    // 更新点击事件
    localSettings.value.headerProduct.imageClickEvent = validationContent;
    // actionJson 已经由 validateAndCompleteClickEvent 自动生成
    localSettings.value.headerProduct.actionJson = validationContent.actionJson;
  } else {
    console.error('头部图片点击事件验证失败');
  }

  handleSettingsChange();
};

// 组件挂载时初始化
onMounted(() => {
  console.log('CouponProductSettings - 组件挂载，开始初始化');
  console.log('CouponProductSettings - props.content:', props.content);
  
  if (!isInitialized.value && props.content && props.content.currentData) {
    initializeSettings(props.content.currentData);
    isInitialized.value = true;
  } else if (!isInitialized.value) {
    console.log('CouponProductSettings - 没有props数据，使用默认设置');
    resetToDefaults();
    isInitialized.value = true;
  }
  
  // 确保默认选中第一个商品
  nextTick(() => {
    if (currentProductIndex.value !== 0) {
      selectProduct(0);
    }
  });
});
</script>

<style scoped lang="scss">
.coupon-product-settings {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
  
  :deep(.setting-content) {
    padding: 0;
  }
  
  :deep(.rich-param-input) {
    padding: 14px 16px;
  }
}

.setting-section {
  margin-bottom: 24px;
  
  .setting-pd10 {
    margin: 10px 16px;
  }
  
  .section-title {
    color: #303133;
    font-weight: normal;
    padding: 10px 16px;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
    position: relative;
  }
}

.setting-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.setting-card-price {
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  .is-show-price-title {
    margin: 0;
    padding: 16px;
    font-size: 14px;
    color: #303133;
    border-bottom: none;
  }
  
  :deep(.el-switch) {
    margin-right: 16px;
  }
}

.preview-image.media-setting {
  width: 150px;
  height: 150px;
  object-fit: none;
  display: block;
  margin: 0px 0 16px;
  
  .media-preview {
    margin-bottom: 12px;
    
    img {
      width: 150px;
      height: 150px;
    }
  }
  
  .media-actions {
    display: flex;
    gap: 8px;
  }
}
.head-setting-desc{
  margin: 0 16px 16px;
}
.setting-desc{
  margin-bottom: 16px;
}
.select-btn {
  width: auto;
  margin: 0px 0 16px;
}
.head-select-btn{
  margin:0 16px 16px;
}
.preview-image.head-image{
  margin: 16px;
}


/* 商品数量控制器样式 */
.product-count-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.control-btn:hover:not(:disabled) {
  border-color: #409eff;
  color: #409eff;
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.number-buttons {
  display: flex;
  gap: 4px;
}

.number-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #409eff;
    color: #409eff;
  }

  &.active {
    background-color: #409eff;
    border-color: #409eff;
    color: #ffffff;
    font-weight: 600;
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }
}

/* 图片设置样式 */
.image-upload-area {
  width: 100%;
  padding: 16px;
}

.image-preview {
  margin-bottom: 12px;
}

.preview-image {
  width: 100%;
  height: 210px;
  // object-fit: cover;
  border-radius: 4px;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
  width: 100%;
  height: 210px;
  border: 2px dashed #dcdfe6;
  border-radius: 4px;
  margin-bottom: 12px;
}

.image-placeholder .el-icon {
  font-size: 48px;
  margin-bottom: 8px;
}

.image-placeholder span {
  font-size: 14px;
}
.button-name-title {
  color: #303133;
  font-weight: normal;
  padding: 10px 16px 0;
  width: 100%;
  
  span {
    color: #999;
  }
}

.button-click-event-setting {
  width: 100%;
  padding: 0px 16px;
}

.click-event-setting {
  width: 100%;
  padding: 0px 16px 16px;
}

.coupon-image-setting{
  margin: 16px;
  .coupon-image-setting-item{
    display: flex;
    gap: 16px;
    margin-top: 10px;
    img{
      width: 80px;
      cursor: pointer;
      opacity: .7;
      border-radius: 50px;
      margin-right: 10px;
      padding: 10px;
      transition: all 0.3s ease;
      
      &:hover {
        opacity: 1;
        border-color: #409eff;
      }
      
      &.selected {
        opacity: 1;
        border: 1px solid #409eff;
      }
    }
  }
}

.button-animation-setting {
  margin-top: 16px;
  display: flex;
  align-items: center;
  
  span {
    margin-right: 10px;
    font-size: 14px;
    color: #606266;
  }
}
</style> 