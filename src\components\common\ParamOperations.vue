<template>
  <div class="param-operations">
    暂无参数设置
    <!-- <el-button type="primary" size="small" @click="handleInsertParam">
      插入参数
    </el-button>
    <el-button size="small" @click="handleManageParam">
      管理参数
    </el-button> -->
  </div>
</template>

<script setup>
import { ref, defineEmits, defineProps } from 'vue';

const props = defineProps({
  source: {
    type: String,
    default: 'unknown'
  },
  component: {
    type: String,
    default: 'unknown'
  },
  panelId: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['insert-param', 'manage-param']);

// 处理参数插入按钮点击
const handleInsertParam = (event) => {
  console.log('参数插入按钮点击');
  
  try {
    // 防止事件冒泡和默认行为
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    // 使用静态时间戳比较，确保足够的时间间隔
    const now = Date.now();
    if (window.LAST_PARAM_INSERT_TIME && (now - window.LAST_PARAM_INSERT_TIME < 100)) {
      console.log('参数插入按钮点击过快，忽略本次操作');
      return;
    }
    
    // 记录本次点击时间
    window.LAST_PARAM_INSERT_TIME = now;
    
    // 构建事件数据
    const eventData = {
      source: props.source || 'param-operations',
      component: props.component || 'button',
      timestamp: now,
      panelId: props.panelId,
      originalEvent: event
    };
    
    // 创建一个小延时，让UI有时间响应按钮点击状态
    setTimeout(() => {
      // 发送事件到父组件，让父组件决定如何处理
      emit('insert-param', eventData);
    }, 10);
  } catch (error) {
    console.error('处理参数插入按钮点击出错:', error);
  }
};

// 参数管理操作
const handleManageParam = (e) => {
  // 准备事件对象
  const eventData = {
    originalEvent: e,
    source: props.source,
    component: props.component,
    timestamp: Date.now()
  };
  
  // 触发事件给父组件
  emit('manage-param', eventData);
};

// 选中内容项时的处理
const handleContentClick = (content) => {
  // 首先选中内容
  selectedContent.value = content;
  
  // 延迟展开点击事件设置面板，给组件足够的初始化时间
  setTimeout(() => {
    const settingsPanel = document.querySelector('.click-event-settings-button');
    if (settingsPanel) {
      // 先检查是否已展开
      const settingsContent = settingsPanel.nextElementSibling;
      if (settingsContent && getComputedStyle(settingsContent).display === 'none') {
        // 需要展开面板
        settingsPanel.click();
      }
    }
  }, 300);
};
</script>

<style scoped lang="scss">
.param-operations {
  display: flex;
  gap: 10px;
}
</style> 