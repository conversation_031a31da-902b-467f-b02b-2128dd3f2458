/**
 * 媒体资源工具函数
 */

// 动态获取当前环境的服务器前缀（从 env.config.js 导入）
import envConfig from '../../env.config.js';
export const SERVER_PREFIX = envConfig.apiBaseUrl; // 直接使用当前环境的 apiBaseUrl

/**
 * 获取完整的媒体URL（未修改部分保持不变）
 * @param {string} path - 媒体相对路径
 * @returns {string} 完整的媒体URL
 */
export function getMediaUrl(path) {
  if (!path) return '';
  
  // 如果已经是完整URL（包含http://或https://），直接返回
  if (path.startsWith('https://') || path.startsWith('http://')) {
    return path;
  }
  
  // 如果已经包含了SERVER_PREFIX，直接返回
  if (path.startsWith(SERVER_PREFIX)) {
    return path;
  }
  
  // 确保path是以/开头
  const formattedPath = path.startsWith('/') ? path : `/${path}`;
  
  // 拼接服务器地址和路径
  return `${SERVER_PREFIX}${formattedPath}`;
}

// getRelativePath 函数（未修改部分保持不变）
export const getRelativePath = (url) => {
  if (!url) return '';
  
  if (url.startsWith(SERVER_PREFIX)) {
    return url.replace(SERVER_PREFIX, '');
  }
  
  return url;
}; 