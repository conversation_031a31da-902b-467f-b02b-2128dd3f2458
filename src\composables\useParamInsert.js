/**
 * 参数插入组合式函数 - 为组件提供统一的参数插入能力
 * 
 * 该组合函数提供以下功能：
 * 1. 插入参数到可编辑元素
 * 2. 参数插入事件监听
 * 3. 焦点管理
 * 4. 自动关联参数到面板
 */

import { ref, onMounted, onBeforeUnmount, computed } from 'vue';
import { useParamService } from '@/services/ParamService';

/**
 * 参数插入功能组合式函数
 * 简化参数插入功能在组件中的使用
 * 
 * @param {Object} options 配置选项
 * @param {Function|HTMLElement} options.target 目标元素或返回目标元素的函数
 * @param {String|Function} options.panelId 面板ID或返回面板ID的函数
 * @param {Boolean} options.autoFocus 是否自动聚焦目标元素
 * @param {Function} options.onSuccess 插入成功回调
 * @param {Function} options.onError 插入失败回调
 * @returns {Object} 参数插入相关方法和状态
 */
export function useParamInsert(options = {}) {
  // 参数服务实例
  const paramService = useParamService();
  
  // 状态管理
  const isInserting = ref(false);  // 是否正在插入参数
  const isActive = ref(false);    // 参数插入功能是否激活
  
  /**
   * 获取目标元素
   * @returns {HTMLElement} 目标元素
   */
  const getTarget = () => {
    if (!options.target) return null;
    
    if (typeof options.target === 'function') {
      return options.target();
    }
    
    return options.target;
  };
  
  /**
   * 获取面板ID
   * @returns {String} 面板ID
   */
  const getPanelId = () => {
    if (!options.panelId) return '';
    
    if (typeof options.panelId === 'function') {
      return options.panelId();
    }
    
    if (typeof options.panelId === 'object' && options.panelId.value) {
      return options.panelId.value;
    }
    
    return options.panelId;
  };
  
  /**
   * 插入参数到目标元素
   * @param {String|Object} param 参数文本或参数配置对象
   * @returns {Boolean} 是否插入成功
   */
  const insertParam = (param) => {
    const target = getTarget();
    
    if (!target) {
      console.error('无法插入参数: 目标元素不存在');
      if (options.onError) options.onError('目标元素不存在');
      return false;
    }
    
    try {
      isInserting.value = true;
      
      // 参数处理
      let paramConfig = {};
      
      if (typeof param === 'string') {
        // 字符串参数：作为参数文本
        paramConfig = { paramText: param };
      } else if (typeof param === 'object') {
        // 对象参数：直接使用
        paramConfig = param;
      }
      
      // 添加面板ID
      if (!paramConfig.panelId) {
        paramConfig.panelId = getPanelId();
      }
      
      // 插入参数
      const result = paramService.insertParam(target, {
        panelId: paramConfig.panelId,
        paramId: paramConfig.paramId,
        focus: true,
        formatText: true
      });
      
      // 处理结果
      if (result) {
        if (options.onSuccess) {
          options.onSuccess(result);
        }
      } else if (options.onError) {
        options.onError('参数插入失败');
      }
      
      return result;
    } catch (error) {
      console.error('参数插入错误:', error);
      if (options.onError) options.onError(error.message);
      return false;
    } finally {
      // 延迟清除插入状态，避免事件冲突
      setTimeout(() => {
        isInserting.value = false;
      }, 300);
    }
  };
  
  /**
   * 处理全局参数插入事件
   * @param {Object} data 事件数据
   */
  const handleParamInsertEvent = (data) => {
    // 检查是否应该处理此事件
    if (!isActive.value) return;
    
    const target = getTarget();
    if (!target) return;
    
    // 如果指定了目标元素且与当前元素匹配，或未指定目标元素
    if (!data.targetId || data.targetId === target.id) {
      insertParam({
        paramId: data.paramId,
        panelId: data.panelId || getPanelId()
      });
    }
  };
  
  /**
   * 设置焦点到目标元素
   */
  const focus = () => {
    const target = getTarget();
    if (!target) return;
    
    // 确保元素可编辑
    if (target.contentEditable !== 'true') {
      target.contentEditable = 'true';
    }
    
    // 聚焦元素
    target.focus();
    
    // 移动光标到末尾
    const range = document.createRange();
    range.selectNodeContents(target);
    range.collapse(false);
    
    const selection = window.getSelection();
    selection.removeAllRanges();
    selection.addRange(range);
  };
  
  // 在组件挂载时注册事件监听
  onMounted(() => {
    isActive.value = true;
    
    // 监听全局参数插入事件
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.on('param-insert-requested', handleParamInsertEvent);
    }
    
    // 自动聚焦
    if (options.autoFocus) {
      setTimeout(focus, 100);
    }
  });
  
  // 在组件卸载前清理
  onBeforeUnmount(() => {
    isActive.value = false;
    
    // 移除事件监听
    if (window.PARAM_EVENT_BUS) {
      window.PARAM_EVENT_BUS.off('param-insert-requested', handleParamInsertEvent);
    }
  });
  
  // 返回功能和状态
  return {
    insertParam,        // 插入参数方法
    focus,              // 聚焦方法
    isInserting,        // 是否正在插入
    isActive,           // 是否激活
    
    // 计算属性
    targetElement: computed(() => getTarget()),
    currentPanelId: computed(() => getPanelId())
  };
} 