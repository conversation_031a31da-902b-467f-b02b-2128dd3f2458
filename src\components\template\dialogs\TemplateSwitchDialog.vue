<template>
  <el-dialog
    v-model="dialogVisible"
    title="提示"
    width="28%"
    :show-close="true"
    align-center
    class="switch-template-dialog"
    @closed="onDialogClosed"
  >
    <div class="switch-dialog-content">
      <el-icon class="warning-icon"><WarningFilled /></el-icon>
      <span class="warning-text">确认选择新的版式吗，若选择新的版式，已经编辑的内容将会被清空哦!</span>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel']);

// 计算属性：对话框可见性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 处理确认按钮点击
const handleConfirm = () => {
  try {
    emit('confirm');
  } catch (error) {
    console.error('确认切换模板时出错:', error);
  } finally {
    dialogVisible.value = false;
  }
};

// 处理取消按钮点击
const handleCancel = () => {
  try {
    emit('cancel');
  } catch (error) {
    console.error('取消切换模板时出错:', error);
  } finally {
    dialogVisible.value = false;
  }
};

// 处理对话框关闭
const onDialogClosed = () => {
  // 确保已更新了模型值
  if (dialogVisible.value !== false) {
    emit('update:modelValue', false);
  }
};
</script>

<style scoped lang="scss">
.switch-dialog-content {
  display: flex;
  align-items: center;
  padding: 20px 0 0;
}

.warning-icon {
  font-size: 24px;
  color: #e6a23c;
  margin-right: 15px;
}

.warning-text {
  font-size: 14px;
  line-height: 1.5;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 