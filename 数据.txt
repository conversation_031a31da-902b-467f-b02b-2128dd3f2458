[{"contents": [{"type": "image", "src": "/aim_files/A173440270073611/M175100926233510024.jpeg", "positionNumber": 1, "actionType": "OPEN_POPUP", "actionJson": {"target": "t1", "content": "t2", "textButton": "t3", "mode": 0}}, {"type": "text", "content": "编辑文本，最多12个中文字", "positionNumber": 2, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多17个中文字", "positionNumber": 3, "isTextTitle": 0}, {"type": "text", "content": "99", "positionNumber": 4, "isTextTitle": 1, "aimCurrencyDisplay": 1}, {"type": "text", "content": "编辑文本，最多11个字", "positionNumber": 5, "isTextTitle": 1}, {"type": "text", "content": "编辑文本，最多32个中文字;编辑文本，最多32个中文字", "positionNumber": 6, "isTextTitle": 0}, {"type": "button", "content": "领", "positionNumber": 7, "isTextTitle": 0, "actionType": "OPEN_SCHEDULE", "actionJson": {"target": "r1", "description": "r2", "beginTime": "2025-07-01 00:00:00", "endTime": "2025-07-30 00:00:00"}}, {"type": "image", "src": "/aim_files/A173440270073611/M175100923797710022.png", "positionNumber": 8}, {"type": "text", "content": "编辑文本，最多28个中文字", "positionNumber": 9, "isTextTitle": 1}, {"type": "text", "content": "编辑文本", "positionNumber": 10, "isTextTitle": 0}, {"type": "text", "content": "100.00", "positionNumber": 11, "isTextTitle": 0, "aimCurrencyDisplay": 1}, {"type": "button", "content": "立即购买", "positionNumber": 12, "isTextTitle": 0, "actionType": "OPEN_EMAIL", "actionJson": {"target": "<EMAIL>", "subject": "", "body": "b2"}}, {"type": "image", "src": "/aim_files/A173440270073611/M175100922626810021.jpeg", "positionNumber": 13}, {"type": "text", "content": "编辑文本，最多28个中文字", "positionNumber": 14, "isTextTitle": 1}, {"type": "text", "content": "编辑文本", "positionNumber": 15, "isTextTitle": 0}, {"type": "text", "content": "100.00", "positionNumber": 16, "isTextTitle": 0, "aimCurrencyDisplay": 1}, {"type": "button", "content": "立即购买", "positionNumber": 17, "isTextTitle": 0, "actionType": "COPY_PARAMETER", "actionJson": {"target": "f1"}}, {"type": "image", "src": "/aim_files/A173440270073611/M175100920182710019.png", "positionNumber": 18}, {"type": "text", "content": "编辑文本，最多28个中文字", "positionNumber": 19, "isTextTitle": 1}, {"type": "text", "content": "编辑文本", "positionNumber": 20, "isTextTitle": 0}, {"type": "text", "content": "100.00", "positionNumber": 21, "isTextTitle": 0, "aimCurrencyDisplay": 1}, {"type": "button", "content": "立即购买", "positionNumber": 22, "isTextTitle": 0, "actionType": "OPEN_POPUP", "actionJson": {"target": "t5", "content": "t6", "textButton": "", "mode": 0}}]}]