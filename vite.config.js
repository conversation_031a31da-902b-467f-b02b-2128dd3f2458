import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

import path from 'path';
import { readdirSync } from 'fs';
// 获取所有页面的配置
function getPages() {
  const pages = {};
  const pagesDir = path.resolve(__dirname, 'src/pages');
  try {
    const pageDirs = readdirSync(pagesDir);
    pageDirs.forEach((dir) => {
      pages[dir] = path.join(__dirname, 'src', 'pages', dir, 'index.html');
    });
  } catch (error) {
    console.error(`Error reading pages directory: ${error}`);
    return {}; // 返回空对象
  }
  return pages;
}
export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  plugins: [
    vue(),
    AutoImport({
      imports: [
          'vue',
          'vue-router',
          'pinia',
          {
              axios: [
                  ['default', 'axios'], // 导入 axios 并将其默认导出重命名为 axios
              ],
          },
      ],
      resolvers: [
        ElementPlusResolver(),
        // 自动导入图标组件
        // IconsResolver({
        //   prefix: 'Icon',
        // }),
      ], // 导入 Element Plus函数，如：ElMessage, ElMessageBox 等
      eslintrc: {
          enabled: true,
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true,
      },
      vueTemplate: true,
      // 导入函数类型声明文件路径 (false:关闭自动生成)
      // dts: false,
      dts: 'src/types/auto-imports.d.ts' // 生成自动导入的类型声明文件
    }),
    Components({
      resolvers: [ElementPlusResolver()], // 导入 Element Plus 组件
      // 指定自定义组件位置(默认:src/components)
      dirs: [
          'src/components',
          'src/**/components',
          'src/layout/components',
          'src/views/**/components',
          'src/views/',
      ],
      // 导入组件类型声明文件路径 (false:关闭自动生成)
      // dts: false,
      dts: "src/types/components.d.ts",
    }),
  ],
  build: {
    // target: 'es2015', // 指定打包的目标浏览器版本
    target: 'esnext',
    outDir: 'dist', // 输出目录
    chunkSizeWarningLimit: 2000,
    minify: 'terser',
    terserOptions: {
      compress: {
        keep_infinity: true, // 防止 Infinity 被压缩成 1/0，这可能会导致 Chrome 上的性能问题
        drop_console: true, // 生产环境去除 console
        drop_debugger: true, // 生产环境去除 debugger
      },
      format: {
        comments: false, // 删除注释
      },
    },
    cssCodeSplit: false, // 是否启用 CSS 代码分割
    emptyOutDir: true, // 在构建之前清空输出目录
    sourcemap: false, // 禁用 Source Map 文件生成
    rollupOptions: {
      // input: getPages(),
      input:{
        index: path.resolve(__dirname, 'src/pages/template/index.html'), 
      },
      output: {
        format: 'iife',
        // format: 'es',
        entryFileNames: 'js/[name].[hash].js',
        chunkFileNames: 'js/[name].[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          let extType = info[info.length - 1];
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'media';
          } else if (/\.(png|jpe?g|gif|svg)(\?.*)?$/.test(assetInfo.name)) {
            extType = 'images';
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
            extType = 'fonts';
          }
          return `${extType}/[name].[hash].[ext]`;
        },
      },
    },
  },
  server: {
    open: true,
    proxy: {
      // 接口代理，以 /api 开头的请求都会被代理到目标服务器
      '/api': {
        target: 'http://58.246.39.26:23451',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        ws: true, // 支持 WebSocket
        // followRedirects: false, // 禁止重定向
        configure: (proxy, options) => {
          // 添加请求头
          proxy.on('proxyReq', (proxyReq) => {
            // console.log('proxyReq', proxyReq);
            proxyReq.setHeader('Authorization','eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiIxIiwiYXVkIjoiMSIsImp0aSI6IjEiLCJzdWIiOiIxLDQxLDQyLDQzLDQ0LDQ1LDQ2LDQ3LDQ4LDQ5LDUwLDUxLDUyLDUzLDU0LDU1LDU2LDU3LDU4LDU5LDYwLDYxLDYyLDYzLDY0LDY1LDY2LDY3LDY4LDY5LDcwLDcxLDcyLDczLDc0LDc1LDc2LDc3LDc4LDc5LDgwLDgxLDgyLDgzLDg0LDg1LDg2LDg3LDg4LDg5LDkwLDkxLDkyLDkzLDk0LDk1LDk2LDk3LDk4IiwiaWF0IjoxNzUzNzUyNTQzLCJleHAiOjE3NTM3NjY5NDN9.bACk6sn31hOOyNA7a1cnZnlUhdSlqWN9zembnmloMspj3mUkjuTRTzxwRGrFmf3L7KxvtD3z9uYt0-YWi7F2SQ');
            proxyReq.setHeader('Custom-Header', 'Custom-Value');
            // 从请求头获取 Authorizatio
            // const token = req.headers.authorization;
            // if (token) {
            //     proxyReq.setHeader('Authorization', token);
            // }
          });
        }
      }
    }
  }
});
