<template>
    <div class="horizontal-swipe-settings">
      <!-- 样式选择 -->
      <!-- <div class="setting-section">
        <h4 class="section-title">样式选择</h4>
        <div class="style-options">
          <div 
            v-for="style in styleOptions" 
            :key="style.id"
            class="style-option"
            :class="{ 'is-selected': selectedStyle === style.id }"
            @click="handleStyleChange(style.id)"
          >
            <img 
              :src="getStyleImage(style.image)" 
              :alt="style.name"
              class="style-image"
            />
            <span class="style-name">{{ style.name }}</span>
          </div>
        </div>
      </div> -->
  
      <!-- 横滑卡片数量控制 -->
      <div class="setting-section">
        <h4 class="section-title">横滑卡片</h4>
        <div class="card-controls">
          <!-- 减号按钮 -->
          <button 
            class="control-btn minus-btn" 
            :disabled="cardCount <= 2"
            @click="removeCard"
          >
            −
          </button>
          
          <!-- 数字按钮组（指示器） -->
          <div class="number-buttons">
            <button
              v-for="(card, index) in pages"
              :key="index"
              :class="['number-btn', { active: currentCardIndex === index }]"
              @click="selectCard(index)"
            >
              {{ index + 1 }}
            </button>
          </div>
          
          <!-- 加号按钮 -->
          <button 
            class="control-btn plus-btn" 
            :disabled="cardCount >= 10"
            @click="addCard"
          >
            +
          </button>
        </div>
  
        <!-- 卡片图片预览和上传 -->
        <div class="card-image-container">
          <ImageElement
            v-if="currentCardImageContent"
            :content="currentCardImageContent"
            :is-selected="false"
            :usage="'settings'"
            :get-media-url="getMediaUrl"
            class="settings-image"
            @select="handleImageSelect"
          />
          <div v-else class="no-image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无图片</span>
          </div>
          <div class="setting-desc" v-if="currentCardImageContent && currentCardImageContent.content">
            {{ currentCardImageContent.content }}
          </div>
          <el-button
            type="primary"
            class="select-btn"
            @click="selectImageFile"
          >
            选择图片
          </el-button>
        </div>
      </div>
  
      <!-- 标题编辑 -->
      <div class="setting-section setting-card">
        <h4 class="section-title">
          <i>*</i> 标题：
        </h4>
        <RichParamInput
          v-model="currentCardTitle"
          @input="handleCardContentChange"
        />
      </div>
  
      <!-- 内容编辑 -->
      <div class="setting-section setting-card">
        <h4 class="section-title">
          <i>*</i>内容：
        </h4>
        <RichParamInput
          v-model="currentCardContent"
          @input="handleCardContentChange"
        />
      </div>
      <!-- 按钮名称编辑 -->
      <div class="setting-section setting-card">
        <h4 class="section-title">
          按钮名称：<span>（最多9位）</span>
        </h4>
        <RichParamInput
          v-model="currentCardButtonText"
          @input="handleCardContentChange"
        />
      </div>
      <!-- 点击事件配置 -->
      <div class="setting-section setting-card">
        <ClickEventSettings 
          :content="currentCard.clickEvent"
          @update:content="handleClickEventUpdate"
          @insert-param="handleParamInsert"
          @manage-param="handleParamManage"
          @field-change="handleClickEventFieldChange"
        />
      </div>
    </div>
  
    <!-- 媒体选择弹框 -->
    <MediaSelectorDialog 
      v-model="dialogVisible"
      :mediaType="dialogMediaType"
      @confirm="handleMediaSelect"
      :filter-app-key="appKey"
    />
  </template>
  
  <script setup>
  import { ref, computed, watch, nextTick, onMounted, inject, onBeforeUnmount, getCurrentInstance } from 'vue';
  import { Picture, Plus, Minus } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';
  import MediaSelectorDialog from '@/components/MediaSelectorDialog.vue';
  import ClickEventSettings from '@/components/settings/ClickEventSettings.vue';
  import RichParamInput from '@/components/richtext/RichParamInput.vue';
  import { getMediaUrl } from '@/utils/mediaUtils';
  import ImageElement from '@/components/template/preview/elements/ImageElement.vue';
  import { CLICK_EVENT_TYPES, ClickEventTypeConverter, ActionJsonGenerator } from '@/utils/clickEventManager.js';
  import HorizontalSwipeTemplate from '@/components/template/types/HorizontalSwipeTemplate.js';
  
  // 存储卡片的初始状态，用于内容变化比较
  const cardInitialStates = ref(new Map());
  
  const props = defineProps({
    content: {
      type: Object,
      default: () => ({})
    },
    template: {
      type: Object,
      default: () => null
    },
    appKey: {
      type: String,
      required: true
    }

  });
  
  const emit = defineEmits(['change', 'settings-change', 'insert-param', 'manage-param', 'add-card', 'remove-card', 'update-card-image', 'update-card-content', 'card-selected']);
  
  // 响应式数据
  const dialogVisible = ref(false);
  const dialogMediaType = ref('image');
  const currentCardIndex = ref(0);
  const selectedStyle = ref('style1');
  
  // 样式选项
  const styleOptions = ref([
    {
      id: 'style1',
      name: '横滑组件1',
      image: '/aim_files/aim_defult/sideslip_1.png'
    },
    {
      id: 'style2',
      name: '横滑组件2',
      image: '/aim_files/aim_defult/sideslip_2.png'
    }
  ]);
  
  // 使用inject来获取当前模板，这样可以保证响应式
  const currentTemplate = inject('currentTemplate', null);
  
  // 1. 新增 pages computed，始终返回 currentTemplate.value.pages 的数组
  const pages = computed(() => {
    if (!currentTemplate || !currentTemplate.value) return [];
    let p = currentTemplate.value.pages;
    if (typeof p === 'string') {
      try { p = JSON.parse(p); } catch { return []; }
    }
    return Array.isArray(p) ? p : [];
  });

  // 2. currentCard 直接依赖 pages
  const currentCard = computed(() => {
    const card = pages.value[currentCardIndex.value] || {};

    // 创建卡片的深拷贝，避免修改原始数据
    const cardCopy = JSON.parse(JSON.stringify(card));

    // 统一 clickEvent 兜底和补全逻辑
    const requiredFields = {
      type: 'OPEN_BROWSER',
      actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
      url: '',
      packageName: '',
      floorType: '0',
      phone: '',
      text: '',
      app: '',
      quick: '',
      email: '',
      emailAddress: '',
      emailSubject: '',
      emailBody: '',
      schedule: '',
      scheduleStartTimeString: '',
      scheduleEndTimeString: '',
      popup: '',
      popupTitle: '',
      popupContent: '',
      popupButtonText: '',
      copyType: '1',
      selectedParamId: '',
      fixedContent: ''
    };

    // 关键修改：只在深拷贝中处理clickEvent，不修改原始数据
    if (!cardCopy.clickEvent) {
      // 只有在完全没有 clickEvent 时才创建新的
      cardCopy.clickEvent = { ...requiredFields };
      console.log('[currentCard] 创建新的 clickEvent:', cardCopy.clickEvent);
    } else {
      // 更智能的字段补全：只补全缺失的字段，不覆盖已有值
      let hasChanges = false;
      Object.keys(requiredFields).forEach(field => {
        if (cardCopy.clickEvent[field] === undefined) {
          cardCopy.clickEvent[field] = requiredFields[field];
          hasChanges = true;
        }
      });
      
      // 确保 actionType 和 type 字段同步 - 优先使用 actionType
      if (cardCopy.clickEvent.actionType && (!cardCopy.clickEvent.type || cardCopy.clickEvent.type === 'OPEN_BROWSER')) {
        // 如果有 actionType，优先使用它来设置 type
        const newType = ClickEventTypeConverter.toClickEventType(cardCopy.clickEvent.actionType);
        if (cardCopy.clickEvent.type !== newType) {
          cardCopy.clickEvent.type = newType;
          hasChanges = true;
        }
      } else if (cardCopy.clickEvent.type && !cardCopy.clickEvent.actionType) {
        // 只有在没有 actionType 的情况下，才使用 type 来设置 actionType
        const newActionType = ClickEventTypeConverter.toActionType(cardCopy.clickEvent.type);
        if (cardCopy.clickEvent.actionType !== newActionType) {
          cardCopy.clickEvent.actionType = newActionType;
          hasChanges = true;
        }
      }
      
      if (hasChanges) {
        console.log('[currentCard] 补全了 clickEvent 字段:', cardCopy.clickEvent);
      }
    }

    console.log('[currentCard] 最终返回的卡片clickEvent:', cardCopy.clickEvent);
    return cardCopy;
  });
    
  // 3. cardCount 直接用 pages
  const cardCount = computed(() => pages.value.length);

  // 4. currentCardImageContent 直接用 currentCard
  const currentCardImageContent = computed(() => {
    const card = currentCard.value;
    const imageContent = (card.contents || card.content || []).find(c => c.type === 'image');
    if (!imageContent || !imageContent.src) return null;
    return {
      contentId: `horizontal-swipe-image-${currentCardIndex.value}`,
      type: 'image',
      src: imageContent.src,
      content: imageContent.content || imageContent.alt || `图片${currentCardIndex.value + 1}`,
      alt: imageContent.alt || `图片${currentCardIndex.value + 1}`
    };
  });
  
  // 5. 新增：从contents数组中获取标题、内容、按钮文本的computed属性
  const currentCardTitle = computed({
    get() {
      const card = currentCard.value;
      const titleContent = (card.contents || []).find(c => c.type === 'text' && c.isTextTitle === 1);
      return titleContent ? titleContent.content : '';
    },
    set(value) {
      // 获取原始数据，避免使用被 currentCard computed 修改过的数据
      const originalCard = pages.value[currentCardIndex.value];
      if (!originalCard || !originalCard.contents) return;
      const titleContent = originalCard.contents.find(c => c.type === 'text' && c.isTextTitle === 1);
      if (titleContent) {
        titleContent.content = value;
        handleCardContentChange();
      }
    }
  });

  const currentCardContent = computed({
    get() {
      const card = currentCard.value;
      const contentText = (card.contents || []).find(c => c.type === 'text' && c.isTextTitle === 0);
      return contentText ? contentText.content : '';
    },
    set(value) {
      // 获取原始数据，避免使用被 currentCard computed 修改过的数据
      const originalCard = pages.value[currentCardIndex.value];
      if (!originalCard || !originalCard.contents) return;
      const contentText = originalCard.contents.find(c => c.type === 'text' && c.isTextTitle === 0);
      if (contentText) {
        contentText.content = value;
        handleCardContentChange();
      }
    }
  });

  const currentCardButtonText = computed({
    get() {
      const card = currentCard.value;
      const buttonContent = (card.contents || []).find(c => c.type === 'button');
      return buttonContent ? buttonContent.content : '';
    },
    set(value) {
      // 获取原始数据，避免使用被 currentCard computed 修改过的数据
      const originalCard = pages.value[currentCardIndex.value];
      if (!originalCard || !originalCard.contents) return;
      const buttonContent = originalCard.contents.find(c => c.type === 'button');
      if (buttonContent) {
        buttonContent.content = value;
        handleCardContentChange();
      }
    }
  });
  // 获取样式图片路径
  const getStyleImage = (imagePath) => {
    try {
      return getMediaUrl(imagePath);
    } catch (error) {
      return '/aim_files/aim_defult/simple.png';
    }
  };
  
  // 处理样式变化
  const handleStyleChange = (styleId) => {
    selectedStyle.value = styleId;
    emitSettingsChange();
  };
  
  // 选择卡片
  const selectCard = (index) => {
    console.log('点击的数字按钮索引:', index);
    console.log('当前 currentCardIndex.value:', currentCardIndex.value);
    
    // 检查是否已经选中了相同的卡片
    if (currentCardIndex.value === index) {
      console.log('卡片已经被选中，但仍需要确保预览区域同步');
      // 即使是同一个卡片，也要发送事件确保预览区域同步和滚动
      emit('card-selected', {
        cardIndex: index,
        selectedCardIndex: index,
        forceScroll: true  // 添加强制滚动标记
      });
      console.log('=== HorizontalSwipeSettings.selectCard 结束（重复选择但强制同步） ===');
      return;
    }
    
    console.log('切换卡片选择，从', currentCardIndex.value, '到', index);
    
    // ===== 关键修改：在切换前先强制保存当前卡片的所有数据 =====
    // 获取原始数据，避免使用被 currentCard computed 修改过的数据
    const originalCurrentCard = pages.value[currentCardIndex.value];
    if (originalCurrentCard) {
      console.log('切换前保存当前卡片原始数据:', originalCurrentCard);
      console.log('切换前当前卡片的原始 clickEvent:', originalCurrentCard.clickEvent);
      
      // 立即触发数据保存 - 传递整个卡片对象
      emit('update-card-content', {
        cardIndex: currentCardIndex.value,
        contentData: JSON.parse(JSON.stringify(originalCurrentCard)) // 使用深拷贝确保数据完整性
      });
      
      // 立即保存设置变化
      const settings = {
        style: selectedStyle.value,
        cards: pages.value,
        currentCardIndex: currentCardIndex.value
      };
      emit('change', settings);
      emit('settings-change', settings);
      
      console.log('切换前已保存当前卡片数据');
    }
    
    // 切换到新卡片前，先保存新卡片的初始状态
    const cards = pages.value;
    if (cards && cards[index]) {
      console.log('切换前新卡片的原始数据:', cards[index]);
      console.log('切换前新卡片的原始 clickEvent:', cards[index].clickEvent);
      saveCardInitialState(index, cards[index]);
      console.log('保存新选中卡片的初始状态:', index);
    }
    
    // 强制保存当前设置
    forceSaveSettings();
    
    // 切换索引
    currentCardIndex.value = index;
    
    // 立即发出设置变化事件，通知父组件更新选中状态
    nextTick(() => {
      console.log('发出设置变化事件');
      emitSettingsChange();
      
      // 通知父组件当前选中的卡片索引发生了变化
      // 这样预览区域可以同步更新选中状态和滚动位置
      console.log('发出card-selected事件，索引:', index);
      emit('card-selected', {
        cardIndex: index,
        selectedCardIndex: index,
        fromSettingsPanel: true  // 标记来源于设置面板
      });
      console.log('=== HorizontalSwipeSettings.selectCard 结束 ===');
    });
  };
  
  // 添加卡片
  const addCard = () => {
    if (cardCount.value >= 10) return;
    
    // 计算新卡片的索引（即将被添加的卡片的索引）
    const newCardIndex = cardCount.value;
    
    console.log('addCard: 准备添加新卡片，索引:', newCardIndex);
    
    // 生成唯一的contentId
    const generateContentId = () => {
      return Date.now() + Math.floor(Math.random() * 1000);
    };
    
    // 构造和接口返回数据结构完全一致的卡片数据
    const defaultCardData = {
      pageId: newCardIndex + 4, // 接口中pageId从4开始
      contents: [
        {
          contentId: generateContentId(),
          pageId: newCardIndex + 4,
          templateId: 4,
          type: "image",
          content: "该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内",
          srcType: null,
          src: "/aim_files/aim_defult/defaultImg.jpg",
          isTextTitle: 0,
          actionType: null,
          positionNumber: 1,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: "center",
          pageLines: 1,
          auditState: null,
          actionJson: { target: '' }
        },
        {
          contentId: generateContentId(),
          pageId: newCardIndex + 4,
          templateId: 4,
          type: "text",
          content: "编辑标题，最多显示13个字",
          srcType: null,
          src: null,
          isTextTitle: 1,
          actionType: null,
          positionNumber: 2,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: "left",
          pageLines: 2,
          auditState: null,
          actionJson: null
        },
        {
          contentId: generateContentId(),
          pageId: newCardIndex + 4,
          templateId: 4,
          type: "text",
          content: "编辑文本，最多显示30个字。编辑文本，最多显示30个字。",
          srcType: null,
          src: null,
          isTextTitle: 0,
          actionType: null,
          positionNumber: 3,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: "left",
          pageLines: 3,
          auditState: null,
          actionJson: null
        },
        {
          contentId: generateContentId(),
          pageId: newCardIndex + 4,
          templateId: 4,
          type: "button",
          content: "编辑按钮",
          srcType: null,
          src: null,
          isTextTitle: 0,
          actionType: null,
          positionNumber: 4,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: "center",
          pageLines: 4,
          auditState: null,
          actionJson: { target: '' }
        }
      ],
      clickEvent: {
        type: CLICK_EVENT_TYPES.OPEN_BROWSER,
        actionType: CLICK_EVENT_TYPES.OPEN_BROWSER,
        url: '',
        packageName: '',
        floorType: '0',
        phone: '',
        text: '',
        app: '',
        quick: '',
        email: '',
        emailAddress: '',
        emailSubject: '',
        emailBody: '',
        schedule: '',
        scheduleStartTimeString: '',
        scheduleEndTimeString: '',
        popup: '',
        popupTitle: '',
        popupContent: '',
        popupButtonText: '',
        copyType: '1',
        selectedParamId: '',
        fixedContent: ''
      }
    };
    
    // 触发添加新页面的事件，让父组件处理
    emit('add-card', {
      cardIndex: newCardIndex,
      defaultCard: defaultCardData
    });
    
    // 设置当前选中的卡片索引为新增的卡片
    currentCardIndex.value = newCardIndex;
    
    console.log('addCard: 设置currentCardIndex为:', newCardIndex);
    
    // 保存新卡片的初始状态
    console.log('addCard: 保存新卡片的初始状态');
    saveCardInitialState(newCardIndex, defaultCardData);
    
    // 等待较短时间，确保模板数据已经更新
    setTimeout(() => {
      console.log('addCard: 延迟后发出设置变化和卡片选择事件');
      
      // 发出设置变化事件
      emitSettingsChange();
      
      // 通知父组件选中新增的卡片，让预览区域滚动到新增的卡片并显示选中状态
      // 关键修复：确保传递正确的索引，并标记这是新增卡片操作
      emit('card-selected', {
        cardIndex: newCardIndex,
        selectedCardIndex: newCardIndex,
        isNewCard: true  // 添加标记，表示这是新增卡片
      });
      
      console.log('addCard: 已发出card-selected事件，索引:', newCardIndex);
    }, 150); // 减少延迟时间
  };
  
  // 添加防重复触发的状态标记
  const isEmittingSettings = ref(false);
  const isHandlingContentChange = ref(false);
  
  // 处理卡片内容变化 - 移除防抖机制，让输入立即生效
  const handleCardContentChange = () => {
    // 防止重复触发，避免死循环
    if (isHandlingContentChange.value) {
      return;
    }

    // 获取原始数据，避免使用被 currentCard computed 修改过的数据
    const originalCard = pages.value[currentCardIndex.value];
    if (!originalCard) {
      console.warn('handleCardContentChange: 当前卡片不存在');
      return;
    }
    
    console.log('当前卡片原始数据:', originalCard);
    
    // 设置标记，防止重复调用
    isHandlingContentChange.value = true;
    
    try {
      // 从contents数组中获取当前值
      const titleContent = (originalCard.contents || []).find(c => c.type === 'text' && c.isTextTitle === 1);
      const contentText = (originalCard.contents || []).find(c => c.type === 'text' && c.isTextTitle === 0);
      const buttonContent = (originalCard.contents || []).find(c => c.type === 'button');
      
      const currentState = {
        title: String(titleContent?.content || '').trim(),
        content: String(contentText?.content || '').trim(),
        buttonText: String(buttonContent?.content || '').trim()
      };
      
      // 获取当前卡片的初始状态进行比较
      const initialState = getCardInitialState(currentCardIndex.value);
    
      // 如果有初始状态，进行比较
      if (initialState) {
        if (initialState.title === currentState.title && 
            initialState.content === currentState.content && 
            initialState.buttonText === currentState.buttonText) {
          console.log('内容相对于初始状态没有变化，跳过更新事件');
          console.log('比较结果:', {
            title: { initial: initialState.title, current: currentState.title },
            content: { initial: initialState.content, current: currentState.content },
            buttonText: { initial: initialState.buttonText, current: currentState.buttonText }
          });
          return;
        }
        
        console.log('检测到内容相对于初始状态发生变化:', {
          title: { initial: initialState.title, current: currentState.title },
          content: { initial: initialState.content, current: currentState.content },
          buttonText: { initial: initialState.buttonText, current: currentState.buttonText }
        });
      } else {
        console.log('没有找到初始状态，认为是首次变化');
      }
    
      // 立即触发更新卡片内容的事件 - 传递整个card对象
      emit('update-card-content', {
        cardIndex: currentCardIndex.value,
        contentData: JSON.parse(JSON.stringify(originalCard))
      });
    
      // 保存当前状态作为新的初始状态
      saveCardInitialState(currentCardIndex.value, currentState);
    
    } finally {
      // 延迟重置标记
      setTimeout(() => {
        isHandlingContentChange.value = false;
      }, 100);
    }
  };
  
  // 处理点击事件更新 
  const handleClickEventUpdate = (updatedClickEvent) => {
    try {
      console.log('HorizontalSwipeSettings - 接收到点击事件更新:', updatedClickEvent);
      
      // 获取原始数据，避免使用被 currentCard computed 修改过的数据
      const originalCard = pages.value[currentCardIndex.value];
      if (!originalCard) {
        console.warn('HorizontalSwipeSettings - 没有选中的卡片');
        return;
      }

      // 使用横滑模板的标准方法更新clickEvent
      console.log('HorizontalSwipeSettings - 更新前的卡片数据:', originalCard);
      console.log('HorizontalSwipeSettings - 更新前的clickEvent:', originalCard.clickEvent);

      // 保存原有的重要字段
      const originalClickEvent = originalCard.clickEvent || {};
      
      // 智能合并：保留原有字段，应用新的更新
      const mergedClickEvent = {
        ...originalClickEvent,  // 保留原有所有字段
        ...updatedClickEvent,   // 应用更新
        // 确保关键字段不会意外丢失，优先使用更新的值
        actionType: updatedClickEvent.actionType || originalClickEvent.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER,
        type: updatedClickEvent.type || originalClickEvent.type || CLICK_EVENT_TYPES.OPEN_BROWSER
      };

      console.log('HorizontalSwipeSettings - 合并后的clickEvent:', mergedClickEvent);

      // 使用横滑模板的标准方法设置clickEvent
      HorizontalSwipeTemplate.setClickEvent(originalCard, mergedClickEvent);
      
      // 生成actionJson
      originalCard.actionJson = HorizontalSwipeTemplate.buildActionJson(originalCard);
      
      console.log('HorizontalSwipeSettings - 设置后的卡片clickEvent:', originalCard.clickEvent);
      console.log('HorizontalSwipeSettings - 生成的actionJson:', originalCard.actionJson);

      // 通知父组件更新卡片内容 - 使用深拷贝确保数据完整性
      const cardDataToSave = JSON.parse(JSON.stringify(originalCard));
      console.log('HorizontalSwipeSettings - 准备发送的卡片数据:', cardDataToSave);
      console.log('HorizontalSwipeSettings - 准备发送的 clickEvent:', cardDataToSave.clickEvent);
      
      emit('update-card-content', {
        cardIndex: currentCardIndex.value,
        contentData: cardDataToSave
      });
      
      console.log('HorizontalSwipeSettings - 已发出update-card-content事件');

    } catch (error) {
      console.error('HorizontalSwipeSettings - 处理点击事件更新时出错:', error);
    }
  };
  
  // 处理参数插入
  const handleParamInsert = (param) => {
    // 实现参数插入逻辑
  };
  
  // 处理参数管理
  const handleParamManage = (param) => {
    // 实现参数管理逻辑
  };
  
  // 新增：处理点击事件内容输入的field-change事件
  const handleClickEventFieldChange = ({ field, value }) => {
    // 获取原始数据，避免使用被 currentCard computed 修改过的数据
    const originalCard = pages.value[currentCardIndex.value];
    if (!originalCard || !originalCard.clickEvent) return;
    
    originalCard.clickEvent[field] = value;

    // 只同步到button内容，不处理image
    const buttonContent = (originalCard.contents || []).find(c => c.type === 'button');
    if (buttonContent) {
      buttonContent.actionType = originalCard.clickEvent.actionType;
      buttonContent.actionJson = HorizontalSwipeTemplate.buildActionJson(originalCard);
    }

    // 使用深拷贝确保数据完整性
    const cardDataToSave = JSON.parse(JSON.stringify(originalCard));
    console.log('handleClickEventFieldChange - 准备发送的卡片数据:', cardDataToSave);
    console.log('handleClickEventFieldChange - 准备发送的 clickEvent:', cardDataToSave.clickEvent);

    emit('update-card-content', {
      cardIndex: currentCardIndex.value,
      contentData: cardDataToSave
    });
  };
  
  // 发出设置变化事件
  const emitSettingsChange = () => {
    // 防止重复触发，避免死循环
    if (isEmittingSettings.value) {
      console.log('emitSettingsChange: 检测到重复调用，跳过以防止死循环');
      return;
    }

    console.log('=== HorizontalSwipeSettings.emitSettingsChange 开始 ===');
    console.log('当前选中卡片索引:', currentCardIndex.value);
    console.log('横滑卡片数据:', pages.value);
    console.log('当前卡片数据:', currentCard.value);
    console.log('当前卡片点击事件:', currentCard.value?.clickEvent);
    
    // 设置标记，防止重复调用
    isEmittingSettings.value = true;
    
    const settings = {
      style: selectedStyle.value,
      cards: pages.value,
      currentCardIndex: currentCardIndex.value
    };
    
    console.log('即将发送的settings数据:', settings);
    console.log('准备发送change和settings-change事件');
    
    try {
    emit('change', settings);
    emit('settings-change', settings);
    
    console.log('=== HorizontalSwipeSettings.emitSettingsChange 结束 ===');
    } finally {
      // 延迟重置标记，给事件处理留出时间
      setTimeout(() => {
        isEmittingSettings.value = false;
      }, 100);
    }
  };
  
  // 强制保存当前所有设置数据
  const forceSaveSettings = () => {
    console.log('横滑设置：收到强制保存请求');
    
    // 立即触发设置同步
    // 获取原始数据，避免使用被 currentCard computed 修改过的数据
    const originalCard = pages.value[currentCardIndex.value];
    if (originalCard) {
      console.log('强制保存：立即同步当前卡片原始数据', originalCard);
      console.log('强制保存：当前卡片的原始 clickEvent:', originalCard.clickEvent);
      
      // 触发内容更新事件 - 传递整个卡片对象
      const cardDataToSave = JSON.parse(JSON.stringify(originalCard));
      console.log('强制保存：准备发送的卡片数据:', cardDataToSave);
      console.log('强制保存：准备发送的 clickEvent:', cardDataToSave.clickEvent);
      
      emit('update-card-content', {
        cardIndex: currentCardIndex.value,
        contentData: cardDataToSave
      });
      
      // 立即发送设置变更事件
      emitSettingsChange();
    } else {
      console.warn('强制保存：当前卡片不存在');
    }
    
    console.log('横滑设置：强制保存完成');
  };
  
  // 初始化设置（从props.content中获取现有设置）
  const initializeSettings = (shouldEmitEvents = false) => {
    console.log('=== HorizontalSwipeSettings.initializeSettings 开始 ===');
    console.log('shouldEmitEvents:', shouldEmitEvents);
    console.log('props.content:', props.content);
    console.log('当前 currentCardIndex.value:', currentCardIndex.value);
    
    // 记录是否有任何更新
    let hasUpdated = false;
    
    if (props.content) {
      // 从横滑设置中获取当前选中的卡片索引
      if (typeof props.content.selectedCardIndex === 'number') {
        console.log('发现 props.content.selectedCardIndex:', props.content.selectedCardIndex);
        // 强制同步到传入的索引，确保设置面板与预览区域一致
        if (currentCardIndex.value !== props.content.selectedCardIndex) {
          console.log('强制同步 currentCardIndex 从', currentCardIndex.value, '到', props.content.selectedCardIndex);
          currentCardIndex.value = props.content.selectedCardIndex;
          hasUpdated = true;
        } else {
          console.log('currentCardIndex 已经是', props.content.selectedCardIndex, '，无需更新');
        }
      }
      
      // 如果有保存的横滑设置，使用它们
      if (props.content.horizontalSwipeSettings) {
        console.log('发现 props.content.horizontalSwipeSettings:', props.content.horizontalSwipeSettings);
        const settings = props.content.horizontalSwipeSettings;
        selectedStyle.value = settings.style || 'style1';
        if (typeof settings.currentCardIndex === 'number') {
          console.log('发现 settings.currentCardIndex:', settings.currentCardIndex);
          // 强制同步到设置中的索引
          if (currentCardIndex.value !== settings.currentCardIndex) {
            console.log('从 horizontalSwipeSettings 强制同步 currentCardIndex 从', currentCardIndex.value, '到', settings.currentCardIndex);
            currentCardIndex.value = settings.currentCardIndex;
            hasUpdated = true;
          } else {
            console.log('currentCardIndex 已经是', settings.currentCardIndex, '，无需从 horizontalSwipeSettings 更新');
          }
        }
      } else {
        console.log('没有发现 props.content.horizontalSwipeSettings');
      }
    } else {
      console.log('没有 props.content');
      // 如果没有props.content，重置为默认值
      if (currentCardIndex.value !== 0) {
        console.log('没有props.content，重置currentCardIndex为0');
        currentCardIndex.value = 0;
        hasUpdated = true;
      }
    }
    
    // 初始化后保存当前选中卡片的初始状态
    setTimeout(() => {
      const cards = pages.value;
      if (cards && cards.length > 0 && cards[currentCardIndex.value]) {
        console.log('初始化时保存卡片初始状态，索引:', currentCardIndex.value);
        saveCardInitialState(currentCardIndex.value, cards[currentCardIndex.value]);
      }
    }, 100);
    
    // 只在有实际更新且明确需要时才通知父组件
    if (hasUpdated && shouldEmitEvents) {
      console.log('有更新且需要发出事件，发出 card-selected 事件，索引:', currentCardIndex.value);
      nextTick(() => {
        emit('card-selected', {
          cardIndex: currentCardIndex.value,
          selectedCardIndex: currentCardIndex.value
        });
      });
    } else if (hasUpdated) {
      console.log('有更新但不需要发出事件');
    } else {
      console.log('没有更新，不发出事件');
    }
    
    console.log('=== HorizontalSwipeSettings.initializeSettings 结束，最终 currentCardIndex:', currentCardIndex.value, ' ===');
  };
  
  // 初始化设置（首次加载时允许发出事件）
  initializeSettings(true);
  
  // 监听content变化，重新初始化设置（但不发出事件，避免循环）
  watch(() => props.content, (newContent, oldContent) => {
    console.log('=== HorizontalSwipeSettings.watch props.content 变化 ===');
    console.log('newContent:', newContent);
    console.log('oldContent:', oldContent);
    console.log('newContent !== oldContent:', newContent !== oldContent);
    
    // 只有在content真正变化时才重新初始化
    if (newContent !== oldContent) {
      console.log('content 发生变化，重新初始化设置');
      // 如果是从null变为有效content（重新打开设置面板），则允许发出事件
      const shouldEmit = !oldContent && newContent;
      initializeSettings(shouldEmit);
    } else {
      console.log('content 没有变化，跳过初始化');
    }
  }, { deep: false }); // 改为浅监听，避免深层对象变化时的频繁触发

  // 专门监听selectedCardIndex的变化
  watch(() => props.content?.selectedCardIndex, (newIndex, oldIndex) => {
    console.log('=== HorizontalSwipeSettings.watch selectedCardIndex 变化 ===');
    console.log('newIndex:', newIndex);
    console.log('oldIndex:', oldIndex);
    console.log('当前 currentCardIndex.value:', currentCardIndex.value);
    
    if (typeof newIndex === 'number' && newIndex !== oldIndex && newIndex !== currentCardIndex.value) {
      console.log('检测到selectedCardIndex变化，更新currentCardIndex从', currentCardIndex.value, '到', newIndex);
      currentCardIndex.value = newIndex;
    } else {
      console.log('selectedCardIndex无需更新');
    }
  }, { immediate: false });

  // 选择图片
  const selectImageFile = () => {
    dialogMediaType.value = 'image';
    dialogVisible.value = true;
  };
  
  // 处理媒体选择
  const handleMediaSelect = (mediaData) => {
    if (mediaData && (mediaData.mediaUrl || mediaData.path)) {
      // 构造相对路径
      const relativePath = `/aim_files/${mediaData.appKey}/${mediaData.path}`;
      
      // 触发更新卡片图片的事件
      emit('update-card-image', {
        cardIndex: currentCardIndex.value,
        imageData: {
          src: relativePath,
          alt: mediaData.mediaDesc || mediaData.name || `图片${currentCardIndex.value + 1}`
        }
      });
      
      // 延迟调用emitSettingsChange，让响应式系统有时间更新模板数据
      nextTick(() => {
        emitSettingsChange();
      });
      
      ElMessage.success('图片选择成功');
    } else {
      console.error('横滑设置 - 媒体数据无效:', mediaData);
      ElMessage.error('选择的图片无效，请重试');
    }
  };
  
  // 处理图片选择（ImageElement的select事件）
  const handleImageSelect = (imageContent) => {
    selectImageFile();
  };

  // 删除卡片 - 修复删除后默认数据重现的问题
  const removeCard = () => {
    console.log('删除卡片操作开始');
    console.log('当前选中的卡片索引:', currentCardIndex.value);
    
    // 直接从template数据计算当前的卡片数量，避免使用响应式的cardCount
    const currentCards = pages.value;
    const currentCount = currentCards.length;
    console.log('当前卡片数量:', currentCount);
    
    if (currentCount <= 2) {
      ElMessage.warning('至少需要保留2张卡片');
      return;
    }

    // 计算删除后的新卡片数量和新选中索引
    const newCardCount = currentCount - 1;
    console.log('删除后的卡片数量:', newCardCount);
    
    // 如果删除的是最后一张卡片，选择倒数第二张
    // 否则保持当前索引
    let newIndex;
    if (currentCardIndex.value >= newCardCount) {
      newIndex = newCardCount - 1;
    } else {
      newIndex = currentCardIndex.value;
    }
    
    console.log('计算的新索引:', newIndex);
    
    // 清理被删除卡片的初始状态记录
    const deletedCardKey = `card_${currentCardIndex.value}`;
    if (cardInitialStates.value.has(deletedCardKey)) {
      cardInitialStates.value.delete(deletedCardKey);
      console.log('已清理被删除卡片的初始状态:', deletedCardKey);
    }
    
    // 先发送删除事件给父组件，让父组件更新模板数据
    emit('remove-card', { 
      cardIndex: currentCardIndex.value 
    });

    // 延迟更新本地选中索引，确保模板数据先更新
    setTimeout(() => {
      console.log('延迟后更新currentCardIndex为:', newIndex);
      currentCardIndex.value = newIndex;
      
      // 再次延迟，确保响应式系统完成更新
      setTimeout(() => {
        console.log('发送card-selected事件，新索引:', newIndex);
        emit('card-selected', { 
          selectedCardIndex: newIndex,
          cardIndex: newIndex,
          fromCardRemoval: true
        });
        
        // 保存新选中卡片的初始状态
        setTimeout(() => {
          const cards = pages.value;
          if (cards && cards.length > 0 && cards[newIndex]) {
            console.log('删除卡片后保存新选中卡片的初始状态，索引:', newIndex);
            saveCardInitialState(newIndex, cards[newIndex]);
          }
        }, 50);
        
        // 强制重新计算当前卡片数据，防止默认数据重现
        nextTick(() => {
          emitSettingsChange();
        });
      }, 50);
    }, 100);
  };

  // 组件挂载时的初始化
  onMounted(() => {
    console.log('HorizontalSwipeSettings 组件已挂载');
    initializeSettings(false);
    
    try {
      // 添加强制保存事件监听器
      const currentInstance = getCurrentInstance();
      const currentElement = currentInstance?.proxy?.$el;
      
      if (currentElement && typeof currentElement.addEventListener === 'function') {
        currentElement.addEventListener('force-save-settings', forceSaveSettings);
        
        if (typeof currentElement.setAttribute === 'function') {
          currentElement.setAttribute('data-component', 'horizontal-swipe-settings');
        }
        
        console.log('已添加强制保存事件监听器');
      } else {
        console.warn('HorizontalSwipeSettings - 无法获取组件元素或元素不支持事件监听');
      }
    } catch (error) {
      console.error('HorizontalSwipeSettings - 添加事件监听器时出错:', error);
    }
  });
  
  // 组件卸载前清理
  onBeforeUnmount(() => {
    console.log('HorizontalSwipeSettings 组件即将卸载');
    
    try {
      // 移除事件监听器
      const currentInstance = getCurrentInstance();
      const currentElement = currentInstance?.proxy?.$el;
      
      if (currentElement && typeof currentElement.removeEventListener === 'function') {
        currentElement.removeEventListener('force-save-settings', forceSaveSettings);
        console.log('已移除强制保存事件监听器');
      }
    } catch (error) {
      console.error('HorizontalSwipeSettings - 移除事件监听器时出错:', error);
    }
  });

  // 添加组件销毁前的强制保存
  onBeforeUnmount(() => {
    console.log('HorizontalSwipeSettings 组件即将卸载，强制保存数据');
    forceSaveSettings();
  });

  // 添加窗口关闭前的强制保存
  const handleBeforeUnload = () => {
    console.log('检测到窗口即将关闭，强制保存横滑设置数据');
    forceSaveSettings();
  };

  onMounted(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
  });

  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });

  // 导出强制保存函数供父组件调用
  defineExpose({
    forceSaveSettings
  });

  // 保存卡片的初始状态
  const saveCardInitialState = (cardIndex, cardData) => {
    const key = `card_${cardIndex}`;
    
    // 从contents数组中获取数据
    const titleContent = (cardData.contents || []).find(c => c.type === 'text' && c.isTextTitle === 1);
    const contentText = (cardData.contents || []).find(c => c.type === 'text' && c.isTextTitle === 0);
    const buttonContent = (cardData.contents || []).find(c => c.type === 'button');
    
    cardInitialStates.value.set(key, {
      title: String(titleContent?.content || cardData.title || '').trim(),
      content: String(contentText?.content || cardData.content || '').trim(),
      buttonText: String(buttonContent?.content || cardData.buttonText || '').trim(),
      timestamp: Date.now()
    });
  };

  // 获取卡片的初始状态
  const getCardInitialState = (cardIndex) => {
    const key = `card_${cardIndex}`;
    return cardInitialStates.value.get(key);
  };
  </script>
  
  <style scoped lang="scss">
  .horizontal-swipe-settings {
    padding: 16px;
    
    .style-options {
      display: flex;
      gap: 14px;
      flex-wrap: wrap;
    }

    .style-option .style-image{
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .style-option.is-selected .style-image {
      border: 1px solid #1989fa;
    }

    .style-image {
      width: 101px;
      display: block;
    }

    .style-name {
      font-size: 14px;
      color: #666;
      text-align: center;
      display: block;
      margin-top: 10px;
    }
    .setting-section{
      margin-bottom: 20px;
    }
    .section-title{
      color: #606266;
      font-weight: normal;
      i{
        color: red;
        margin-right: 4px;
      }
    }
    // 卡片控制
    .card-controls {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 16px 0 10px;
      
      .control-btn {
        width: 32px;
        height: 32px;
        border: 1px solid #dcdfe6;
        background: white;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 16px;
        color: #606266;
        
        &:hover:not(:disabled) {
          border-color: #409eff;
          color: #409eff;
        }
        
        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
      
      .number-buttons {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        
        .number-btn {
          width: 32px;
          height: 32px;
          border: 1px solid #dcdfe6;
          background: white;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 14px;
          color: #606266;
          
          &:hover {
            border-color: #409eff;
            color: #409eff;
          }
          
          &.active {
            background: #409eff;
            border-color: #409eff;
            color: white;
          }
        }
      }
    }
    
    // 卡片图片
    .card-image-container {
      margin-bottom: 16px;
      
      .settings-image {
        width: 180px;
        height: 180px;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        overflow: hidden;
        display: block;
        margin-bottom: 8px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      
      .no-image-placeholder {
        width: 100%;
        height: 160px;
        border: 1px dashed #dcdfe6;
        border-radius: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        font-size: 14px;
        margin-bottom: 8px;
        
        .el-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }
      }
      
      .setting-desc {
        font-size: 14px;
        line-height: 22px;
        margin: 12px 0;
        color: #666;
      }
      
      .select-btn {
        margin-top: 8px;
      }
    }

  }
  </style>