<template>
  <div class="setting-group">
    <div class="group-title">可用厂商</div>
    <div class="setting-item">
      <div class="factory-list">
        <span 
          v-for="factory in factoryList" 
          :key="factory.code"
          class="factory-tag"
          size="small">
          <img :src="getFactoryIcon(factory.code)" class="factory-icon" :alt="getFactoryName(factory.code)">
          {{ getFactoryName(factory.code) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getFactoryIcon, getFactoryName } from '@/utils/factoryUtils';

const props = defineProps({
  factoryList: {
    type: Array,
    default: () => []
  }
});
// 打印 factoryList 数据
// console.log('factoryList:', props.factoryList);
</script>

<style scoped lang="scss">
.setting-group {
  .group-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    position: relative;
    background: #eee;
    padding: 14px;
  }

  .setting-item {
    padding: 20px 14px 0;
  }
}

.factory-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.factory-tag {
  display: flex;
    align-items: center;
    padding: 0px 10px 6px 0px;
    border-radius: 4px;
    font-size: 14px;
    color: #333;
    width: 78px;
}

.factory-icon {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-right: 5px;
}
</style> 