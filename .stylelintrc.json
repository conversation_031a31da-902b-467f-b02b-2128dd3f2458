{"extends": ["stylelint-config-standard", "stylelint-config-prettier", "stylelint-config-recommended-vue"], "plugins": ["stylelint-prettier"], "overrides": [{"files": ["**/*.{vue,html}"], "customSyntax": "postcss-html"}, {"files": ["**/*.{css,scss}"], "customSyntax": "postcss-scss"}], "rules": {"declaration-property-value-no-unknown": null, "declaration-empty-line-before": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["deep", "global"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["apply", "use", "forward", "extend"]}]}}