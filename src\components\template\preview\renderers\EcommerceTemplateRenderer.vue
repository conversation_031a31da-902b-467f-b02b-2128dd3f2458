<template>
  <div class="ecommerce-template" @click="handleEcommerceClick">
    <!-- 轮播图片区域 -->
    <div class="media-section">
      <!-- 轮播图模式 -->
      <CarouselElement 
        v-if="displayData.style === 'image'"
        :content="getCarouselContent()"
        :usage="usage"
        :get-media-url="getMediaUrl"
        class="ecommerce-carousel"
        @select="handleCarouselSelect"
        @imageClick="handleCarouselImageClick"
        @update:content="handleCarouselUpdate"
      />
      
      <!-- 视频模式 -->
      <div v-else-if="displayData.style === 'video'" class="video-section">
        <video
          v-if="displayData.videoSrc && !isDefaultVideo(displayData.videoSrc)"
          :src="getVideoSrc(displayData.videoSrc)"
          controls
          class="ecommerce-video"
        />
        <div v-else class="video-placeholder">
          <img 
            :src="getMediaUrl('/aim_files/aim_defult/defaultVideo.jpg')"
            alt="默认视频缩略图"
            class="video-default-image"
          />
          <div class="video-placeholder-icon">
            ▶
          </div>
        </div>
      </div>
    </div>

    <!-- 价格区域-->
    <div class="ecommerce-price-section">
      <span class="ecommerce-price">
        <template v-if="displayData.aimCurrencyDisplay === 1">￥</template>{{ displayData.priceText }}
      </span>
      <span class="ecommerce-tag">{{ displayData.tagText }}</span>
    </div>

    <!-- 标题区域  -->
    <div class="ecommerce-title-section">{{ displayData.titleText }}</div>

    <!-- 内容区域 -->
    <div class="ecommerce-content-section">{{ displayData.contentText }}</div>

    <!-- 按钮区域  -->
    <div v-if="visibleButtons && visibleButtons.length > 0" class="ecommerce-button-section">
      <button 
        v-for="(button, index) in visibleButtons" 
        :key="index"
        class="ecommerce-button"
        :class="`ecommerce-button-${index}`"
        @click="handleButtonClick(button, index, $event)"
      >
        {{ button.text || `按钮${index + 1}` }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, watch } from 'vue';
import CarouselElement from '../elements/CarouselElement.vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import { ClickEventTypeConverter, ActionJsonGenerator } from '@/utils/ClickEventManager';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  // 新增：接收从父组件传递的显示数据
  ecommerceDisplayData: {
    type: Object,
    default: null
  },
  // 新增：usage属性，用于控制组件行为
  usage: {
    type: String,
    default: 'editor' // 'editor' | 'list'
  },
  // 新增：getMediaUrl函数
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 处理电商模板点击事件
const handleEcommerceClick = (event) => {
  // 防止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();
  
  // 检查是否已处理过此次点击
  const container = event.target.closest('.ecommerce-template');
  if (container && container.dataset.clickProcessed) {
    return;
  }
  
  // 标记为已处理
  if (container) {
    container.dataset.clickProcessed = 'true';
    setTimeout(() => {
      delete container.dataset.clickProcessed;
    }, 200);
  }
  
  console.log('电商模板点击事件');
  
  emit('select-content', {
    contentId: 'ecommerce-settings',
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: displayData.value
  });
};

// 处理轮播图容器选择事件
const handleCarouselSelect = (content) => {
  // 轮播图容器被选择时，显示电商设置面板
  emit('select-content', {
    contentId: 'ecommerce-settings',
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: displayData.value
  });
};

// 处理轮播图中单张图片点击事件
const handleCarouselImageClick = (data) => {
  // 点击轮播图中的单张图片时，显示电商设置面板
  emit('select-content', {
    contentId: 'ecommerce-settings',
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: displayData.value
  });
};

// 处理轮播图内容更新事件
const handleCarouselUpdate = (updatedContent) => {
  // 将轮播图的更新传递给父组件，用于同步设置面板
  emit('update:content', updatedContent);
};

// 处理按钮点击事件
const handleButtonClick = (button, index, event) => {
  // 防止事件冒泡到父级
  event.preventDefault();
  event.stopPropagation();
  
  // 按钮被点击时，显示电商设置面板并选中对应按钮
  emit('select-content', {
    contentId: 'ecommerce-settings',
    type: 'ecommerce-settings',
    isEcommerceSettings: true,
    currentData: {
      ...displayData.value,
      selectedButtonIndex: index // 标记选中的按钮索引
    }
  });
};

// 获取轮播图内容格式
const getCarouselContent = () => {
  
  if (!displayData.value.images || displayData.value.images.length === 0) {
    return {
      type: 'image',
      contentId: 48,
      carouselImages: [],
      isCarousel: true,
      currentImageIndex: 0,
      positionNumber: 1
    };
  }
  
  // 将电商图片数据转换为轮播图组件需要的格式
  const carouselImages = displayData.value.images.map((image, index) => {
    // 兼容旧格式，统一为actionType/actionUrl
    let actionType = 'OPEN_BROWSER';
    let actionUrl = '';
    if (image.clickEvent) {
      actionType = ClickEventTypeConverter.toActionType(image.clickEvent.type);
      actionUrl = ActionJsonGenerator.fromClickEvent(image.clickEvent).target || '';
    }
    return {
      src: image.src || image, // 兼容字符串和对象格式
      alt: image.alt || `商品图片${index + 1}`,
      positionNumber: 8 + index, // 第一张为8，后续递增
      clickEvent: {
        actionType,
        actionUrl
      }
    };
  });
  
  // 获取当前轮播图索引，优先使用设置面板传来的索引
  const currentIndex = displayData.value.currentImageIndex !== undefined 
    ? displayData.value.currentImageIndex 
    : 0;
  
  return {
    type: 'image',
    contentId: 48, // 电商图片的contentId
    isCarousel: true,
    carouselImages: carouselImages,
    currentImageIndex: currentIndex, // 使用同步的索引
    positionNumber: 1
  };
};

// 获取视频源地址
const getVideoSrc = (videoSrc) => {
  if (!videoSrc) {
    return '';
  }
  
  // 如果videoSrc是对象且有src属性
  if (typeof videoSrc === 'object' && videoSrc.src) {
    // 使用getMediaUrl处理视频路径
    return getMediaUrl(videoSrc.src);
  }
  
  // 如果videoSrc是字符串，直接返回
  if (typeof videoSrc === 'string') {
    // 使用getMediaUrl处理视频路径
    return getMediaUrl(videoSrc);
  }
  
  // 默认视频
  return '';
};

// 判断是否为默认视频
const isDefaultVideo = (videoSrc) => {
  if (!videoSrc) return true;
  
  // 检查是否是默认视频路径
  return videoSrc === '/aim_files/aim_defult/defaultVideo.jpg' || 
         videoSrc.includes('defaultVideo.jpg');
};

// 处理文本内容
const handleTextContent = (contents) => {
  
  let priceText = '';
  let tagText = '';
  let titleText = '';
  let contentText = '';
  let aimCurrencyDisplay = 1; // 默认显示￥符号

  contents.forEach((content, index) => {
    
    if (content.type === 'text') {
      // 根据API数据结构正确识别文本类型
      if (content.positionNumber === 2 && content.aimCurrencyDisplay === 1 && content.isTextTitle === 1) {
        // 价格文本 - positionNumber=2, aimCurrencyDisplay=1, isTextTitle=1
        priceText = content.content || '';
        aimCurrencyDisplay = content.aimCurrencyDisplay; // 提取货币显示设置
      } else if (content.positionNumber === 3 && content.isTextTitle === 1) {
        // 标签文本 - positionNumber=3, isTextTitle=1
        tagText = content.content || '';
      } else if (content.positionNumber === 4 && content.isTextTitle === 1) {
        // 主标题文本 - positionNumber=4, isTextTitle=1
        titleText = content.content || '';
      } else if (content.positionNumber === 5 && content.isTextTitle === 0) {
        // 正文内容 - positionNumber=5, isTextTitle=0
        contentText = content.content || '';
      } else {
        // 兼容旧版本数据格式
        if (content.contentId === 49 || (content.aimCurrencyDisplay === 1 && !priceText)) {
          priceText = content.content || priceText;
          if (typeof content.aimCurrencyDisplay === 'number') {
            aimCurrencyDisplay = content.aimCurrencyDisplay;
          }
        } else if (content.contentId === 174 && !tagText) {
          tagText = content.content || tagText;
        } else if (content.contentId === 50 && !titleText) {
          titleText = content.content || titleText;
        } else if (content.contentId === 51 && !contentText) {
          contentText = content.content || contentText;
        }
      }
    }
  });

  return { priceText, tagText, titleText, contentText, aimCurrencyDisplay };
};

// 计算显示数据 - 优先使用父组件传递的数据
const displayData = computed(() => {
  
  // 如果父组件传递了显示数据，直接使用
  if (props.ecommerceDisplayData) {
    return props.ecommerceDisplayData;
  }

  
  // 如果没有内容数据，返回空结构
  if (!props.contents || !Array.isArray(props.contents)) {
    return {
      style: 'image',
      images: [],
      videoSrc: '',
      aimCurrencyDisplay: 1, // 默认显示￥符号，会被实际数据覆盖
      priceText: '',
      tagText: '',
      titleText: '',
      contentText: '',
      buttons: [],
      currentImageIndex: 0
    };
  }

  // 直接从原始数据中提取内容，不添加默认值
  const result = {
    style: 'image',
    images: [],
    videoSrc: '',
    aimCurrencyDisplay: 1, // 默认显示￥符号，会被实际数据覆盖
    priceText: '',
    tagText: '',
    titleText: '',
    contentText: '',
    buttons: [],
    currentImageIndex: 0
  };
  
  // 处理文本内容，并应用返回的所有属性包括aimCurrencyDisplay
  const textData = handleTextContent(props.contents);
  Object.assign(result, textData);
  
  // 处理其他类型的内容
  props.contents.forEach(content => {
    
    switch (content.type) {
      case 'image':
        // 处理电商轮播图数据（来自编辑器设置）
        if (content.templateType === 'ecommerce' && content.isEcommerceImage && content.carouselImages) {
          result.images = content.carouselImages;
          
          // 提取currentImageIndex
          if (content.currentData && typeof content.currentData.currentImageIndex === 'number') {
            result.currentImageIndex = content.currentData.currentImageIndex;
          } else if (typeof content.currentImageIndex === 'number') {
            result.currentImageIndex = content.currentImageIndex;
          }
          
        }
        // 处理接口返回的普通图片数据
        else if (content.src) {
          // 将普通图片转换为电商轮播图格式
          const imageData = {
            src: content.src,
            alt: content.content || content.alt || `图片${result.images.length + 1}`,
            positionNumber: content.positionNumber || (result.images.length + 1),
            clickEvent: content.clickEvent || {
              actionType: 'OPEN_BROWSER', // 使用clickEventManager中定义的映射类型
              actionUrl: ''
            }
          };
          
          result.images.push(imageData);
        }
        break;
        
      case 'video':
        result.style = 'video';
        result.videoSrc = content.src || '';
        break;
        
      case 'button':
        // contentId 52 和 53 是按钮，同时处理字符串和数字类型
        if (content.contentId === 52 || content.contentId === 53 || 
            content.contentId === '52' || content.contentId === '53') {
          
          result.buttons.push({
            text: content.content || '',
            positionNumber: content.positionNumber || 0,
            contentId: content.contentId,
            hidden: false, // 默认显示按钮
            clickEvent: content.clickEvent || {
              actionType: 'OPEN_BROWSER', // 使用clickEventManager中定义的映射类型
              actionUrl: '',

            }
          });
        }
        break;
    }
  });

  // 按位置排序按钮
  result.buttons.sort((a, b) => (a.positionNumber || 0) - (b.positionNumber || 0));
  return result;
});

// 计算可见按钮 - 过滤掉隐藏的按钮
const visibleButtons = computed(() => {
  if (!displayData.value.buttons || !Array.isArray(displayData.value.buttons)) {
    return [];
  }
  
  // 过滤掉隐藏的按钮
  const filtered = displayData.value.buttons.filter(button => !button.hidden);
  
  return filtered;
});
</script>

<style scoped lang="scss">

.ecommerce-media-section {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.ecommerce-video-container {
  width: 100%;
  height: 100%;
}

.ecommerce-video {
  width: 100%;
  height: auto;
  max-height: 192px;
  object-fit: contain;
  background-color: #000;
}

.ecommerce-image-container {
  width: 100%;
  height: 100%;
  display: flex;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
}

.ecommerce-image-item {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  scroll-snap-align: start;
}

.ecommerce-image {
  width: 100%;
  height: 100%;
  // object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
  border: 2px dashed #ddd;
  box-sizing: border-box;
}

.ecommerce-price-section {
  padding: 12px 16px 8px;
  display: flex;
  align-items: center;
}

.ecommerce-price {
  font-size: 20px;
  font-weight: 700;
  color: #e3413a;
}

.ecommerce-tag {
  display: inline-block;
  background: #e84026;
  color: white;
  font-size: 14px;
  padding: 1px 6px;
  border-radius: 4px;
  font-weight: 500;
  margin-left: 8px;
  min-width: 30px;
  height: 23px;
}

.ecommerce-title-section {
  padding: 0 16px 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.ecommerce-content-section{
  margin: 0 16px 16px;
  font-size: 13px;
  color: #333;
  line-height: 1.5;
  height: 60px;
  text-indent: 78px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  position: relative;
  &::before{
    content: "【国泰海通】";
    text-indent: 0px;
    line-height: inherit;
    color: rgb(250, 173, 20);
    display: block;
    position: absolute;
    top: 0px;
    left: 0px;
    white-space: nowrap;
    border-radius: 4px;
  }
}

.ecommerce-button-section {
  padding: 0 6px 20px;
  display: flex;
  justify-content: space-between;
}

.ecommerce-button {
  height: 36px;
  color: #fff;
  text-align: center;
  line-height: 36px;
  font-size: 16px;
  border-radius: 40px;
  border: none;
  background-color: #f9a01e;
  margin: 0 12px;
  flex: 1;
}

.ecommerce-button-1 {
  background-color: #e3413a;
}

.ecommerce-carousel {
  width: 100%;
  height: 100%;
  :deep(.el-carousel__container){
    height: 342px !important;
  }
}

.ecommerce-carousel :deep(.carousel-element) {
  height: 100%;
}

.ecommerce-carousel :deep(.carousel-container) {
  height: 100%;
}

.ecommerce-carousel :deep(.el-carousel__item) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ecommerce-carousel :deep(.carousel-image) {
  width: 100%;
  height: 100%;
  // object-fit: cover;
}

.video-section {
  position: relative;
  width: 100%;
}

.video-placeholder {
  width: 100%;
  height: 192px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.video-placeholder .video-default-image {
  width: 100%;
  height: 100%;
  // object-fit: cover;
}

.video-placeholder-icon {
  width: 46px;
  height: 46px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  font-size: 20px;
  color: white;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-placeholder-icon:hover {
  background-color: rgba(0, 0, 0, 0.7);
}
</style> 