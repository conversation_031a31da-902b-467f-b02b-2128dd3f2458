<template>
  <el-dialog
    v-model="dialogVisible"
    title="预览提交"
    width="50%"
    :show-close="true"
    align-center
    :close-on-click-modal="false"
    class="preview-submit-dialog"
  >
    <div class="preview-wrap">
      <el-form label-position="top" class="form-container" :model="editData" ref="formRef" :rules="rules" :validate-on-rule-change="false">
        <!-- 模板名称 -->
        <el-form-item label="模板名称" class="form-item" prop="templateName">
          <el-input 
            v-model="editData.templateName" 
            placeholder="请输入模板名称" 
            :maxlength="17"
            show-word-limit
          />
        </el-form-item>
        
        <!-- 使用场景 -->
        <el-form-item label="使用场景" class="form-item required-label" prop="scene">
          <el-input 
            v-model="editData.scene" 
            placeholder="请输入模板使用场景（如购买抢购）" 
            :maxlength="10"
            show-word-limit
          />
        </el-form-item>
        
        <!-- 模板用途 -->
        <el-form-item label="模板用途" class="form-item required-label" prop="useId">
          <el-select v-model="editData.useId" placeholder="请选择模板用途" class="full-width">
            <el-option label="请选择模板用途" value="" disabled />
            <el-option label="商用" value="1" />
            <el-option label="试商用" value="2" />
          </el-select>
        </el-form-item>
        
        <!-- 送审厂商 -->
        <el-form-item label="送审厂商" class="form-item required-label" prop="factoryInfos">
          <div class="vendor-checkbox-group">
            <template v-if="availableFactories.length > 0">
              <el-checkbox 
                v-for="factory in availableFactories" 
                :key="factory.code"
                v-model="editData.factoryInfos[factory.code]"
                :label="factory.name"
              ></el-checkbox>
            </template>
            <template v-else>
              <el-checkbox v-model="editData.factoryInfos.huawei" label="华为"></el-checkbox>
              <el-checkbox v-model="editData.factoryInfos.honor" label="荣耀"></el-checkbox>
              <el-checkbox v-model="editData.factoryInfos.xiaomi" label="小米"></el-checkbox>
              <el-checkbox v-model="editData.factoryInfos.oppo" label="oppo"></el-checkbox>
              <el-checkbox v-model="editData.factoryInfos.vivo" label="vivo"></el-checkbox>
              <el-checkbox v-model="editData.factoryInfos.samsung" label="三星"></el-checkbox>
            </template>
          </div>
        </el-form-item>
        
        <!-- 短信签名 -->
        <!-- <el-form-item label="短信签名" class="form-item" prop="aimSmsSigns">
          <el-select 
            v-model="editData.aimSmsSigns"
            multiple
            filterable
            allow-create
            :reserve-keyword="false"
            default-first-option
            placeholder="请选择/输入短信签名(最多3个)"
            :max="3"
            class="full-width"
          >
            <el-option
              v-for="item in signatureOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        
        <!-- 短信示例 -->
        <el-form-item label="短信示例" class="form-item required-label" prop="smsExample">
          <el-input
            v-model="editData.smsExample"
            type="textarea"
            placeholder="请输入所要发送的短信原文的示例（用于模版审核时使用）"
            :maxlength="48"
            :rows="2"
            show-word-limit
          />
        </el-form-item>

       <!-- 备注信息 -->
       <!-- <el-form-item label="备注信息" class="form-item" prop="memo">
          <el-input
            v-model="editData.memo"
            type="textarea"
            placeholder="您可以输入业务ID、客户ID、或其它备注信息(选填)"
            :maxlength="50"
            :rows="2"
            show-word-limit
          />
        </el-form-item> -->
      </el-form>
    </div>
      
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  templateData: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm','update:templateName']);

// 表单ref
const formRef = ref(null);

// 表单校验规则
const rules = {
  templateName: [
    { required: true, message: '请输入模板名称', trigger: 'blur' }
  ],
  scene: [
    { required: true, message: '请输入使用场景', trigger: 'blur' }
  ],
  useId: [
    { required: true, message: '请选择模板用途', trigger: 'change' }
  ],
  factoryInfos: [
    { 
      validator: (rule, value, callback) => {
        const hasSelected = Object.values(value).some(selected => selected === true);
        if (!hasSelected) {
          callback(new Error('请至少选择一个送审厂商'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ],
  smsExample: [
    { required: true, message: '请输入短信示例', trigger: 'blur' }
  ],
  aimSmsSigns: [
    { 
      validator: (rule, value, callback) => {
        if (value.length > 3) {
          callback(new Error('最多只能选择3个短信签名'));
        } else {
          callback();
        }
      },
      trigger: 'change'
    }
  ]
};

// 控制对话框显示
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
});

// 可用厂商列表
const availableFactories = ref([
  { code: 'huawei', name: '华为' },
  { code: 'honor', name: '荣耀' },
  { code: 'xiaomi', name: '小米' },
  { code: 'oppo', name: 'oppo' },
  { code: 'vivo', name: 'vivo' },
  { code: 'samsung', name: '三星' }
]);

// 编辑数据（可以在预览时修改）
const editData = reactive({
  templateName: '',

  factoryInfos: {
    huawei: false,
    honor: false,
    xiaomi: false,
    oppo: false,
    vivo: false,
    samsung: false
  },
  aimSmsSigns: [],
  smsExample: ''
});

// 签名选项
const signatureOptions = [];

// 监听传入数据变化
// 修复：只监听 dialogVisible，避免每次输入都被重置
watch(dialogVisible, (isVisible) => {
  if (isVisible && props.templateData) {
    // 初始化编辑数据
    editData.templateName = props.templateData.templateName || '';
    // 完全重置厂商数据
    editData.factoryInfos = {
      huawei: false,
      honor: false,
      xiaomi: false,
      oppo: false,
      vivo: false,
      samsung: false
    };
    // 设置新选择
    if (props.templateData.factoryInfos) {
      if (Array.isArray(props.templateData.factoryInfos)) {
        props.templateData.factoryInfos.forEach(key => {
          if (key in editData.factoryInfos) {
            editData.factoryInfos[key] = true;
          }
        });
      } else if (typeof props.templateData.factoryInfos === 'object') {
        Object.entries(props.templateData.factoryInfos).forEach(([key, value]) => {
          if (key in editData.factoryInfos) {
            editData.factoryInfos[key] = !!value;
          }
        });
      }
    }
    // 初始化场景和用途
    editData.scene = '';
    editData.useId = '1';
    // 初始化签名和示例，确保aimSmsSigns是数组
    editData.aimSmsSigns = props.templateData.aimSmsSigns ? 
      (Array.isArray(props.templateData.aimSmsSigns) ? 
        [...props.templateData.aimSmsSigns] : 
        typeof props.templateData.aimSmsSigns === 'string' ? 
          props.templateData.aimSmsSigns.split(',').map(s => s.trim().replace(/^['"]|['"]$/g, '')) : 
          []) : 
      [];
    // 过滤空字符串
    editData.aimSmsSigns = editData.aimSmsSigns.filter(sign => sign && sign.trim() !== '');
    editData.smsExample = '';
  }
}, { immediate: true });

// 监听模板名称变化，同步回父组件
watch(() => editData.templateName, (newName) => {
  // 使用自定义事件通知父组件模板名称变更
  emit('update:templateName', newName);
});

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
  emit('cancel');
};

// 确认提交
const handleConfirm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      console.log('表单校验通过，提交数据:', editData);
      
      // 构建提交数据
      const submitData = {
        scene: editData.scene,
        useId: editData.useId,
        factoryInfos: editData.factoryInfos,
        aimSmsSigns: editData.aimSmsSigns.map(sign => `"${sign}"`).join(','),
        smsExample: editData.smsExample
      };
      
      // 触发确认事件
      emit('confirm', submitData);
    } else {
      console.log('表单校验失败');
      ElMessage.warning('请完善必填信息');
      return false;
    }
  });
};
</script>

<style scoped lang="scss">
.form-item {
  margin-bottom: 18px;
}

.full-width {
  width: 100%;
}

.required-label :deep(.el-form-item__label)::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.vendor-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
</style>