/**
 * 多图文模板处理器
 * 负责处理多图文模板的特殊逻辑，支持1-3对图文内容
 */

import { ActionJsonGenerator } from '@/utils/clickEventManager.js';

export default {
  /**
   * 获取模板渲染器组件
   * @returns {string} 渲染器组件名称
   */
  getRenderer() {
    return 'MultiTextTemplateRenderer';
  },

  /**
   * 处理模板数据
   * @param {Object} template 原始模板数据
   * @returns {Object} 处理后的模板数据
   */
  processTemplate(template) {
    if (!template) return template;

    console.log('MultiTextTemplate.processTemplate - 开始处理模板:', template);

    // 确保模板有基本结构
    const processedTemplate = {
      ...template,
      tplType: template.tplType || '多图文'
    };

    // 解析pages数据
    let pages = template.pages;
    if (typeof pages === 'string') {
      try {
        pages = JSON.parse(pages);
      } catch (error) {
        console.error('MultiTextTemplate.processTemplate - 解析pages失败:', error);
        pages = [];
      }
    }

    // 确保pages是数组
    if (!Array.isArray(pages)) {
      pages = [];
    }

    // 处理每一页的内容
    const processedPages = pages.map(page => {
      if (!page.contents || !Array.isArray(page.contents)) {
        return page;
      }

      // 处理页面内容，确保图文内容正确显示
      const processedContents = page.contents.map(content => {
        const processedContent = {
          ...content,
          // 确保内容文本正确显示
          content: content.content || content.text || '',
          // 确保类型正确
          type: content.type || 'text',
          // 确保有contentId
          contentId: content.contentId || `${content.type || 'text'}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          // 确保可见性
          visible: content.visible !== false,
          // 添加模板相关信息
          userId: template.userId || null,
          templateId: template.templateId || null,
          pageId: content.pageId || page.pageId || 1
        };

        console.log('MultiTextTemplate.processTemplate - 处理内容项:', {
          原始: content,
          处理后: processedContent
        });

        return processedContent;
      });

      return {
        ...page,
        contents: processedContents
      };
    });

    processedTemplate.pages = processedPages;

    console.log('MultiTextTemplate.processTemplate - 处理完成:', processedTemplate);
    return processedTemplate;
  },

  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的内容数组
   */
  initializeContents(template) {
    console.log('MultiTextTemplate.initializeContents - 开始初始化，模板数据:', template);
    
    if (!template) return [];
    
    // 确保模板有页面结构
    if (!template.pages || !Array.isArray(template.pages)) {
      template.pages = [];
    }
    
    // 如果没有页面，创建一个默认页面
    if (template.pages.length === 0) {
      template.pages.push({
        pageId: 1,
        contents: []
      });
    }
    
    // 获取第一页的内容
    const firstPage = template.pages[0];
    if (!firstPage.contents || !Array.isArray(firstPage.contents)) {
      console.log('MultiTextTemplate.initializeContents - 没有内容，返回空数组');
      return [];
    }
    
    console.log('MultiTextTemplate.initializeContents - 原始内容:', firstPage.contents);
    
    // 处理内容，确保每个内容都有必要的属性
    const processedContents = firstPage.contents.map((content, index) => {
      console.log(`MultiTextTemplate.initializeContents - 处理第${index + 1}个内容:`, {
        type: content.type,
        positionNumber: content.positionNumber,
        isTextTitle: content.isTextTitle,
        原始role: content.role
      });
      
      const processedContent = {
        ...content,
        // 确保有唯一的contentId
        contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        // 确保可见性
        visible: content.visible !== false,
        // 确保有正确的类型
        type: content.type || 'text'
      };

      // 根据位置和类型自动分配role属性
      if (!processedContent.role) {
        const pos = processedContent.positionNumber;
        
        console.log(`MultiTextTemplate.initializeContents - 为第${index + 1}个内容分配role，位置: ${pos}, 类型: ${processedContent.type}`);
        
        if (processedContent.type === 'image' && pos === 1) {
          // 第一个位置的图片作为头部图片
          processedContent.role = 'header-image';
          processedContent.layout = 'header';
          console.log(`MultiTextTemplate.initializeContents - 分配role: header-image`);
        } else if (processedContent.type === 'text' && processedContent.isTextTitle === 1 && pos === 2) {
          // 第二个位置的标题文本作为头部文本
          processedContent.role = 'header-text';
          processedContent.layout = 'header';
          console.log(`MultiTextTemplate.initializeContents - 分配role: header-text`);
        } else if (processedContent.type === 'image' && pos >= 3) {
          // 第3个位置开始的图片作为图文对的图片
          // 位置3是第1对，位置5是第2对，位置7是第3对...
          const pairIndex = Math.floor((pos - 1) / 2);
          processedContent.role = 'pair-image';
          processedContent.layout = 'left';
          processedContent.pairIndex = pairIndex;
          console.log(`MultiTextTemplate.initializeContents - 分配role: pair-image, pairIndex: ${pairIndex}`);
        } else if (processedContent.type === 'text' && pos >= 4) {
          // 第4个位置开始的文本作为图文对的文本
          // 位置4是第1对，位置6是第2对，位置8是第3对...
          const pairIndex = Math.floor((pos - 2) / 2);
          processedContent.role = 'pair-text';
          processedContent.layout = 'right';
          processedContent.pairIndex = pairIndex;
          console.log(`MultiTextTemplate.initializeContents - 分配role: pair-text, pairIndex: ${pairIndex}`);
        } else {
          console.log(`MultiTextTemplate.initializeContents - 未匹配到role规则，保持原样`);
        }
      } else {
        console.log(`MultiTextTemplate.initializeContents - 已有role: ${processedContent.role}`);
      }

      console.log('MultiTextTemplate.initializeContents - 处理内容项:', {
        原始: content,
        处理后: processedContent
      });

      return processedContent;
    });
    
    console.log('MultiTextTemplate.initializeContents - 最终处理结果:', processedContents);
    return processedContents;
  },

  /**
   * 确保有默认内容
   * @param {Array} contents 现有内容数组
   * @param {Object} template 模板数据（可选）
   * @returns {Array} 确保有默认内容的数组
   */
  ensureDefaultContent(contents, template = null) {
    // 如果已经有内容，直接返回
    if (contents && Array.isArray(contents) && contents.length > 0) {
      return contents;
    }

    // 如果没有内容，创建默认的多图文模板内容
    return this.createDefaultContents(template);
  },

  /**
   * 创建默认的多图文模板内容
   * @param {Object} template 模板数据（可选，用于从接口获取默认内容）
   * @returns {Array} 默认内容数组
   */
  createDefaultContents(template = null) {
    // 如果有模板数据且有页面内容，优先使用接口返回的内容
    if (template && template.pages && Array.isArray(template.pages) && template.pages.length > 0) {
      const page = template.pages[0];
      if (page.contents && Array.isArray(page.contents) && page.contents.length > 0) {
        return page.contents.map((content, index) => {
          const processedContent = {
            ...content,
            contentId: content.contentId || `${content.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            userId: template.userId || null,
            templateId: template.templateId || null,
            // 确保内容文本正确显示
            content: content.content || content.text || '',
            // 确保类型正确
            type: content.type || 'text',
            // 确保可见性
            visible: content.visible !== false
          };

          // 根据位置和类型自动分配role属性
          if (!processedContent.role) {
            const pos = processedContent.positionNumber;
            
            if (processedContent.type === 'image' && pos === 1) {
              // 第一个位置的图片作为头部图片
              processedContent.role = 'header-image';
              processedContent.layout = 'header';
            } else if (processedContent.type === 'text' && processedContent.isTextTitle === 1 && pos === 2) {
              // 第二个位置的标题文本作为头部文本
              processedContent.role = 'header-text';
              processedContent.layout = 'header';
            } else if (processedContent.type === 'image' && pos >= 3) {
              // 第3个位置开始的图片作为图文对的图片
              // 位置3是第1对，位置5是第2对，位置7是第3对...
              const pairIndex = Math.floor((pos - 1) / 2);
              processedContent.role = 'pair-image';
              processedContent.layout = 'left';
              processedContent.pairIndex = pairIndex;
            } else if (processedContent.type === 'text' && pos >= 4) {
              // 第4个位置开始的文本作为图文对的文本
              // 位置4是第1对，位置6是第2对，位置8是第3对...
              const pairIndex = Math.floor((pos - 2) / 2);
              processedContent.role = 'pair-text';
              processedContent.layout = 'right';
              processedContent.pairIndex = pairIndex;
            }
          }

          return processedContent;
        });
      }
    }

    // 创建默认的多图文模板内容
    const defaultContents = [];
    
    // 顶部大图片
    defaultContents.push({
      contentId: `header-image-${Date.now()}`,
      type: 'image',
      content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', // 使用接口返回的默认提示文本
      src: '/aim_files/aim_defult/defaultImg.jpg', // 使用接口返回的默认图片路径
      pageId: 1,
      positionNumber: 1,
      layout: 'header',
      role: 'header-image',
      pageLayout: 'center',
      pageLines: 1,
      actionType: 'OPEN_BROWSER',
      actionJson: { target: '' }
    });
    
    // 顶部大图片的底部文本
    defaultContents.push({
      contentId: `header-text-${Date.now()}`,
      type: 'text',
      content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
      pageId: 1,
      positionNumber: 2,
      layout: 'header',
      role: 'header-text',
      isTextTitle: 1,
      pageLayout: 'left',
      pageLines: 2
    });
    
    // 默认创建1对图文内容
    for (let i = 0; i < 1; i++) {
      const pairIndex = i + 1;
      
      // 左侧小图片
      defaultContents.push({
        contentId: `image-${pairIndex}-${Date.now()}`,
        type: 'image',
        content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内',
        src: '/aim_files/aim_defult/defaultImg.jpg',
        pageId: 1,
        positionNumber: i * 2 + 3, // 从第3个位置开始
        pairIndex: pairIndex,
        layout: 'left',
        role: 'pair-image',
        pageLayout: 'left',
        pageLines: 3,
        actionType: 'OPEN_BROWSER',
        actionJson: { target: '' }
      });
      
      // 右侧文本
      defaultContents.push({
        contentId: `text-${pairIndex}-${Date.now()}`,
        type: 'text',
        content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
        pageId: 1,
        positionNumber: i * 2 + 4, // 从第4个位置开始
        pairIndex: pairIndex,
        layout: 'right',
        role: 'pair-text',
        isTextTitle: 0,
        pageLayout: 'right',
        pageLines: 4
      });
    }
    
    return defaultContents;
  },

  /**
   * 确保指定数量的图文对
   * @param {Array} contents 现有内容数组
   * @param {number} pairCount 图文对数量（1-3）
   * @returns {Array} 调整后的内容数组
   */
  ensureTextImagePairs(contents, pairCount = 1) {
    if (!Array.isArray(contents)) {
      contents = [];
    }

    // 限制图文对数量在1-3之间
    pairCount = Math.max(1, Math.min(3, pairCount));

    // 分离头部内容和图文对内容
    const headerImage = contents.find(content => content.type === 'image' && content.positionNumber === 1);
    const headerText = contents.find(content => content.type === 'text' && content.positionNumber === 2);
    
    // 按positionNumber分组现有的图文对内容
    const existingPairs = {};
    contents.forEach(content => {
      if (content.type === 'image' && content.positionNumber >= 3) {
        const pairIndex = Math.floor((content.positionNumber - 3) / 2) + 1;
        if (!existingPairs[pairIndex]) {
          existingPairs[pairIndex] = {};
        }
        existingPairs[pairIndex].image = content;
      } else if (content.type === 'text' && content.positionNumber >= 4) {
        const pairIndex = Math.floor((content.positionNumber - 4) / 2) + 1;
        if (!existingPairs[pairIndex]) {
          existingPairs[pairIndex] = {};
        }
        existingPairs[pairIndex].text = content;
      }
    });

    // 生成新的内容数组
    const newContents = [];
    
    // 添加头部内容
    if (headerImage) {
      newContents.push(headerImage);
    } else {
      newContents.push({
        contentId: `header-image-${Date.now()}`,
        type: 'image',
        content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内',
        src: '/aim_files/aim_defult/defaultImg.jpg',
        defaultSrc: '/aim_files/aim_defult/defaultImg.jpg',
        pageId: 1,
        positionNumber: 1,
        actionType: 'OPEN_BROWSER',
        actionJson: { target: '' }
      });
    }
    
    if (headerText) {
      newContents.push(headerText);
    } else {
      newContents.push({
        contentId: `header-text-${Date.now()}`,
        type: 'text',
        content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
        pageId: 1,
        positionNumber: 2
      });
    }

    // 生成指定数量的图文对
    for (let i = 1; i <= pairCount; i++) {
      const existingPair = existingPairs[i] || {};
      
      // 确保有图片内容
      if (!existingPair.image) {
        newContents.push({
          contentId: `image-${i}-${Date.now()}`,
          type: 'image',
          content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内',
          src: '/aim_files/aim_defult/defaultImg.jpg',
          defaultSrc: '/aim_files/aim_defult/defaultImg.jpg',
          pageId: 1,
          positionNumber: (i - 1) * 2 + 3,
          actionType: 'OPEN_BROWSER',
          actionJson: { target: '' }
        });
      } else {
        newContents.push(existingPair.image);
      }

      // 确保有文本内容
      if (!existingPair.text) {
        newContents.push({
          contentId: `text-${i}-${Date.now()}`,
          type: 'text',
          content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
          pageId: 1,
          positionNumber: (i - 1) * 2 + 4
        });
      } else {
        newContents.push(existingPair.text);
      }
    }

    console.log('ensureTextImagePairs - 生成的内容:', newContents.map(c => ({
      type: c.type,
      positionNumber: c.positionNumber,
      content: c.content?.substring(0, 20)
    })));

    // 按positionNumber排序
    return newContents.sort((a, b) => (a.positionNumber || 0) - (b.positionNumber || 0));
  },

  /**
   * 验证模板数据
   * @param {Object} template 模板数据
   * @returns {boolean} 是否有效
   */
  validateTemplate(template) {
    if (!template) return false;
    
    // 检查必要的字段
    if (!template.templateId || !template.templateName) {
      return false;
    }

    return true;
  },

  /**
   * 获取模板默认配置
   * @returns {Object} 默认配置
   */
  getDefaultConfig() {
    return {
      maxTextLength: 30,
      maxPairs: 3,
      minPairs: 1,
      defaultPairs: 1,
      allowedContentTypes: ['text', 'image']
    };
  },

  /**
   * 生成多图文模板的提交数据
   * @param {Array} contents 模板内容数组
   * @param {Object} settings 多图文设置 { pairCount }
   * @param {Object} options 其他选项 { isEditMode }
   * @returns {Array} 处理后的提交数据
   */
  generateSubmitData(contents, settings = {}, options = {}) {
    console.log('MultiTextTemplate.generateSubmitData - 开始处理提交数据:', {
      contents: contents.length,
      settings,
      options
    });

    const { pairCount = 1 } = settings;
    const { isEditMode = false } = options;

    // 分离不同类型的内容
    const textContents = contents.filter(c => c.type === 'text');
    const imageContents = contents.filter(c => c.type === 'image');
    const otherContents = contents.filter(c => !['text', 'image'].includes(c.type));

    console.log('MultiTextTemplate.generateSubmitData - 内容分类:', {
      text: textContents.length,
      image: imageContents.length,
      other: otherContents.length
    });

    // 识别图文对
    const textImagePairs = this.identifyTextImagePairs(textContents, imageContents);
    console.log('MultiTextTemplate.generateSubmitData - 识别到的图文对:', textImagePairs.map(pair => ({
      imagePosition: pair.image?.positionNumber,
      textPosition: pair.text?.positionNumber,
      imageContent: pair.image?.content,
      textContent: pair.text?.content
    })));

    // 过滤图文对（根据用户设置）
    const visiblePairs = textImagePairs.slice(0, pairCount);
    console.log('MultiTextTemplate.generateSubmitData - 可见图文对数量:', visiblePairs.length);

    // 收集所有需要提交的内容
    const submitContents = [];

    // 1. 添加头部内容（位置1和2）
    const headerImage = imageContents.find(c => c.positionNumber === 1);
    const headerText = textContents.find(c => c.positionNumber === 2 && c.isTextTitle === 1);
    
    if (headerImage) {
      submitContents.push(headerImage);
    }
    if (headerText) {
      submitContents.push(headerText);
    }

    // 2. 添加可见的图文对内容
    visiblePairs.forEach(pair => {
      if (pair.image) {
        submitContents.push(pair.image);
      }
      if (pair.text) {
        submitContents.push(pair.text);
      }
    });

    // 3. 添加其他内容
    submitContents.push(...otherContents);

    // 处理每个内容项
    const processedContents = submitContents.map(content => {
      const processedContent = JSON.parse(JSON.stringify(content));

      // 在编辑模式下保留contentId，新建模式下删除
      if (!isEditMode) {
        delete processedContent.contentId;
      } else if (!processedContent.contentId) {
        processedContent.contentId = this.generateUniqueId();
      }

      // 处理可能有HTML标签的文本内容
      if (processedContent.type === 'text' && processedContent.editContent) {
        processedContent.content = this.cleanTextContent(processedContent.editContent);
      }

      // 如果是图片类型，可以不传content或传空
      if (processedContent.type === 'image') {
        processedContent.content = '';
      }

      // 清理不需要的字段，只保留必要的字段
      const cleanContent = {};
      // 只保留基础字段，所有点击事件信息都通过actionJson传递
      const fieldsToKeep = [
        'type', 'content', 'src', 'positionNumber', 'isTextTitle',
        'actionType', 'actionJson'
      ];

      // 在编辑模式下保留contentId
      if (isEditMode) {
        fieldsToKeep.push('contentId');
      }

      fieldsToKeep.forEach(field => {
        // 特殊处理actionJson字段，它可能是对象或字符串
        if (field === 'actionJson') {
          if (processedContent[field] !== undefined && processedContent[field] !== null) {
            // 如果actionJson是字符串，尝试解析为对象
            if (typeof processedContent[field] === 'string') {
              try {
                // 首先尝试解析为JSON对象
                cleanContent[field] = JSON.parse(processedContent[field]);
              } catch (e) {
                // 如果解析失败，检查是否是逗号分隔的动作类型列表
                if (processedContent[field].includes(',')) {
                  // 这是一个动作类型列表，转换为默认的actionJson对象格式
                  cleanContent[field] = { target: '' };
                } else {
                  // 单个动作类型，转换为默认格式
                  cleanContent[field] = { target: '' };
                }
              }
            } else {
              // 如果是对象，直接使用
              cleanContent[field] = processedContent[field];
            }
          }
        } else if (field === 'positionNumber') {
          // positionNumber允许为0，只要不是undefined或null
          if (processedContent[field] !== undefined && processedContent[field] !== null) {
            cleanContent[field] = processedContent[field];
          }
        } else {
          // 其他字段保持原有逻辑
          if (processedContent[field] !== undefined && processedContent[field] !== null && processedContent[field] !== '') {
            cleanContent[field] = processedContent[field];
          }
        }
      });

      return cleanContent;
    });

    // 去重：按positionNumber和type去重，保留最后一个
    const uniqueContents = [];
    const seenKeys = new Set();
    
    // 按 positionNumber 排序，确保数据顺序正确
    processedContents.sort((a, b) => {
      const posA = a.positionNumber !== undefined ? a.positionNumber : 999;
      const posB = b.positionNumber !== undefined ? b.positionNumber : 999;
      return posA - posB;
    });
    
    // 从后往前遍历，保留每个positionNumber+type组合的最后一个
    for (let i = processedContents.length - 1; i >= 0; i--) {
      const content = processedContents[i];
      const key = `${content.positionNumber}-${content.type}`;
      
      if (!seenKeys.has(key)) {
        seenKeys.add(key);
        uniqueContents.unshift(content); // 添加到开头，保持顺序
      }
    }
    
    console.log('MultiTextTemplate.generateSubmitData - 去重前内容数量:', processedContents.length);
    console.log('MultiTextTemplate.generateSubmitData - 去重后内容数量:', uniqueContents.length);
    console.log('MultiTextTemplate.generateSubmitData - 最终提交内容:', uniqueContents);

    return uniqueContents;
  },

  /**
   * 识别图文对
   * @param {Array} textContents 文本内容数组
   * @param {Array} imageContents 图片内容数组
   * @returns {Array} 图文对数组
   */
  identifyTextImagePairs(textContents, imageContents) {
    console.log('MultiTextTemplate.identifyTextImagePairs - 开始识别图文对');
    
    const pairs = [];
    
    // 多图文模板的图文对位置规律：
    // 图片位置：3, 5, 7... (奇数位置从3开始)
    // 文本位置：4, 6, 8... (偶数位置从4开始)
    
    // 遍历所有文本内容，识别参数对
    const pairTexts = textContents.filter(content => 
      content.positionNumber >= 4 && 
      content.isTextTitle !== 1
    );
    
    console.log('MultiTextTemplate.identifyTextImagePairs - 找到的图文对文本:', pairTexts.map(t => ({
      positionNumber: t.positionNumber,
      content: t.content
    })));
    
    // 按positionNumber排序
    pairTexts.sort((a, b) => a.positionNumber - b.positionNumber);
    
    // 识别图文对
    for (let i = 0; i < pairTexts.length; i++) {
      const textContent = pairTexts[i];
      const textPosition = textContent.positionNumber;
      
      // 计算对应的图片位置：文本位置-1
      const imagePosition = textPosition - 1;
      
      // 查找对应的图片
      const imageContent = imageContents.find(img => img.positionNumber === imagePosition);
      
      console.log(`MultiTextTemplate.identifyTextImagePairs - 第${i + 1}对图文:`, {
        文本位置: textPosition,
        图片位置: imagePosition,
        文本内容: textContent.content,
        图片内容: imageContent ? '找到' : '未找到'
      });
      
      // 只添加至少有一个内容的对
      if (imageContent || textContent) {
        pairs.push({
          index: i + 1,
          image: imageContent || null,
          text: textContent || null
        });
      }
    }
    
    console.log('MultiTextTemplate.identifyTextImagePairs - 识别到的图文对数量:', pairs.length);
    return pairs;
  },

  /**
   * 清理文本内容，去除HTML标签，保留纯文本
   * @param {string} htmlContent HTML内容
   * @returns {string} 清理后的文本
   */
  cleanTextContent(htmlContent) {
    if (!htmlContent) return '';
    
    // 创建临时容器解析HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // 提取纯文本
    return tempDiv.textContent || tempDiv.innerText || '';
  },

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  generateUniqueId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}; 