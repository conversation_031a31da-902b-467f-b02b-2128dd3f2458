/**
 * 单卡券模板类
 * 处理单卡券样式的模板
 */

import BaseTemplate from './BaseTemplate.js';

class CardVoucherTemplate extends BaseTemplate {
  /**
   * 初始化模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容
   */
  static initializeContents(template) {
    // 设置全局变量标记当前不是红包模板
    window.TEMPLATE_IS_REDPACKET = false;
    
    // 如果模板已经有内容，使用基类方法初始化
    if (template && template.pages) {
      const contents = super.initializeContents(template);
      if (contents && contents.length > 0) {
        // 为内容添加单卡券特定的类名和处理
        contents.forEach(content => {
          // 添加单卡券特定的样式类名
          if (content.type === 'background') {
            content.className = 'card-voucher-background';
            content.aimOppoBackground = content.aimOppoBackground || '';
          } else if (content.type === 'text' && content.isTextTitle === 41) {
            // 优惠金额文本（如：100）
            content.className = 'voucher-amount';
          } else if (content.type === 'text' && content.positionNumber === 2) {
            // 使用条件文本（如：满1000可用）
            content.className = 'voucher-condition';
          } else if (content.type === 'text' && content.isTextTitle === 1) {
            // 主要描述文本
            content.className = 'voucher-description';
          } else if (content.type === 'text' && content.positionNumber === 4) {
            // 有效期文本
            content.className = 'voucher-validity';
          } else if (content.type === 'image' && content.positionNumber === 5) {
            // 中心图标
            content.className = 'voucher-icon';
          } else if (content.type === 'button') {
            content.className = 'voucher-button';
          }
        });
        return contents;
      }
    }
    
    // 如果没有内容，创建默认内容
    return
  }
  
 
  
  /**
   * 验证单卡券模板内容
   * @param {Array} contents 模板内容数组
   * @returns {boolean} 验证结果
   */
  static validateContents(contents) {
    // 首先使用基类验证
    if (!super.validateContents(contents)) {
      return false;
    }
    
    // 单卡券模板必须有背景和按钮
    const hasBackground = contents.some(content => 
      (content.type === 'background' || (content.type === 'image' && content.isBackground === 1))
    );
    
    const hasButton = contents.some(content => 
      content.type === 'button'
    );
    
    // 必须有优惠金额文本
    const hasAmount = contents.some(content => 
      content.type === 'text' && content.isTextTitle === 41
    );
    
    return hasBackground && hasButton && hasAmount;
  }
  
}

export default CardVoucherTemplate; 