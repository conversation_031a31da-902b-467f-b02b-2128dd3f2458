<template>
  <div class="template-body">
    <div class="category-container">
      <div class="category-left">
        <h3 class="category-title">模板分类</h3>
        <MediaCategory 
          :dir-type="5"
          :sub-type="subType"
          @category-change="handleCategoryChange"
          @update-app-key-dir-id="updateAppKeyAndDirId"
          @update-templates="(data, isFromMediaCategory) => updateTemplates(data, isFromMediaCategory)"
          @update-template-params="updateTemplateParams"
        />
      </div>
      <div class="category-right" v-if="selectedSubCount > 0"> 
        <div class="search-form-box">
          <el-form class="search-form" :model="searchForm" :inline="true">
            <el-form-item label="状态" prop="state">
              <el-select v-model="searchForm.state" placeholder="请选择">
                <el-option label="全部" value="" />
                <el-option label="草稿" value="-1" />
                <el-option label="一审驳回" value="-2" />
                <el-option label="二审驳回" value="-3" />
                <el-option label="无效" value="0" />
                <el-option label="有效" value="1" />
                <el-option label="一审通过" value="2" />
                <el-option label="待提交" value="3" />
                <el-option label="待审核" value="4" />
                <el-option label="已审核" value="5" />
                <el-option label="已驳回" value="6" />
                <el-option label="已启用" value="7" />
                <el-option label="已禁用" value="8" />
              </el-select>
            </el-form-item>
            <el-form-item label="模板名" prop="title">
              <el-input v-model="searchForm.title" placeholder="请输入模板名称" />
            </el-form-item>
          </el-form>
          <div class="search-btn">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </div>
        </div>
        <div class="template-box">
          <!-- 使用TemplateList组件 -->
          <TemplateList
            :templates="templates"
            :loading="isLoading"
            :showCreateButton="selectedSubCount === 1 && dirId !== '-1'"
            :showPagination="true"
            :total="total"
            :page="currentPage"
            :pageSize="pageSize"
            @create="addTemplateDialog"
            @edit="editTemplate"
            @delete="deleteTemplate"
            @select="viewDetails"
            @generate-short-link="generateShortLink"
            @view-details="viewDetails"
            @send-template="sendTemplate"
            @switch-change="handleSwitchChange"
            @page-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </div>
    </div>

      <!-- 新版模板编辑器 -->
      <TemplateEditor
        id="new-template-editor"
        ref="templateEditorRef"
        :app-key="appKey"
        :dir-id="dirId"
        :sub-type="subType"
        @submit="handleTemplateSubmit"
        @update-sub-type="handleSubTypeUpdate"
      />

      <!-- 生成短链弹窗 -->
      <ShortLinkDialog 
        ref="shortLinkDialogRef" 
        :domains="domains" 
      />

      <!-- 模板详情弹窗 -->
      <TemplateDetail 
        ref="templateDetailRef" 
      />

      <!-- 发送短链弹窗 -->
      <SendLinkDialog 
        ref="sendLinkDialogRef" 
      />
      </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import api from "@/api/index";
import TemplateList from '@/components/TemplateList.vue';
import MediaCategory from '@/components/MediaCategory.vue';
import TemplateEditor from '@/components/template/core/TemplateEditor.vue';
import ShortLinkDialog from '@/components/ShortLinkDialog.vue';
import TemplateDetail from '@/components/TemplateDetail.vue';
import SendLinkDialog from '@/components/SendLinkDialog.vue';
import { parseFactoryInfos } from '@/utils/factoryUtils';
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus';
import getTemBasicList from '@/api/getTemBasicList0.json';
import { CLICK_EVENT_TYPES } from '@/utils/clickEventManager';

const templateEditorRef = ref(null); // 创建新模板编辑器
const shortLinkDialogRef = ref(null);
const templateDetailRef = ref(null); // 模板详情弹窗
const sendLinkDialogRef = ref(null); // 发送短链弹窗
const appKey = ref('');
const dirId = ref('');
const subType = ref(''); // 添加subType
const domains = ref([]);
const isLoading = ref(false);
const templates = ref([]);
const selectedSubCount = ref(0); 
const total = ref(0); // 总数据条数
const pageSize = ref(10); // 每页显示数量，改为默认10条
const currentPage = ref(1); // 当前页码
let allTemplates = []; // 保存所有模板数据，用于前端分页

// 搜索表单数据
const searchForm = reactive({
  state: '', // 状态
  title: '' // 模板名称
});

// 在MediaCategory组件中更新分类选择时的处理函数
const handleCategoryChange = (selectedCategories) => {
  console.log('分类选择变化，当前选中:', selectedCategories.length, '个分类');
  selectedSubCount.value = selectedCategories.length;
  
  // 无论是否有分类选中，都重置页码
  currentPage.value = 1;
  console.log('分类变化: 重置页码到第1页');
  
  if (selectedCategories.length === 0) {
    console.log('没有选中任何分类，清空模板列表');
    templates.value = [];
    total.value = 0;
    allTemplates = [];
  }
};

// 更新appKey和dirId
const updateAppKeyAndDirId = (newAppKey, newDirId, newSubType) => {
  console.log('更新appKey和dirId:', { newAppKey, newDirId, newSubType });
  appKey.value = newAppKey;
  dirId.value = newDirId;
  // 如果传入了newSubType，则使用它；否则保持现有值
  if (newSubType !== undefined) {
    subType.value = newSubType;
  }
};

// 处理 subType 更新
const handleSubTypeUpdate = (newSubType) => {
  console.log('handleSubTypeUpdate 被调用，接收到新的 subType:', newSubType);
  console.log('更新前的 subType 值:', subType.value);
  // 只在newSubType有值时更新
  if (newSubType !== undefined && newSubType !== null && newSubType !== '') {
    subType.value = newSubType;
  }
};

// 修改标记模板列表，存储全部数据
const updateTemplates = async (newData, isFromMediaCategory = false) => {
  // 如果是来自MediaCategory的数据，且用户已经进行过搜索或分页操作，则忽略这次更新
  if (isFromMediaCategory && (searchForm.state || searchForm.title || currentPage.value > 1)) {
    console.log('忽略MediaCategory的数据更新，因为用户已进行搜索或分页操作');
    return;
  }
  
  isLoading.value = true;
  try {
    await new Promise(resolve => setTimeout(resolve, 300)); 
    
    // 重要：如果传入的新模板是空对象或空数组，需要清空现有数据
    if (!newData || (!newData.list && !Array.isArray(newData))) {
      console.log('收到空模板数据，清空模板列表');
      allTemplates = [];
      templates.value = [];
      total.value = 0;
      // 确保重置页码
      currentPage.value = 1;
      return;
    }
    
    // 处理新的数据格式
    const newTemplates = Array.isArray(newData) ? newData : newData.list;
    const newTotal = newData.total || newTemplates.length;
    // 如果明确设置了hasSubCategories，使用它的值，否则默认为true
    const hasSubCategories = newData.hasSubCategories !== undefined ? newData.hasSubCategories : true;
    
    console.log(`收到${newTemplates.length}个模板数据，总数：${newTotal}，是否有子分类：${hasSubCategories}，来源：${isFromMediaCategory ? 'MediaCategory' : '其他'}`);
    
    // 如果不是来自MediaCategory，或者是初始加载，才重置页码
    if (!isFromMediaCategory || currentPage.value === 1) {
      currentPage.value = 1;
      console.log('已重置页码到第一页');
    }
    
    // 按模板 ID 降序排序，新创建的模板在前面
    if (Array.isArray(newTemplates)) {
      newTemplates.sort((a, b) => b.templateId - a.templateId);
    }
    allTemplates = [...newTemplates];
    
    // 处理模板数据
    const processedTemplates = allTemplates.map(template => {
      // 从 channels 里的 factoryinfos 提取厂商信息
      let factoryInfos = '';
      if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
        factoryInfos = template.channels[0].factoryinfos;
      } else {
        factoryInfos = template.factoryInfos;
      }
      
      return { 
        ...template, 
        templateName: template.templateName || '', 
        isHovered: false, 
        isEnabled: template.state === 7, 
        tempIsEnabled: template.state === 7,
        factoryInfos // 更新 factoryInfos
      };
    });
    
    // 更新总数量
    total.value = newTotal;
    
    // 更新当前页的数据
    templates.value = processedTemplates;
    
    // 如果有子分类，保持selectedSubCount不变
    if (!hasSubCategories) {
      selectedSubCount.value = 0;
    }
    
    // 提取域名数据
    domains.value = [...new Set(allTemplates.flatMap(template => template.channels?.flatMap(channel => channel.domains?.split(',') || []) || []))];
  } finally {
    isLoading.value = false;
  }
};

// 显示创建模板对话框
const addTemplateDialog = async () => {
  // 始终使用新的TemplateEditor
  if (templateEditorRef.value) {
    // 创建新模板时，subType暂时为空，会在选择模板类型后设置
    templateEditorRef.value.open();
  } else {
    ElMessage.error('模板编辑器组件未加载，请刷新页面重试');
  }
};

// 生成短链
const generateShortLink = (template) => {
  let factoryInfos = template.factoryInfos;
  
  if (typeof factoryInfos === 'string') {
    // 直接用逗号分割字符串并去除首尾空格
    factoryInfos = factoryInfos.split(',').map(code => code.trim()).filter(code => code);
  } else if (!Array.isArray(factoryInfos)) {
    factoryInfos = [];
  }
  
  // 将数组转换为纯逗号分隔的字符串
  template.factoryInfos = factoryInfos.join(',');
  
  shortLinkDialogRef.value.open(template);
};

// 查看详情
const viewDetails = async (template) => {
  templateDetailRef.value.open(template);
};

//发送模板
const sendTemplate = (template) => {
  // 传递完整模板数据
  const templateWithCompletedData = {
    ...template,
    dirId: dirId.value,
    appKey: appKey.value
  };
  sendLinkDialogRef.value.open(templateWithCompletedData);
}

// 添加防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function(...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 修改模板启停方法
const handleSwitchChange = async (template, isChecked) => {
  // 保存初始状态
  const initialState = template.tempIsEnabled;
  // 临时恢复状态，阻止默认改变
  template.tempIsEnabled = initialState;

  if (!isChecked) {
    try {
      const { value: disableDesc } = await ElMessageBox.prompt(
        '模板禁用后，则无法生成新短链，且之前生成的短链无法进行解析。确认是否禁用？',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入禁用原因',
          inputType: 'textarea',
          inputValidator: (value) => {
            if (!value || value.trim() === '') {
              return '请输入禁用原因';
            }
            return true;
          }
        }
      );

      // 添加loading
      const loading = ElLoading.service({
        lock: true,
        text: '处理中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const data = {
        appKey: appKey.value,
        templateId: template.templateId,
        state: 8,
        disableDesc: disableDesc.trim()
      };

      await api.setTemplate(data);
      ElMessage.success('模板禁用成功');
      // 接口调用成功后更新临时状态和实际状态
      template.tempIsEnabled = isChecked;
      template.isEnabled = isChecked;
      template.state = 8;
      
      // 刷新模板列表
      await fetchTemplateList();
      
      loading.close();
    } catch (error) {
      if (error === 'cancel') {
        // 用户点击取消，保持初始状态
        template.tempIsEnabled = initialState;
      } else {
        // 接口调用失败，保持初始状态
        template.tempIsEnabled = initialState;
      }
    }
  } else {
    try {
      // 从列表数据中获取审核状态
      const auditState = template.channels?.[0]?.auditState;
      if (auditState !== '2') {
        ElMessage.warning('审核未通过，不能启用');
        template.tempIsEnabled = initialState;
        return;
      }

      // 添加loading
      const loading = ElLoading.service({
        lock: true,
        text: '处理中...',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const data = {
        appKey: appKey.value,
        templateId: template.templateId,
        state: 7,
        disableDesc: ''
      };
      
      await api.setTemplate(data);
      ElMessage.success('模板启用成功');
      // 接口调用成功后更新临时状态和实际状态
      template.tempIsEnabled = isChecked;
      template.isEnabled = isChecked;
      template.state = 7;
      
      // 刷新模板列表
      await fetchTemplateList();
      
      loading.close();
    } catch (error) {
      // 接口调用失败，保持初始状态
      template.tempIsEnabled = initialState;
    }
  }
};
// 删除模板方法
const deleteTemplate = async (template) => {
  try {
    const confirmMessage = `确认删除后，该模板（ID:${template.templateId}）将会消失，请确认是否删除`;
    await ElMessageBox.confirm(
      confirmMessage,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    await api.delTemplate({ templateId: template.templateId, appKey: appKey.value });
    ElMessage.success('删除成功');
    // 删除成功后，更新模板列表
    templates.value = templates.value.filter(t => t.templateId !== template.templateId);
    total.value -= 1; // 更新总条数

  } catch (error) {
    // 若用户点击取消，error 会是 'cancel'，此时不做额外处理
    if (error !== 'cancel') {
      console.error('模板删除失败:', error);
      ElMessage.error('删除失败，请重试');
    }
  }
};

const templateParams = ref([]);
const updateTemplateParams = (newParams) => {
  templateParams.value = newParams;
};

// 搜索处理
const handleSearch = async () => {
  currentPage.value = 1; // 重置页码为第一页
  await fetchTemplateList(); // 直接调用接口获取数据
};

// 重置处理
const handleReset = async () => {
  searchForm.state = '';
  searchForm.title = '';
  currentPage.value = 1; // 重置页码
  await fetchTemplateList(); // 直接调用接口获取数据
};

// 处理页码变化
const handlePageChange = async (page) => {
  console.log('Page changed to:', page);
  currentPage.value = page;
  await fetchTemplateList(); // 直接调用接口获取数据
};

// 处理页大小变化
const handleSizeChange = async (size) => {
  console.log('Size changed to:', size);
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  await fetchTemplateList(); // 直接调用接口获取数据
};

// 获取模板列表的方法
const fetchTemplateList = async (forceUserTemplates = false) => {
  if (!appKey.value || selectedSubCount.value === 0) return;
  
  isLoading.value = true;
  try {
    let isBaseTemplate;
    
    if (forceUserTemplates) {
      // 强制获取用户模板列表
      isBaseTemplate = false;
      console.log('强制获取用户模板列表，设置 isBase: 0');
    } else {
      // 正常判断是否为基础版式：检查dirId是否为负数或特殊标识
      const dirIds = dirId.value ? dirId.value.split(',') : [];
      isBaseTemplate = dirIds.some(id => {
        const numericDirId = parseInt(id);
        return numericDirId < 0 || id.toString().includes('base') || id.toString().includes('template');
      });
      console.log('fetchTemplateList - 判断是否为基础版式:', isBaseTemplate, '当前dirId:', dirId.value, '解析后的dirIds:', dirIds);
    }
    
    // 构建 dirIds 参数
    const params = {
      isBase: isBaseTemplate ? 1 : 0, // 根据版式类型或强制参数动态设置isBase
      appKey: appKey.value,
      dirIds: dirId.value || '',
      page: currentPage.value,
      limit: pageSize.value,
      state: searchForm.state,
      title: searchForm.title,
    };
    
    console.log('请求模板列表参数:', params);
    const res = await api.getTemBasicList(params);
    // const res = getTemBasicList
    
    if (res.data && Array.isArray(res.data.list)) {
      const processedTemplates = res.data.list.map(template => {
        // 改进 pages 字段处理逻辑 - 更好地处理API返回的数据
        if (!template.pages) {
          template.pages = [];
        } else if (typeof template.pages === 'string') {
          // 处理字符串类型的pages数据
          if (template.pages === '[object Object]' || template.pages === 'null' || template.pages === 'undefined') {
            // 如果是无效的字符串，尝试从其他字段获取数据或设为空数组
            console.warn('模板pages数据异常:', template.templateId, template.pages);
            template.pages = [];
          } else if (template.pages.trim() === '') {
            // 空字符串
            template.pages = [];
          } else {
            try {
              const parsedPages = JSON.parse(template.pages);
              template.pages = parsedPages;
              console.log('成功解析模板pages数据:', template.templateId, template.pages);
            } catch (e) {
              console.error('解析模板pages数据失败:', template.templateId, template.pages, e);
              // 解析失败时，尝试从模板的其他信息重建基本结构
              template.pages = [{
                pageId: 1,
                contents: []
              }];
            }
          }
        } else if (typeof template.pages === 'object' && template.pages !== null) {
          // 如果是对象但不是数组，转换为数组
          if (!Array.isArray(template.pages)) {
            template.pages = [template.pages];
          }
        } else {
          // 其他情况设为空数组
          template.pages = [];
        }

        // 确保pages是有效的数组结构
        if (Array.isArray(template.pages)) {
          template.pages = template.pages.map(page => {
            // 确保每个页面都有基本结构
            if (!page || typeof page !== 'object') {
              return {
                pageId: 1,
                contents: []
              };
            }
            
            // 确保页面有contents数组
            if (!page.contents) {
              page.contents = [];
            } else if (!Array.isArray(page.contents)) {
              // 如果contents不是数组，尝试转换
              if (typeof page.contents === 'object') {
                page.contents = [page.contents];
              } else {
                page.contents = [];
              }
            }
            
            return {
              pageId: page.pageId || 1,
              contents: page.contents,
              ...page
            };
          });
        }

        // 从 channels 里的 factoryinfos 提取厂商信息
        if (template.channels && template.channels.length > 0 && template.channels[0].factoryinfos) {
          template.factoryInfos = template.channels[0].factoryinfos;
        }

        return {
          ...template,
          isHovered: false,
          isEnabled: template.state === 7,
          tempIsEnabled: template.state === 7,
          // 确保模板名称不为空
          templateName: template.templateName || '未命名模板',
          // 确保有完整的模板数据用于显示
          scene: template.scene || '',
          tplType: template.tplType || '',
          timeCreate: template.timeCreate || '',
          timeUpdate: template.timeUpdate || ''
        };
      });

      // 更新模板列表和总数
      templates.value = processedTemplates;
      total.value = res.data.total || 0;
      
      console.log('模板列表更新完成，共', processedTemplates.length, '个模板');

      // 提取动态参数
      const allParams = new Set();
      processedTemplates.forEach(template => {
        if (template.pages && Array.isArray(template.pages)) {
          template.pages.forEach(page => {
            if (page.contents && Array.isArray(page.contents)) {
              page.contents.forEach(content => {
                if (content.content) {
                  const matches = content.content.match(/\{#(.*?)#\}/g);
                  if (matches) {
                    matches.forEach(match => {
                      const paramName = match.replace(/\{#|#\}/g, '');
                      allParams.add(paramName);
                    });
                  }
                }
              });
            }
          });
        }
      });
      templateParams.value = Array.from(allParams).map(name => ({ name }));
    } else {
      templates.value = [];
      total.value = 0;
      templateParams.value = [];
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    templates.value = [];
    total.value = 0;
    templateParams.value = [];
  } finally {
    isLoading.value = false;
  }
};

// 修改editTemplate方法
const editTemplate = async (template) => {
  // console.log('Editing template:', template);
  try {
    const res = await api.getTemplate({ templateId: template.templateId });
    console.log('getTemplate 接口返回数据:', res.data.userId);
    if (res.code === 0) {
      const templateData = res.data;
      
      // 从模板数据中获取subType并设置到响应式变量
      if (templateData.subType) {
        subType.value = templateData.subType;
        console.log('从模板数据中获取subType:', templateData.subType);
      }
      
      // 处理模板数据
      let processedTemplateData = {
        ...templateData,
        templateId: templateData.templateId,
        appKey: templateData.appKey || appKey.value,
        dirId: templateData.dirId || dirId.value,
        userId: templateData.userId
      };

      // 处理厂商信息 - 优先使用根级别的factoryInfos
      if (templateData.factoryInfos) {
        try {
          let factoryInfos = templateData.factoryInfos;
          if (typeof factoryInfos === 'string') {
            // 尝试解析JSON字符串
            try {
              factoryInfos = JSON.parse(factoryInfos);
            } catch (e) {
              // 如果解析失败，说明是逗号分隔的字符串
              factoryInfos = factoryInfos.split(',').map(item => item.trim());
            }
          }
          // 确保是数组格式
          if (!Array.isArray(factoryInfos)) {
            factoryInfos = [factoryInfos];
          }
          // 过滤空值并转换为字符串
          processedTemplateData.factoryInfos = factoryInfos.filter(Boolean).join(',');
        } catch (e) {
          console.error('处理厂商信息失败:', e);
        }
      }

      // 处理pages数据
      if (processedTemplateData.pages) {
        try {
          if (typeof processedTemplateData.pages === 'string') {
            processedTemplateData.pages = JSON.parse(processedTemplateData.pages);
          }
          if (!Array.isArray(processedTemplateData.pages)) {
            processedTemplateData.pages = [processedTemplateData.pages];
          }
          
          // 处理每个页面的内容
          processedTemplateData.pages = processedTemplateData.pages.map(page => {
            if (page.contents && !Array.isArray(page.contents)) {
              page.contents = [page.contents];
            }
            
            // 确保每个content都有正确的actionType和其他必要属性
            if (page.contents) {
              page.contents = page.contents.map(content => {
                let processedContent = { ...content };
                
                // 处理actionJson
                if (content.actionJson) {
                  try {
                    let actionData;
                    if (typeof content.actionJson === 'string') {
                      // 如果是逗号分隔的字符串，说明是可选的action类型列表
                      if (content.actionJson.includes(',')) {
                        processedContent.actionTypes = content.actionJson.split(',').map(type => type.trim());
                      } else {
                        // 尝试解析为JSON对象
                        try {
                          actionData = JSON.parse(content.actionJson);
                        } catch (e) {
                          actionData = { target: content.actionJson };
                        }
                      }
                    } else {
                      actionData = content.actionJson;
                    }

                    // 如果解析出了actionData，设置相应的属性
                    if (actionData) {
                      // 设置actionType - 保持原始的actionType
                      processedContent.actionType = content.actionType || CLICK_EVENT_TYPES.OPEN_BROWSER;
                      
                      // 设置actionUrl
                      if (actionData.target) {
                        processedContent.actionUrl = actionData.target;
                      } else if (actionData.data) {
                        if (actionData.data.scheme) {
                          processedContent.actionUrl = actionData.data.scheme;
                        } else if (actionData.data.number) {
                          processedContent.actionUrl = actionData.data.number;
                        } else if (actionData.data.content) {
                          processedContent.actionUrl = actionData.data.content;
                        }
                      }
                      
                      // 设置actionPath（用于短信内容等）
                      if (actionData.data && actionData.data.content) {
                        processedContent.actionPath = actionData.data.content;
                      }
                      
                      // 保存原始的actionJson以备后用
                      processedContent.originalActionJson = actionData;
                    }
                  } catch (e) {
                    console.error('解析actionJson失败:', e);
                  }
                }
                
                return processedContent;
              });
            }
            return page;
          });
        } catch (e) {
          console.error('解析pages数据失败:', e);
          processedTemplateData.pages = [];
        }
      }

      console.log('处理后的模板数据:', processedTemplateData);
      
      // 先触发版式选中事件
      const selectEvent = new CustomEvent('template-category-select', {
        detail: {
          dirId: processedTemplateData.dirId,
          appKey: processedTemplateData.appKey,
          shouldHighlight: true
        }
      });
      document.dispatchEvent(selectEvent);
      
      // 等待一段时间后再打开编辑器，确保版式选中状态已更新
      setTimeout(() => {
        // 确保版式选中状态在DOM更新后生效
        const categoryElement = document.querySelector(`.category-item[data-dir-id="${processedTemplateData.dirId}"]`);
        if (categoryElement) {
          // 移除其他选中状态
          document.querySelectorAll('.category-item').forEach(item => {
            item.classList.remove('selected');
          });
          // 添加选中状态
          categoryElement.classList.add('selected');
          // 确保版式可见
          categoryElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        
        // 打开编辑器
        if (templateEditorRef.value) {
          templateEditorRef.value.openEdit(processedTemplateData);
        } else {
          ElMessage.error('模板编辑器组件未加载，请刷新页面重试');
        }
      }, 100);
    } else {
      ElMessage.error(res.message || '获取模板编辑数据失败，请重试');
    }
  } catch (error) {
    console.error('获取模板编辑失败:', error);
    ElMessage.error('获取模板编辑失败，请重试');
  }
};
const handleTemplateSubmit = async (submitResult) => {
  console.log('模板提交成功，准备刷新列表', submitResult);
  
  // 刷新模板列表 - 确保获取最新的API数据
  await fetchTemplateList();
  
  // 如果是编辑模式，需要更新当前显示的模板数据
  if (submitResult.isEdit && submitResult.data) {
    const index = templates.value.findIndex(t => t.templateId === submitResult.data.templateId);
    if (index !== -1) {
      // 获取刷新后的模板数据，确保使用API返回的最新数据
      const refreshedTemplate = templates.value[index];
      
      // 更新模板数据，优先使用API返回的数据，但保留提交成功的关键信息
      templates.value[index] = {
        ...refreshedTemplate, // 使用API返回的完整数据
        templateName: submitResult.data.templateName || refreshedTemplate.templateName, // 确保模板名称正确更新
        state: submitResult.data.state || refreshedTemplate.state, // 确保状态正确
        isHovered: false,
        isEnabled: (submitResult.data.state || refreshedTemplate.state) === 7,
        tempIsEnabled: (submitResult.data.state || refreshedTemplate.state) === 7,
        // 确保pages数据是最新的
        pages: refreshedTemplate.pages || submitResult.data.pages
      };
      
      console.log('更新后的模板数据:', templates.value[index]);
      
      // 在更新本地数据后，再次触发更新事件
      const updateEvent = new CustomEvent('template-updated', {
        detail: templates.value[index]
      });
      window.dispatchEvent(updateEvent);
      
      // 如果有全局事件总线，也通过事件总线发送
      if (window.TEMPLATE_EVENT_BUS) {
        window.TEMPLATE_EVENT_BUS.emit('template-updated', templates.value[index]);
      }
      
      console.log('触发模板更新事件，最终数据:', templates.value[index]);
    }
  } else if (submitResult.data) {
    // 新建模式，重新获取列表确保显示最新数据
    await fetchTemplateList();
    
    // 触发更新事件
    const updateEvent = new CustomEvent('template-updated', {
      detail: submitResult.data
    });
    window.dispatchEvent(updateEvent);
    
    // 如果有全局事件总线，也通过事件总线发送
    if (window.TEMPLATE_EVENT_BUS) {
      window.TEMPLATE_EVENT_BUS.emit('template-updated', submitResult.data);
    }
  }
};

// 在组件挂载时添加事件监听
onMounted(async () => {
  // 监听模板更新事件
  const handleTemplateUpdate = (event) => {
    console.log('收到模板更新事件:', event.detail);
    // 刷新模板列表以确保显示最新数据
    if (appKey.value && selectedSubCount.value > 0) {
      fetchTemplateList();
    }
  };
  
  // 监听模板编辑器关闭事件
  const handleTemplateEditorClose = () => {
    console.log('模板编辑器已关闭，刷新列表数据');
    // 确保在编辑器关闭后刷新列表，显示真实的API数据
    if (appKey.value && selectedSubCount.value > 0) {
      setTimeout(() => {
        // 强制调用用户模板列表，而不是基础模板列表
        fetchTemplateList(true); // 传入参数表示强制获取用户模板
      }, 100); // 延迟一点确保编辑器完全关闭
    }
  };
  
  window.addEventListener('template-updated', handleTemplateUpdate);
  window.addEventListener('template-editor-closed', handleTemplateEditorClose);
  
  // 在组件卸载时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('template-updated', handleTemplateUpdate);
    window.removeEventListener('template-editor-closed', handleTemplateEditorClose);
  });
});
</script>

<style scoped lang="scss">
.category-container {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;
  background: #fff;
}

.category-left {
  width: 220px;
  border: 1px solid #ddd;
  padding: 20px 16px;
  height: 86vh;
  overflow: auto;
  
  .category-title {
    margin-bottom: 10px;
    font-weight: bold;
    font-size: 14px;
  }
}

.category-right {
  flex: 1;
  margin-left: 10px;
  overflow: auto;
}

.search-form-box {
  display: flex;
  padding: 0 14px 0 10px;
  margin-bottom: 15px;
}

.search-form {
  flex: 1;
  
  .el-select {
    --el-select-width: 220px;
  }
}

.el-message-box__status.el-message-box-icon--warning{
  font-size: 24px;
}
</style>