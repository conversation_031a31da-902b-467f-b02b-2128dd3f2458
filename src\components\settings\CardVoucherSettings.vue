<template>
  <div class="card-voucher-settings">
    <!-- 券类型 -->
    <div class="setting-item setting-card">
      <div>
        <h4 class="setting-title">券类型</h4>
        <div class="voucher-type-options">
          <el-radio-group v-model="voucherType" @change="handleVoucherTypeChange">
            <el-radio value="1">满减券</el-radio>
            <!-- <el-radio value="2">折扣券</el-radio>
            <el-radio value="3">自定义</el-radio> -->
          </el-radio-group>
        </div>
      </div>
  
       <!-- 金额设置 -->
      <div class="setting-cell flex-wrap" v-if="voucherType === '1'">
        <div class="setting-cell-amount">
          "<span>￥</span>" 图标显示/隐藏
          <el-switch 
            class="setting-switch"
            v-model="currencyDisplay"
            @change="handleCurrencyDisplayChange"
          />
        </div>
        <div class="setting-cell-condition">
          <span class="setting-label">满</span>
          <el-input 
            class="setting-input"
            v-model="minAmount" 
            @input="handleMinAmountInput"
            type="number"
            :min="0"
            placeholder="请输入满减金额"
            :class="{ 'is-error': minAmountError }"
          />
          <span>元 减</span>
          <el-input 
            class="setting-input"
            v-model="discountAmount" 
            @input="handleDiscountAmountInput"
            type="number"
            :min="0"
            :max="minAmount || 0"
            placeholder="请输入优惠金额"
            :class="{ 'is-error': discountAmountError }"
          />
          <span>元</span>
        </div>
      </div>

      <!-- <div class="setting-cell" v-else-if="voucherType === '2'">
        <span class="setting-label">满</span>
        <el-input 
          class="setting-input"
          v-model="minAmount" 
          placeholder="1000"
          @input="updateConditionText"
        />
        <span>元 打</span>
        <el-input 
          class="setting-input"
          v-model="discountAmount" 
          placeholder="8.6"
          @input="updateAmountAndCondition"
        />
        <span>元</span>
      </div>

      <div class="setting-cell" v-if="voucherType === '3'">
        <el-input 
          v-model="customConditionText" 
          placeholder="输入自定义条件文本"
          @input="updateCustomConditionText"
        />
      </div> -->
    </div>
    
    <!-- 主标题编辑 -->
    <div class="setting-item setting-card">
      <h4 class="setting-title">券名称</h4>
      <div class="setting-cell mt-20">
        <RichParamInput
        v-model="voucherName"
        :maxLength="15"
        :showParamButtons="false"
        componentType="text"
      /> 
      </div>
    </div>
    <!-- 有效期 -->
    <div class="setting-item setting-card">
      <div>

      </div>
      <h4 class="setting-title">有效期</h4>
      <div class="validity-options setting-cell">
        <el-radio-group v-model="validityType" @change="handleValidityTypeChange">
          <el-radio value="1">固定日期</el-radio>
          <!-- <el-radio value="2">领取后</el-radio>
          <el-radio value="3">自定义</el-radio> -->
        </el-radio-group>
      </div>
      <!-- 固定日期选择 -->
      <div class="setting-cell" v-if="validityType === '1'">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="—"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="updateValidityText"
          style="width: 100%;"
        />
      </div>
      <!-- <div class="setting-cell" v-if="validityType === '2'">
        <el-input 
          class="setting-input"
          v-model="leadDays" 
          placeholder="10"
          @input="updateValidityText"
        />
        <span>天过期</span>
      </div>
      <div class="setting-cell" v-if="validityType === '3'">
        <RichParamInput
        
        />
      </div> -->
    </div>

    <div class="setting-item setting-card">
      <h4 class="setting-title">编辑logo图</h4>
      <div class="logo-container">
        <ImageElement
          v-if="logoContent"
          :content="logoContent"
          :is-selected="false"
          :usage="'settings'"
          :get-media-url="getMediaUrl"
          class="settings-image"
          @select="handleImageSelect"
          @update:content="handleLogoUpdate"
        />
        <!-- 当前图片描述 -->
        <div class="setting-desc" v-if="logoContent && logoContent.content">
          {{ logoContent.content }}
        </div>
        <el-button
          type="primary"
          class="select-btn"
          @click="selectImageFile"
        >
          选择图片
        </el-button>
      </div>
      <!-- logo点击事件设置 -->
      <div class="setting-cell">
        <ClickEventSettings 
          v-if="logoContent"
          :content="logoContent"
          @update:content="handleLogoClickEventUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
        />
      </div>
    </div>
    
    <div class="setting-item setting-card">
      <h4 class="setting-title">编辑按钮</h4>
      <div class="setting-cell button-setting">
        <div class="tip">按钮名称（最多15位）</div>
        <RichParamInput
          v-model="buttonText"
          :maxLength="15"
          :showParamButtons="false"
          componentType="text"
        />
      </div> 
      <div class="setting-cell"> 
        <!-- 按钮点击事件设置 -->
        <ClickEventSettings 
          v-if="buttonContent"
          :content="buttonContent"
          @update:content="handleButtonClickEventUpdate"
          @param-insert="handleParamInsert"
          @param-manage="handleParamManage"
        />
      </div>
    </div>
  </div>
  
  <!-- 素材库选择弹框 -->
  <MediaSelectorDialog 
    v-model="dialogVisible"
    mediaType="image"
    :filter-app-key="appKey"
    @confirm="handleMediaSelect"
  />
</template>

<script setup>
import { ref, watch, inject, onMounted, computed, onBeforeUnmount } from 'vue';
import { Plus, Close, Picture } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import RichParamInput from '../richtext/RichParamInput.vue';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import ImageElement from '../template/preview/elements/ImageElement.vue';
import ClickEventSettings from './ClickEventSettings.vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import {ActionJsonGenerator, CLICK_EVENT_TYPES} from '@/utils/ClickEventManager';

// 定义事件
const emit = defineEmits([
  'update:content',
  'update',
  'close',
  'param-insert',
  'param-manage',
  'input',
  'contents-change',
  'content-change'
]);

// 定义props
const props = defineProps({
  templateData: {
    type: Object,
    default: () => ({})
  },
  content: {  
    type: Object, 
    required: true 
  },
  contents: {  // 添加contents属性定义
    type: Array,
    default: () => []
  },
  template: {  // 添加template属性定义
    type: Object,
    default: () => ({})
  },
  appKey: {
    type: String,
    default: ''
  }
});

// 组件状态
const voucherType = ref('1'); // 券类型：满减券、折扣券、自定义
const minAmount = ref(''); // 满减条件金额
const discountAmount = ref(''); // 减免金额
const discountRate = ref(''); // 折扣率
const voucherName = ref(''); // 券名称
const validityType = ref('1'); // 有效期类型
const dateRange = ref(['']); // 固定日期范围
const leadDays = ref(''); // 领后天数
const customValidityText = ref(''); // 自定义有效期文本
const logoImage = ref(''); // logo图片
const eventType = ref(''); // 图标点击事件类型
const linkUrl = ref(''); // 图标点击链接
const buttonText = ref(''); // 按钮文本
const buttonEventType = ref(''); // 按钮事件类型
const buttonLinkUrl = ref(''); // 按钮点击链接

// 注入共享数据
const templateContents = inject('templateContents', ref([]));
const selectedTemplateContents = inject('selectedTemplateContents', ref([]));
const mediaDialogRef = inject('mediaDialogRef', null);

// 添加货币符号显示状态
const currencyDisplay = ref(true);

// 计算属性：获取当前模板内容
const currentContents = computed(() => {
  console.log('CardVoucherSettings - 计算属性被调用');
  console.log('CardVoucherSettings - props.contents:', props.contents);
  console.log('CardVoucherSettings - selectedTemplateContents.value:', selectedTemplateContents.value);
  
  // 直接使用props.contents，这是从TemplateSettings传入的selectedTemplateContents
  const contents = props.contents && props.contents.length > 0 
    ? props.contents 
    : selectedTemplateContents.value;
  
  console.log('CardVoucherSettings - 最终使用的内容:', contents);
  return contents;
});

// 从模板内容同步到本地状态
const syncFromContents = (contents) => {
  console.log('CardVoucherSettings - syncFromContents 被调用, contents:', contents);
  
  if (!contents || !Array.isArray(contents)) {
    console.log('CardVoucherSettings - 内容为空或不是数组');
    return;
  }
  
  // 获取第一个内容的货币符号显示状态
  if (contents.length > 0) {
    currencyDisplay.value = contents[0].aimCurrencyDisplay !== 0;
  }
  
  contents.forEach((content, index) => {
    console.log(`CardVoucherSettings - 处理内容 ${index}:`, content);
    
    if (content.type === 'text' && content.positionNumber === 1) {
      // 优惠金额
      console.log('CardVoucherSettings - 找到金额内容:', content.content);
      discountAmount.value = content.content || '';
    } else if (content.type === 'text' && content.positionNumber === 3) {
      // 使用条件
      const conditionText = content.content || '';
      console.log('CardVoucherSettings - 找到条件内容:', conditionText);
        const match = conditionText.match(/满(\d+)可用/);
        if (match) {
        console.log('CardVoucherSettings - 提取到金额:', match[1]);
          minAmount.value = match[1];
      }
    } else if (content.type === 'text' && content.positionNumber === 4) {
      // 描述文本
      console.log('CardVoucherSettings - 找到描述内容:', content.content);
      voucherName.value = content.content || '';
    } else if (content.type === 'text' && content.positionNumber === 5) {
      // 有效期
      const validityText = content.content || '';
      console.log('CardVoucherSettings - 找到有效期内容:', validityText);
      if (validityText.includes('~')) {
        const dates = validityText.split('~');
        if (dates.length === 2) {
          dateRange.value = [dates[0].trim(), dates[1].trim()];
          validityType.value = '1';
          console.log('CardVoucherSettings - 设置日期范围:', dateRange.value);
        }
      }
    } else if (content.type === 'image' && content.positionNumber === 6) {
      // logo图片
      logoImage.value = content.src || '';
    } else if (content.type === 'button' && content.positionNumber === 7) {
      // 按钮
      buttonText.value = content.content || '立即领取';
      if (content.clickEvent) {
        buttonEventType.value = content.clickEvent.type || CLICK_EVENT_TYPES.OPEN_BROWSER;
        buttonLinkUrl.value = content.clickEvent.target || '';
      }
    }
  });
  
  console.log('CardVoucherSettings - 同步完成后的状态:');
  console.log('  - discountAmount:', discountAmount.value);
  console.log('  - minAmount:', minAmount.value);
  console.log('  - voucherName:', voucherName.value);
  console.log('  - dateRange:', dateRange.value);
};

// 错误状态
const minAmountError = ref('');
const discountAmountError = ref('');



// 处理满减金额输入
const handleMinAmountInput = () => {
  console.log('CardVoucherSettings - handleMinAmountInput 被调用, minAmount:', minAmount.value);
  
  // 清除错误提示
  minAmountError.value = '';
  
  // 如果输入非数字，直接返回
  if (minAmount.value && !/^\d*\.?\d*$/.test(minAmount.value)) {
    minAmount.value = minAmount.value.replace(/[^\d.]/g, '');
    return;
  }
  
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents || !Array.isArray(contents)) {
    console.log('CardVoucherSettings - handleMinAmountInput: contents无效');
    return;
  }

  const conditionContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 3
  );
  
  if (conditionContent) {
    console.log('CardVoucherSettings - 找到条件内容，更新为:', `满${minAmount.value}可用`);
    // 更新原始内容对象
    conditionContent.content = `满${minAmount.value}可用`;
    // 同时更新editContent字段，确保prepareTemplateData能正确使用
    conditionContent.editContent = `满${minAmount.value}可用`;
    
    // 发送更新事件
    console.log('CardVoucherSettings - 发送更新事件，contents:', contents);
    emitContentsUpdate();
    // 立即同步到全局状态
    syncToGlobalState();
  } else {
    console.log('CardVoucherSettings - 未找到positionNumber为3的条件内容');
  }
};

// 处理优惠金额输入
const handleDiscountAmountInput = () => {
  console.log('CardVoucherSettings - handleDiscountAmountInput 被调用, discountAmount:', discountAmount.value);
  
  // 清除错误提示
  discountAmountError.value = '';
  
  // 如果输入非数字，直接返回
  if (discountAmount.value && !/^\d*\.?\d*$/.test(discountAmount.value)) {
    discountAmount.value = discountAmount.value.replace(/[^\d.]/g, '');
    return;
  }
  
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents || !Array.isArray(contents)) {
    console.log('CardVoucherSettings - handleDiscountAmountInput: contents无效');
    return;
  }

  const amountContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 1
  );
  
  if (amountContent) {
    console.log('CardVoucherSettings - 找到金额内容，更新为:', discountAmount.value);
    // 更新原始内容对象
    amountContent.content = discountAmount.value;
    // 同时更新editContent字段，确保prepareTemplateData能正确使用
    amountContent.editContent = discountAmount.value;
    
    // 发送更新事件
    console.log('CardVoucherSettings - 发送更新事件，contents:', contents);
    emitContentsUpdate();
    // 立即同步到全局状态
    syncToGlobalState();
  } else {
    console.log('CardVoucherSettings - 未找到positionNumber为1的金额内容');
  }
};

// 添加调试日志
console.log('CardVoucherSettings - 当前内容:', currentContents.value);
console.log('CardVoucherSettings - 金额值:', discountAmount.value);
console.log('CardVoucherSettings - 满减值:', minAmount.value);

// 监听内容变化
watch(() => currentContents.value, (newContents) => {
  console.log('CardVoucherSettings - watch 触发，新内容:', newContents);
  if (newContents && newContents.length > 0) {
    syncFromContents(newContents);
  }
}, { immediate: true, deep: true });

// 监听props.contents的变化
watch(() => props.contents, (newContents) => {
  console.log('CardVoucherSettings - props.contents 变化:', newContents);
  if (newContents && newContents.length > 0) {
    syncFromContents(newContents);
  }
}, { immediate: true, deep: true });

// 监听券名称变化
watch(voucherName, (newValue) => {
  console.log('CardVoucherSettings - voucherName 变化:', newValue);
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents || !Array.isArray(contents)) {
    console.log('CardVoucherSettings - voucherName watch: contents无效');
    return;
  }

  const nameContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 4
  );
  
  if (nameContent) {
    console.log('CardVoucherSettings - 找到券名称内容，更新为:', newValue);
    // 更新原始内容对象
    nameContent.content = newValue;
    // 同时更新editContent字段，确保prepareTemplateData能正确使用
    nameContent.editContent = newValue;
    
    console.log('CardVoucherSettings - 发送券名称更新事件，contents:', contents);
    emitContentsUpdate();
    // 立即同步到全局状态
    syncToGlobalState();
  } else {
    console.log('CardVoucherSettings - 未找到券名称内容');
  }
}, { immediate: false });

// 处理有效期更新
const updateValidityText = () => {
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  const validityContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 5
  );
  if (validityContent) {
    if (validityType.value === '1' && dateRange.value[0] && dateRange.value[1]) {
      // 更新原始内容对象
      validityContent.content = `${dateRange.value[0]}~${dateRange.value[1]}`;
      // 同时更新editContent字段
      validityContent.editContent = `${dateRange.value[0]}~${dateRange.value[1]}`;
      
      // 发送更新事件
      emitContentsUpdate();
      // 立即同步到全局状态
      syncToGlobalState();
    }
  }
};

// 处理券类型变更
const handleVoucherTypeChange = () => {
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  // 更新金额
  const amountContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 1
  );
  if (amountContent) {
    amountContent.content = discountAmount.value;
    amountContent.editContent = discountAmount.value;
  }

  // 更新使用条件
  const conditionContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 3
  );
  if (conditionContent) {
    conditionContent.content = `满${minAmount.value}可用`;
    conditionContent.editContent = `满${minAmount.value}可用`;
  }

  // 发送更新事件
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
};

// 处理有效期类型变更
const handleValidityTypeChange = () => {
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  // 更新有效期
  const validityContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 5
  );
  if (validityContent) {
    if (validityType.value === '1' && dateRange.value[0] && dateRange.value[1]) {
      validityContent.content = `${dateRange.value[0]}~${dateRange.value[1]}`;
      validityContent.editContent = `${dateRange.value[0]}~${dateRange.value[1]}`;
    }
  }

  // 发送更新事件
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
};

// 确认券设置
const confirmSettings = () => {
  ElMessage.success('单卡券设置已更新');
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  // 更新金额
  const amountContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 1
  );
  if (amountContent) {
    amountContent.content = discountAmount.value;
    amountContent.editContent = discountAmount.value;
  }

  // 更新使用条件
  const conditionContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 3
  );
  if (conditionContent) {
    conditionContent.content = `满${minAmount.value}可用`;
    conditionContent.editContent = `满${minAmount.value}可用`;
  }

  // 发送更新事件
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
};

// 确认按钮设置
const confirmButtonSettings = () => {
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  // 更新按钮
  const buttonContent = contents.find(content => 
    content.type === 'button' && content.positionNumber === 7
  );
  if (buttonContent) {
    buttonContent.content = buttonText.value;
    buttonContent.editContent = buttonText.value;
    if (buttonEventType.value && buttonLinkUrl.value) {
      buttonContent.clickEvent = {
        type: buttonEventType.value,
        target: buttonLinkUrl.value
      };
    }
  }

  // 发送更新事件
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
  ElMessage.success('按钮设置已更新');
};

// 图片选择相关
const dialogVisible = ref(false);

// 选择图片文件
const selectImageFile = () => {
  console.log('CardVoucherSettings - 打开媒体选择弹框');
  dialogVisible.value = true;
};

// 处理媒体选择
const handleMediaSelect = (media) => {
  console.log('CardVoucherSettings - 选择了媒体:', media);
  
  if (media && (media.mediaUrl || media.path)) {
    const sourceAppKey = media.appKey || props.appKey;
    
    if (!sourceAppKey) {
      console.error('错误：无法获取有效的appKey！');
      ElMessage.error('无法获取素材的有效appKey，请重试');
      return;
    }
    
    const relativePath = `/aim_files/${sourceAppKey}/${media.path}`;
    // 使用当前内容
    const contents = currentContents.value;
    
    // 查找logo内容
    const logoContent = contents.find(content => 
      content.type === 'image' && content.positionNumber === 6
    );
    
    if (logoContent) {
      // 更新logo图片
      logoContent.src = relativePath;
      logoContent.alt = media.mediaDesc || 'logo图片';
      
      console.log('CardVoucherSettings - 发送更新事件，contents:', contents);
      emitContentsUpdate();
      // 立即同步到全局状态
      syncToGlobalState();
      
      ElMessage.success('logo图片设置成功');
    } else {
      console.error('未找到logo图片内容');
      ElMessage.error('设置失败：未找到logo图片内容');
    }
  } else {
    console.error('媒体对象无效或缺少必要字段:', media);
    ElMessage.error('选择的媒体文件无效，请重试');
  }
  
  dialogVisible.value = false;
};

// logo图片内容
const logoContent = computed(() => {
  const contents = currentContents.value;
  if (!contents) return null;
  
  const logo = contents.find(content => 
    content.type === 'image' && content.positionNumber === 6
  );
  
  if (!logo) {
    console.log('CardVoucherSettings - 未找到logo图片内容');
    return null;
  }
  
  // 添加唯一contentId（格式：card-voucher-logo-位置编号）
  return {
    ...logo,
    contentId: `card-voucher-logo-${logo.positionNumber}` // 关键修改：确保为字符串
  };
});

// 处理logo图片选择
const handleImageSelect = () => {
  console.log('CardVoucherSettings - 选择logo图片');
  selectImageFile();
};

// 处理logo更新
const handleLogoUpdate = (updatedContent) => {
  console.log('CardVoucherSettings - logo图片更新:', updatedContent);
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;
  
  const logoIndex = contents.findIndex(content => 
    content.type === 'image' && content.positionNumber === 6
  );
  
  if (logoIndex !== -1) {
    contents[logoIndex] = updatedContent;
    emitContentsUpdate();
  }
};

// 处理logo点击事件更新
const handleLogoClickEventUpdate = (updatedContent) => {
  console.log('CardVoucherSettings - logo点击事件更新:', updatedContent);
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;
  const logoIndex = contents.findIndex(content => 
    content.type === 'image' && content.positionNumber === 6
  );
  if (logoIndex !== -1) {
    // 统一用 ActionJsonGenerator.fromClickEventSettings 组装 clickEvent
    const clickEvent = ActionJsonGenerator.fromClickEventSettings(updatedContent);
    contents[logoIndex] = {
      ...contents[logoIndex],
      ...updatedContent,
      clickEvent: clickEvent
    };
    console.log('CardVoucherSettings - 更新后的logo内容:', contents[logoIndex]);
    console.log('CardVoucherSettings - 转换后的clickEvent:', clickEvent);
    emitContentsUpdate();
    console.log('CardVoucherSettings - 已发送logo更新事件');
  }
};

// 处理参数插入
const handleParamInsert = (param) => {
  console.log('CardVoucherSettings - 插入参数:', param);
  emit('param-insert', param);
};

// 处理参数管理
const handleParamManage = () => {
  console.log('CardVoucherSettings - 管理参数');
  emit('param-manage');
};

// 按钮相关的计算属性和方法
const buttonContent = computed(() => {
  const contents = currentContents.value;
  if (!contents) return null;
  
  const button = contents.find(content => 
    content.type === 'button' && content.positionNumber === 7
  );
  
  if (!button) {
    console.log('CardVoucherSettings - 未找到按钮内容');
    return null;
  }
  
  // 添加唯一contentId（格式：card-voucher-button-位置编号）
  return {
    ...button,
    contentId: `card-voucher-button-${button.positionNumber}` // 关键修改：确保为字符串
  };
});

// 监听按钮文本变化
watch(buttonText, (newText) => {
  console.log('CardVoucherSettings - 按钮文本变化:', newText);
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;
  
  const buttonIndex = contents.findIndex(content => 
    content.type === 'button' && content.positionNumber === 7
  );
  
  if (buttonIndex !== -1) {
    // 更新按钮文本
    contents[buttonIndex].content = newText;
    // 同时更新editContent字段，确保prepareTemplateData能正确使用
    contents[buttonIndex].editContent = newText;
    
    // 发送更新事件
    emitContentsUpdate();
  }
});

// 初始化时同步数据
onMounted(() => {
  console.log('CardVoucherSettings - onMounted 被调用');
  console.log('CardVoucherSettings - 初始化内容:', currentContents.value);
  console.log('CardVoucherSettings - props.contents:', props.contents);
  console.log('CardVoucherSettings - templateContents.value:', templateContents.value);
  
  const contents = currentContents.value;
  if (contents && contents.length > 0) {
    syncFromContents(contents);
  } else {
    console.log('CardVoucherSettings - 没有找到内容数据，尝试延迟初始化');
    // 延迟一下再试
    setTimeout(() => {
      const delayedContents = currentContents.value;
      console.log('CardVoucherSettings - 延迟后的内容:', delayedContents);
      if (delayedContents && delayedContents.length > 0) {
        syncFromContents(delayedContents);
      }
    }, 100);
  }
  
  // 添加全局事件监听器，响应强制同步事件
  const handleForceSync = () => {
    console.log('CardVoucherSettings - 收到强制同步事件');
    // 立即发送最新数据
    emitContentsUpdate();
    
    // 确保全局状态也是最新的
    syncToGlobalState();
  };
  
  document.addEventListener('force-cardvoucher-sync', handleForceSync);
  
  // 组件卸载时移除监听器
  onBeforeUnmount(() => {
    document.removeEventListener('force-cardvoucher-sync', handleForceSync);
  });
  
  // 初始化按钮文本
  const button = contents.find(content => 
    content.type === 'button' && content.positionNumber === 7
  );
  
  if (button) {
    buttonText.value = button.content || '';
  }
});

// 处理金额和条件更新
const updateAmountAndCondition = () => {
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents) return;

  // 更新金额
  const amountContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 1
  );
  if (amountContent) {
    amountContent.content = discountAmount.value;
    amountContent.editContent = discountAmount.value;
  }

  // 更新使用条件
  const conditionContent = contents.find(content => 
    content.type === 'text' && content.positionNumber === 3
  );
  if (conditionContent) {
    conditionContent.content = `满${minAmount.value}可用`;
    conditionContent.editContent = `满${minAmount.value}可用`;
  }

  // 发送更新事件
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
};

// 处理按钮点击事件更新
const handleButtonClickEventUpdate = (updatedContent) => {
  console.log('CardVoucherSettings - 按钮点击事件更新:', updatedContent);
  const contents = currentContents.value;
  if (!contents) return;
  const buttonIndex = contents.findIndex(content => 
    content.type === 'button' && content.positionNumber === 7
  );
  if (buttonIndex !== -1) {
    // 统一用 ActionJsonGenerator.fromClickEventSettings 组装 clickEvent
    const clickEvent = ActionJsonGenerator.fromClickEventSettings(updatedContent);
    contents[buttonIndex] = {
      ...contents[buttonIndex],
      ...updatedContent,
      clickEvent: clickEvent
    };
    console.log('CardVoucherSettings - 更新后的按钮内容:', contents[buttonIndex]);
    console.log('CardVoucherSettings - 转换后的clickEvent:', clickEvent);
    emitContentsUpdate();
    syncToGlobalState();
    console.log('CardVoucherSettings - 已发送按钮更新事件');
  }
};

// 发送内容更新事件的通用函数
const emitContentsUpdate = () => {
  console.log('CardVoucherSettings - 发送内容更新事件');
  console.log('CardVoucherSettings - 当前的 currentContents:', currentContents.value);
  
  // 确保数据是最新的
  const latestContents = [...currentContents.value];
  
  // 验证数据完整性
  const hasValidData = latestContents.some(content => 
    content.type === 'text' && content.content && content.content !== '编辑文本，最'
  );
  
  console.log('CardVoucherSettings - 数据验证结果:', hasValidData);
  console.log('CardVoucherSettings - 即将发送的数据:', latestContents);
  
  // 发送更新事件
  emit('contents-change', latestContents);
  
  // 额外的确保机制：通过多种方式发送数据
  setTimeout(() => {
    console.log('CardVoucherSettings - 延迟发送确认数据');
    emit('contents-change', [...currentContents.value]);
  }, 50);
  
  // 通过全局事件发送数据
  const globalEvent = new CustomEvent('cardvoucher-forced-update', {
    detail: { contents: latestContents }
  });
  document.dispatchEvent(globalEvent);
};

// 添加全局数据同步函数
const syncToGlobalState = () => {
  console.log('CardVoucherSettings - 同步数据到全局状态');
  console.log('CardVoucherSettings - 当前内容:', JSON.parse(JSON.stringify(currentContents.value)));
  
  // 通过事件立即同步数据
  emit('update', currentContents.value);
  
  // 同时通过全局机制确保数据同步
  if (window.TEMPLATE_GLOBAL_STATE) {
    window.TEMPLATE_GLOBAL_STATE.selectedTemplateContents = [...currentContents.value];
    console.log('CardVoucherSettings - 已同步到全局状态');
  }
  
  // 设置全局标记，表明数据已更新
  window.CARDVOUCHER_DATA_UPDATED = true;
  window.CARDVOUCHER_LATEST_DATA = JSON.parse(JSON.stringify(currentContents.value));
  
  // 验证同步的数据内容
  const textContents = currentContents.value.filter(content => content.type === 'text');
  console.log('CardVoucherSettings - 同步的文本内容详情:', textContents.map(content => ({
    positionNumber: content.positionNumber,
    content: content.content,
    editContent: content.editContent,
    type: content.type
  })));
  
  console.log('CardVoucherSettings - 全局数据更新完成, window.CARDVOUCHER_LATEST_DATA:', JSON.parse(JSON.stringify(window.CARDVOUCHER_LATEST_DATA)));
};

// 修改所有更新函数，添加同步调用
const updateDiscountAmount = (value) => {
  const discountContent = findContentByType('text', 'discount_amount');
  if (discountContent) {
    discountContent.content = value;
    discountContent.editContent = value;
    console.log('CardVoucherSettings - 更新折扣金额:', value);
    syncToGlobalState(); // 立即同步
  }
};

const updateDiscountCondition = (value) => {
  const conditionContent = findContentByType('text', 'discount_condition');
  if (conditionContent) {
    conditionContent.content = value;
    conditionContent.editContent = value;
    console.log('CardVoucherSettings - 更新使用条件:', value);
    syncToGlobalState(); // 立即同步
  }
};

const updateValidityPeriod = (value) => {
  const validityContent = findContentByType('text', 'validity_period');
  if (validityContent) {
    validityContent.content = value;
    validityContent.editContent = value;
    console.log('CardVoucherSettings - 更新有效期:', value);
    syncToGlobalState(); // 立即同步
  }
};

const updateVoucherName = (value) => {
  const nameContent = findContentByType('text', 'voucher_name');
  if (nameContent) {
    nameContent.content = value;
    nameContent.editContent = value;
    console.log('CardVoucherSettings - 更新券名称:', value);
    syncToGlobalState(); // 立即同步
  }
};

// 处理货币符号显示/隐藏变更
const handleCurrencyDisplayChange = (value) => {
  console.log('CardVoucherSettings - 货币符号显示状态变更:', value);
  
  // 使用当前内容
  const contents = currentContents.value;
  if (!contents || !Array.isArray(contents)) {
    console.log('CardVoucherSettings - handleCurrencyDisplayChange: contents无效');
    return;
  }

  // 更新所有内容的 aimCurrencyDisplay 属性
  contents.forEach(content => {
    content.aimCurrencyDisplay = value ? 1 : 0;
  });
  
  // 发送更新事件
  console.log('CardVoucherSettings - 发送货币符号显示更新事件，contents:', contents);
  emitContentsUpdate();
  // 立即同步到全局状态
  syncToGlobalState();
};

// 暴露方法给父组件
defineExpose({
  voucherType,
  discountAmount,
  voucherName,
  validityType,
  buttonText,
  confirmSettings,
  confirmButtonSettings
});
</script>

<style scoped lang="scss">
.card-voucher-settings {
  padding: 0 16px 20px;
  .mt-20{
    margin-top: 20px;
  }
  .flex-wrap{
    flex-direction: column;
    align-items: flex-start !important;
  }
  .setting-cell-amount{
    margin: -6px 0 8px;
  }
  .setting-switch{
    margin-left: 10px;
  }
  .setting-item {
    margin-top: 20px;
    .setting-title {
      color: #303133;
      font-weight: normal;
      padding: 10px 16px;
      width: 100%;
      border-bottom: 1px solid #ebeef5;
      position: relative;
    }
    .voucher-type-options{
      padding: 10px 16px;
    }
    .setting-group{
      width: 100%;
    }
  }
  
  .setting-card {
    border: 1px solid #ebeef5;
    background-color: #fff;
    transition: .3s;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    border-radius: 4px;
  }

  .setting-cell{
    padding: 0px 16px 20px;
    display: flex;
    align-items: center;
  }
  .validity-options{
    margin-top: 10px;
    padding-bottom: 10px;
  }
  .setting-input{
    width: 90px;
    margin: 0 5px;
  }
  .button-setting{
    flex-wrap: wrap;
    margin-top: 10px;
    .tip{
      margin-bottom: 10px;
    }
  }
}

.amount-inputs {
  margin-top: 10px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-group .el-input {
  width: 120px;
}

.logo-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.logo-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin: 16px;
  .settings-image {
    width: 150px;
    height: 150px;
    overflow: hidden;
  }

}

.select-btn {
  width: 88px;
}


</style> 