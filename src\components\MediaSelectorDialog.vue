<template>
  <teleport to="body">
    <div 
      v-if="dialogVisible" 
      class="media-selector-overlay" 
      @click="handleOverlayClick"
    >
      <div 
        class="media-selector-container" 
        @click.stop
      >
        <div class="dialog-header">
          <div class="dialog-title">{{ mediaTypeTitle }}</div>
          <div class="dialog-close" @click.stop="handleCancel">
            <el-icon><Close /></el-icon>
          </div>
        </div>
        
        <div class="dialog-body">
          <div class="category-section">
            <MediaCategory 
              :dir-type="2"
              :filter-by-app-key="true"
              :filter-app-key="selectedAppKey"
              @category-change="handleCategoryChange"
              @update-app-key-dir-id="handleAppKeyDirIdUpdate"
            />
          </div>
          
          <div class="media-section">
            <div v-if="!selectedCategoryId || selectedDirIds.length === 0" class="no-category-selected">
              <el-empty description="请先选择左侧分类" />
            </div>
            <template v-else>
              <div class="search-box">
                <el-input
                  v-model="searchQuery"
                  placeholder="请输入素材名称"
                  clearable
                >
                  <template #prefix>
                    <el-icon><Search /></el-icon>
                  </template>
                </el-input>
                <el-button type="primary" @click.stop="handleSearch">搜索</el-button>
              </div>
              
              <el-scrollbar class="media-list">
                <el-row>
                  <el-col :span="6" v-for="media in mediaList" :key="media.mediaId">
                    <div 
                      class="media-item"
                      :class="{ 'is-selected': selectedMediaId === media.mediaId,'disabled': media.channelType === null }"
                      @click.stop="handleMediaSelect(media)"
                    >
                      <div 
                        class="channel-type" 
                        :class="getChannelTypeClass(media)"
                      >
                      {{ getChannelTypeName(media.channelType) }}
                      </div>
                      <div class="image-container">
                        <el-image 
                          v-if="props.mediaType === 'image'"
                          :src="getImageUrl(media)">
                          <template #error>
                            <div class="image-slot">
                              <el-icon class="image-error-icon"><PictureFilled /></el-icon>
                            </div>
                          </template>
                        </el-image>
                        <video 
                          v-else 
                          class="video-player" 
                          :src="getImageUrl(media)" 
                          controls
                          controlsList="nodownload"
                          preload="metadata"
                          @click.stop
                        ></video>
                        <div class="media-overlay">
                          <i></i>
                          <div class="select-text" >选择{{ props.mediaType === 'image' ? '图片' : '视频' }}</div>
                        </div>
                        <div class="media-info">{{ media.mediaDesc }}</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                
                <div v-if="mediaList.length === 0" class="no-data">
                  <el-empty description="暂无数据" />
                </div>
              </el-scrollbar>

              <div class="pagination-area" v-if="total > 0">
                <div class="pagination">
                  共 {{ total }} 条
                  <el-pagination
                    v-model:current-page="currentPage"
                    :page-size="pageSize"
                    layout="prev, pager, next"
                    :total="total"
                    @current-change="handlePageChange"
                  />
                  前往 <el-input v-model="goToPage" class="page-input" /> 页
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup>
import { ref, watch, nextTick, onBeforeUnmount, computed } from 'vue'
import MediaCategory from './MediaCategory.vue'
import api from '@/api/index'
import { getMediaUrl } from '@/utils/mediaUtils';
import mockGetMediaList from '@/api/getMediaList.json';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  mediaType: {
    type: String,
    required: true,
    validator: (value) => {
      // 添加 background 类型
      return ['image', 'video', 'audio', 'background'].includes(value);
    }
  },
  filterAppKey: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const dialogVisible = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(8)
const total = ref(0)
const goToPage = ref(1);
const mediaList = ref([])
const selectedMediaId = ref(null)
const selectedCategoryId = ref(null)
const selectedDirIds = ref([])
const numType = ref(1)
const selectedAppKey = ref('')
let isApiCalled = false;

// 初始化时，如果props中传入了filterAppKey，则设置为selectedAppKey
if (props.filterAppKey) {
  selectedAppKey.value = props.filterAppKey;
  console.log('初始化时设置当前选择的AppKey为:', selectedAppKey.value);
}

// 监听filterAppKey的变化
watch(() => props.filterAppKey, (newAppKey, oldAppKey) => {
  if (newAppKey && newAppKey !== oldAppKey) {
    selectedAppKey.value = newAppKey;
    console.log('props.filterAppKey变化，更新selectedAppKey为:', selectedAppKey.value);
    
    // 当appKey变化时，重置分类和媒体列表
    selectedMediaId.value = null;
    selectedCategoryId.value = null;
    selectedDirIds.value = [];
    mediaList.value = [];
    total.value = 0;
    currentPage.value = 1;
    
    // 如果对话框是打开的，重新获取分类数据
    if (dialogVisible.value) {
      nextTick(() => {
        // 由MediaCategory组件触发数据加载
        console.log('appKey变化，需要重新加载分类和媒体数据');
      });
    }
  }
}, { immediate: true });

// 根据mediaType显示对应的标题
const mediaTypeTitle = computed(() => {
  return props.mediaType === 'image' ? '素材图库' : '素材视频库'
})

// 获取channelType对应的CSS类
const getChannelTypeClass = (media) => {
  if (media.channelType === null) {
    return 'channel-error'; // 红色
  }
  if (media.channelState === '0') {
    return 'channel-error'; // 红色
  } else if ((media.url && media.url !== 'null' && media.url !== null) || 
             (media.channelState === '4' && media.tid && media.tid !== 'null' && media.tid !== null)) {
    return 'channel-success'; // 绿色
  } else {
    return ''; // 默认灰色
  }
};
// 渠道类型映射对象
const CHANNEL_TYPE_MAP = {
  'AIM_SH_UNICOM': '阅信+上海联通'
};
// 获取渠道类型友好名称的方法
const getChannelTypeName = (channelType) => {
  if (channelType === null) {
    return '阅信+上海联通';
  }
  return CHANNEL_TYPE_MAP[channelType] || channelType; // 无映射时返回原始值
};
// 获取完整图片URL
const getImageUrl = (media) => {
  if (!media || !media.path) return '';
  
  // 拼接完整图片路径: 域名/aim_files/appKey/path
  const appKey = media.appKey || selectedAppKey.value;
  const path = media.path;
  
  return getMediaUrl(`/aim_files/${appKey}/${path}`);
};

// 监听modelValue变化
watch(() => props.modelValue, (val) => {
  if (val) {
    // 在对话框打开时完全重置状态
    resetDialogState();
    
    // 设置对话框为可见
    dialogVisible.value = true;
    
    // 防止背景滚动
    document.body.style.overflow = 'hidden';
  } else {
    // 恢复背景滚动
    document.body.style.overflow = '';
  }
});

// 重置对话框状态
const resetDialogState = () => {
  console.log('重置媒体选择器状态');
  
  // 重置选中内容状态
  selectedMediaId.value = null;
  selectedCategoryId.value = null;
  selectedDirIds.value = [];
  
  // 重置媒体列表 - 确保初始为空
  mediaList.value = [];
  total.value = 0;
  searchQuery.value = '';
  currentPage.value = 1;
  
  // 重要：处理filterAppKey - 立即设置，而不是延迟设置
  selectedAppKey.value = props.filterAppKey || '';
  console.log('重置状态时设置selectedAppKey:', selectedAppKey.value);
  
  // 触发媒体类型选择变化
  if (props.mediaType === 'image' || props.mediaType === 'background') {
    numType.value = 1;
  } else if (props.mediaType === 'video') {
    numType.value = 2;
  }
  
  // 确保不会自动请求数据
  isApiCalled = false;
  
  console.log('媒体选择器状态已重置:', {
    selectedAppKey: selectedAppKey.value,
    mediaType: props.mediaType,
    numType: numType.value
  });
};

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  emit('update:modelValue', val)
  if (!val) {
    // 关闭弹框时重置状态
    searchQuery.value = ''
    selectedMediaId.value = null
  }
})

// 监听页码跳转输入框
watch(goToPage, (newVal) => {
  const pageNum = parseInt(newVal);
  if (pageNum && pageNum > 0 && pageNum <= Math.ceil(total.value / pageSize.value)) {
    currentPage.value = pageNum;
  }
});
// 点击遮罩层关闭
const handleOverlayClick = (event) => {
  // 阻止冒泡以防止触发其他事件
  event.stopPropagation();
  
  // 只有点击遮罩层本身时才关闭弹框
  if (event.target === event.currentTarget) {
    handleCancel();
  }
}

// 获取素材列表
const fetchMediaList = async () => {
  if (isApiCalled) return;
  if (!selectedCategoryId.value || selectedDirIds.value.length === 0) {
    console.log('未选择分类，不获取媒体列表');
    mediaList.value = [];
    total.value = 0;
    return;
  }
  
  console.log('开始获取媒体列表，选中的分类:', selectedDirIds.value);
  isApiCalled = true;
  try {
    let params = {
      appKey: selectedAppKey.value, // 使用从分类中获取的appKey
      dirIds: selectedDirIds.value.join(','),
      dirType: 2,
      numType: numType.value,
      page: currentPage.value,
      limit: pageSize.value,
      mediaType: props.mediaType // 使用props传入的mediaType
    };
    
    if (searchQuery.value) {
      params.mediaName = searchQuery.value;
    }
    
    console.log('获取素材列表参数:', params);
    // const res = mockGetMediaList
    const res = await api.getMediaList(params);
    console.log('获取素材列表结果:', res);
    
    if (res.code == 0) {
      mediaList.value = res.data.list;
      total.value = res.data.total;
    } else {
      console.error('获取素材列表接口返回错误:', res.code, res.msg);
      mediaList.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error('获取素材列表失败:', error);
    mediaList.value = [];
    total.value = 0;
  } finally {
    isApiCalled = false;
  }
};

// 处理分类变化
const handleCategoryChange = (categories) => {
  console.log('分类变化:', categories);
  
  // 清空之前的数据
  mediaList.value = [];
  total.value = 0;
  
  // 只有当有分类被选中时才获取媒体列表
  if (categories && categories.length > 0) {
    selectedCategoryId.value = categories[0].dirId;
    // 收集所有选中分类的ID
    selectedDirIds.value = categories.map(cat => cat.dirId);
    
    // 获取并保存appKey，但只在未设置appKey或明确需要更新时更新
    if (categories[0].appKey && (!selectedAppKey.value || !props.filterAppKey)) {
      selectedAppKey.value = categories[0].appKey;
      console.log('从分类中获取并更新appKey:', selectedAppKey.value);
    }
    
    // 重置页码并获取媒体列表
    currentPage.value = 1;
    fetchMediaList();
  } else {
    // 没有选择分类时，清空状态
    selectedCategoryId.value = null;
    selectedDirIds.value = [];
    mediaList.value = [];
    total.value = 0;
  }
}

// 处理appKey和dirId更新
const handleAppKeyDirIdUpdate = (appKey, dirId) => {
  console.log('从分类组件获取到appKey和dirId:', appKey, dirId);
  
  // 如果没有设置固定appKey，才接受从分类传来的appKey
  if (!props.filterAppKey && appKey) {
    selectedAppKey.value = appKey;
    console.log('从分类组件更新selectedAppKey:', selectedAppKey.value);
  } else if (props.filterAppKey) {
    console.log('已固定appKey:', props.filterAppKey, '忽略从分类组件传来的appKey:', appKey);
  }
  
  // 只有当dirId有值时，才更新dirId并请求数据
  if (dirId) {
    selectedCategoryId.value = dirId;
    selectedDirIds.value = typeof dirId === 'string' && dirId.includes(',') 
      ? dirId.split(',') 
      : [dirId];
    
    // 实时获取媒体列表
    if (selectedAppKey.value && selectedDirIds.value.length > 0) {
      fetchMediaList();
    }
  } else {
    console.log('没有接收到dirId，不更新dirId和请求数据');
  }
};

// 监听mediaType变化，更新素材列表
watch(() => props.mediaType, (newType) => {
  console.log('媒体类型变化:', newType);
  if (selectedCategoryId.value) {
    currentPage.value = 1;
    fetchMediaList();
  }
});

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchMediaList()
}

// 处理素材选择
const handleMediaSelect = (media) => {
  selectedMediaId.value = media.mediaId;
  
  // 确保appKey正确传递
  const selectedMedia = {
    ...media,
    // 优先使用媒体自身的appKey，其次是当前选中的appKey，最后是props传入的appKey
    appKey: media.appKey || selectedAppKey.value || props.filterAppKey
  };
  
  // 确保媒体对象包含必要字段
  if (!selectedMedia.mediaUrl) {
    selectedMedia.mediaUrl = getImageUrl(selectedMedia);
  }
  
  // 记录为了调试
  console.log('选择了媒体文件:', selectedMedia);
  console.log('使用的appKey优先级: 媒体自身的appKey=', media.appKey, '当前选中的appKey=', selectedAppKey.value, 'props传入的appKey=', props.filterAppKey);
  console.log('最终使用的appKey:', selectedMedia.appKey);
  
  emit('confirm', selectedMedia);
  closeDialog();
}

const handlePageChange = (page) => {
  currentPage.value = page;
  fetchMediaList();
};
// 处理分页大小变化
const handleSizeChange = (val) => {
  pageSize.value = val
  fetchMediaList()
}

// 处理取消
const handleCancel = () => {
  console.log('关闭弹框');
  dialogVisible.value = false;
  searchQuery.value = '';
  selectedMediaId.value = null;
}

// 实现关闭弹窗方法
const closeDialog = () => {
  dialogVisible.value = false;
};

// 组件卸载时清理
onBeforeUnmount(() => {
  document.body.style.overflow = ''; // 恢复背景滚动
})
</script>

<style scoped lang="scss">
.media-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.media-selector-container {
  width: 70%;
  height: 80vh;
  background-color: #fff;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #dcdfe6;
}

.dialog-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.dialog-close {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
}

.dialog-body {
  flex: 1;
  display: flex;
  padding: 20px;
  overflow: hidden;
}

.category-section {
  width: 220px;
  border-right: 1px solid #dcdfe6;
  padding-right: 16px;
  overflow-y: auto;
}

.media-section {
  flex: 1;
  padding-left: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-box {
  margin-bottom: 16px;
  display: flex;
  gap: 10px;
	:deep(.el-input) {
		width: 260px;
	}
	:deep(.el-input__inner) {
		font-size: 13px;
		
	}
}

.media-list {
  flex: 1;
	:deep(.el-scrollbar__bar.is-horizontal){
		height: 0 !important;
	}
	:deep(.el-row){
		margin-right: -2px !important;
	}
}

.media-item {
  position: relative;
  margin: 0 20px 20px 0;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
	&:hover{
		.media-overlay{
			opacity: 1;
		}
	}
  &.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;  // 禁止鼠标交互
  }
}

.channel-type {
  color: #666; /* 默认灰色 */
  padding: 2px 6px;
  font-size: 14px;
  z-index: 2;
  height: 28px;
  
  &.channel-success {
    color: green; /* 绿色 */
  }
  
  &.channel-error {
    color: red; /* 红色 */
  }
}

.image-container {
  width: 100%;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  position: relative;
	border-radius: 4px;
}

.media-item .el-image {
  width: 100%;
  height: 150px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.video-player {
  width: 100%;
  height: 150px;
  object-fit: contain;
  background-color: #000;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 1;
	background: rgba(0, 0, 0, 0.5);
}

.select-text {
	width: 100px;
	text-align: center;
	line-height: 24px;
	border-radius: 12px;
	cursor: pointer;
	color: #fff;
	border: 1px solid #fff;
	margin: 5px 0;
}

.delete-icon {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #fff;
	background: red;
	border-radius: 50%;
  font-size: 16px;
	width: 28px;
	height: 28px;
	text-align: center;
	line-height: 32px;
}
.image-error-icon{
	font-size: 30px;
	color: #909399;
}
.media-info {
  padding: 6px 8px;
  flex: 1;
  color: #666;
	font-size: 14px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.pagination-area {
	margin: 16px 20px 0 0;
	display: flex;
	justify-content: right;
	font-size: 13px;
	color: #666;
	.pagination {
		display: flex;
		align-items: center;
		gap: 8px;
		
		.page-input {
			width: 40px;
		}
		:deep(.el-input__inner) {
			text-align: center;
		}
	}
}

.no-category-selected, .no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
}

.no-category-selected {
  height: 400px;
  width: 100%;
}
</style> 