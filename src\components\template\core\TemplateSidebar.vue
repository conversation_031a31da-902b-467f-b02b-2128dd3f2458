<template>
  <div class="left-section">
    <div class="tab-container">
      <el-radio-group v-model="activeTab" size="large">
        <el-radio-button 
          v-for="type in uniqueChannelTypes" 
          :key="type" 
          :value="type">
          {{ type }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <el-scrollbar class="template-container">
      <template v-for="(templates, scene) in groupedTemplatesByScene" :key="scene">
        <div class="scene-section">
          <div class="scene-title">{{ scene }}</div>
          <div class="template-list">
            <div v-for="template in templates" 
              :key="template.templateId" 
              class="template-card"
              :class="{ 'selected': isTemplateSelected(template) }"
              @click="handleTemplateClick(template)">
              <el-card shadow="hover" :body-style="{ padding: '0px' }">
                <div class="template-content">
                  <img :src="getContentSrc(template.tplThumbSrc)" class="preview-image">
                  <div class="template-name">{{ template.templateName }}</div>
                </div>
              </el-card>
            </div>
          </div>
        </div>
      </template>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getMediaUrl } from '@/utils/mediaUtils';

const props = defineProps({
  templates: {
    type: Array,
    default: () => []
  },
  selectedTemplate: {
    type: Object,
    default: null
  },
  activeTab: {
    type: String,
    default: '图文'
  },
  appKey: {
    type: String,
    default: ''
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['template-click', 'switch-template', 'tab-change']);

// 使用计算属性替代ref，以支持v-model模式
const activeTab = computed({
  get: () => props.activeTab,
  set: (value) => emit('tab-change', value)
});

// 获取媒体URL
const getContentSrc = (src) => {
  if (!src) return '';
  return getMediaUrl(src);
};

// 计算属性：唯一的频道类型
const uniqueChannelTypes = computed(() => {
  const types = new Set();
  props.templates.forEach(template => {
    if (template.tplType) {
      types.add(template.tplType);
    } else {
      types.add('图文'); // 默认分类
    }
  });
  return Array.from(types);
});

// 计算属性：按场景分组的模板
const groupedTemplatesByScene = computed(() => {
  const filteredTemplates = props.templates.filter(template => {
    return template.tplType === activeTab.value || 
           (!template.tplType && activeTab.value === '图文');
  });
  
  // 按场景分组
  const groups = {};
  filteredTemplates.forEach(template => {
    const scene = template.scene || '其他';
    if (!groups[scene]) {
      groups[scene] = [];
    }
    groups[scene].push(template);
  });
  
  return groups;
});

// 处理模板点击
const handleTemplateClick = (template) => {
  // 统一触发template-click事件，让父组件决定如何处理
  emit('template-click', template);
};

// 监听activeTab变化，当切换到没有模板的Tab时提示用户
watch(activeTab, (newTab) => {
  const hasTemplates = Object.keys(groupedTemplatesByScene.value).length > 0;
  if (!hasTemplates) {
    ElMessage.info(`暂无${newTab}类型的模板`);
  }
});

// 辅助函数：判断模板是否被选中
const isTemplateSelected = (template) => {
  if (!props.selectedTemplate) return false;
  
  // 比较模板ID，如果都有templateId则比较templateId
  if (template.templateId && props.selectedTemplate.templateId) {
    return template.templateId === props.selectedTemplate.templateId;
  }
  
  // 如果没有templateId，则比较模板名称和类型
  return template.templateName === props.selectedTemplate.templateName && 
         template.tplType === props.selectedTemplate.tplType;
};
</script>

<style scoped lang="scss">
.left-section {
  width: 256px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.tab-container {
  padding: 8px;
  :deep(.el-radio-group){
    width: 100%;
  }
  :deep(.el-radio-button__inner){
    width: 100%;
  }
  :deep(.el-radio-button){
    flex: 1;
  }
}

.template-container {
  flex: 1;
  overflow-y: auto;
  padding: 6px 15px 6px 6px;
  background: #fff;
}

.scene-section {
  margin-bottom: 20px;
  
  .scene-title {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    margin-bottom: 12px;
    position: relative;
    padding-left: 10px;
    border-left: 4px solid #409EFF;
  }
  
  .template-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
}

.template-card {
  cursor: pointer;
  background: #f8f8fa;
  
  &.selected {
    :deep(.el-card) {
      border-color: #409EFF !important;
      border-width: 1px !important;
      box-shadow: 0 1px 6px rgba(64, 158, 255, 0.3) !important;
    }
  }
  
  .template-content {
    position: relative;
    
    .preview-image {
      width: 100%;
      height: 120px;
      // object-fit: cover;
      padding: 6px;
      display: block;
    }
    
    .template-name {
      padding: 4px 0;
      font-size: 14px;
      text-align: center;
      color: #595961;
    }
  }
  
  :deep(.el-card) {
    border: 1px solid #f2f2f3;
    background: none;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #409EFF;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }
  }
}
</style> 