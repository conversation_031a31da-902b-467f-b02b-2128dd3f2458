<template>
  <div class="multi-text-template" @click="handleTemplateClick">
    <!-- 顶部大图片和文本区域 -->
    <div class="header-section" v-if="headerContent.image || headerContent.text">
      <!-- 头部大图片 -->
      <div class="header-image-container" v-if="headerContent.image">
        <ImageElement
          :content="headerContent.image"
          :is-selected="selectedContent?.contentId === headerContent.image.contentId"
          :editable="editable"
          :usage="usage"
          :get-media-url="getMediaUrl"
          @select="handleContentSelect"
          @update:content="handleContentUpdate"
        />
        <!-- 图片底部文本 -->
        <div class="header-text-overlay" v-if="headerContent.text">
          <TextElement
            :content="headerContent.text"
            :is-selected="selectedContent?.contentId === headerContent.text.contentId"
            :editable="editable"
            :usage="usage"
            @select="handleContentSelect"
            @update:content="handleContentUpdate"
          />
        </div>
      </div>
    </div>

    <!-- 图文对列表 -->
    <div 
      v-if="visibleTextImagePairs && visibleTextImagePairs.length > 0" 
      class="text-image-pairs"
    >
      <div 
        v-for="(pair, index) in visibleTextImagePairs" 
        :key="`text-image-pair-${index}`"
        class="text-image-pair"
        :class="{ 'selected': isSelectedPair(pair) }"
      >
        <!-- 左侧小图片 -->
        <div class="image-section" v-if="pair.image">
          <ImageElement
            :content="pair.image"
            :is-selected="selectedContent?.contentId === pair.image.contentId"
            :editable="editable"
            :usage="usage"
            :get-media-url="getMediaUrl"
            @select="handleContentSelect"
            @update:content="handleContentUpdate"
          />
        </div>
        
        <!-- 右侧文本 -->
        <div class="text-section" v-if="pair.text">
          <TextElement
            :content="pair.text"
            :is-selected="selectedContent?.contentId === pair.text.contentId"
            :editable="editable"
            :usage="usage"
            @select="handleContentSelect"
            @update:content="handleContentUpdate"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, inject, watch, onMounted, nextTick } from 'vue';
import ImageElement from '../elements/ImageElement.vue';
import TextElement from '../elements/TextElement.vue';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  selectedContent: {
    type: Object,
    default: null
  },
  editable: {
    type: Boolean,
    default: false
  },
  usage: {
    type: String,
    default: 'editor'
  },
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 注入设置数据，如果没有则使用默认值
const multiTextSettings = inject('multiTextSettings', () => ({
  pairCount: 1 // 默认1对图文
}));

// 注入当前模板信息
const currentTemplate = inject('currentTemplate', null);

// 计算属性：头部内容
const headerContent = computed(() => {
  
  // 参考通知类模板的做法，使用原始API数据结构
  // 头部图片：type === 'image' && positionNumber === 1
  const headerImage = props.contents?.find(item => 
    item.type === 'image' && 
    item.positionNumber === 1
  ) || null;
  
  // 头部文本：type === 'text' && isTextTitle === 1 && positionNumber === 2
  const headerText = props.contents?.find(item => 
    item.type === 'text' && 
    item.isTextTitle === 1 && 
    item.positionNumber === 2
  ) || null;
  
  
  return { image: headerImage, text: headerText };
});

// 计算属性：图文对内容
const textImagePairContents = computed(() => {
  
  // 参考通知类模板的做法，使用原始API数据结构
  // 图文对图片：type === 'image' && positionNumber >= 3
  const images = props.contents?.filter(item => 
    item.type === 'image' && 
    item.positionNumber >= 3
  ) || [];
  
  // 图文对文本：type === 'text' && positionNumber >= 4 && isTextTitle !== 1
  const texts = props.contents?.filter(item => 
    item.type === 'text' && 
    item.positionNumber >= 4 && 
    item.isTextTitle !== 1
  ) || [];
  
  
  return { images, texts };
});

// 计算属性：可见的图文对
const visibleTextImagePairs = computed(() => {
  
  const { images, texts } = textImagePairContents.value;
  const pairCount = multiTextSettings?.value?.pairCount || multiTextSettings?.pairCount || 1;
  
  const pairs = [];
  
  // 图片位置：3, 5, 7... (奇数位置从3开始)
  // 文本位置：4, 6, 8... (偶数位置从4开始)
  for (let i = 1; i <= pairCount; i++) {
    const imagePosition = (i - 1) * 2 + 3; // 第1对：位置3，第2对：位置5，第3对：位置7
    const textPosition = (i - 1) * 2 + 4;  // 第1对：位置4，第2对：位置6，第3对：位置8
    
    const image = images.find(img => img.positionNumber === imagePosition) || null;
    const text = texts.find(txt => txt.positionNumber === textPosition) || null;
    
    // 只添加至少有一个内容的对
    if (image || text) {
      pairs.push({
        index: i,
        image: image,
        text: text
      });
    }
  }
  
  console.log('MultiTextTemplateRenderer - 生成的图文对:', pairs);
  return pairs;
});

// 判断是否为选中的图文对
const isSelectedPair = (pair) => {
  if (!props.selectedContent) return false;
  
  return (pair.image && pair.image.contentId === props.selectedContent.contentId) ||
         (pair.text && pair.text.contentId === props.selectedContent.contentId);
};

// 处理内容选择
const handleContentSelect = (content) => {
  emit('select-content', content);
};

// 处理内容更新
const handleContentUpdate = (content) => {
  emit('update:content', content);
};

// 处理模板点击
const handleTemplateClick = (event) => {
  // 如果点击的是空白区域（没有选中任何内容元素）
  if (event.target === event.currentTarget || 
      event.target.classList.contains('multi-text-template') ||
      event.target.classList.contains('text-image-pairs')) {
    emit('select-content', null);
  }
};

// 监听设置变化
watch(() => multiTextSettings?.value?.pairCount || multiTextSettings?.pairCount, (newCount) => {
  console.log('MultiTextTemplateRenderer - 图文对数量变化:', newCount);
}, { immediate: true });

// 组件挂载后的初始化
onMounted(() => {
  console.log('MultiTextTemplateRenderer - 初始内容:', props.contents);
});
</script>

<style lang="scss" scoped>
.multi-text-template {
  width: 100%;
  min-height: 200px;
  background: #ffffff;
  // border-radius: 8px;
  // border: 1px solid #e5e7eb;
  cursor: pointer;
  overflow: hidden;
  
  .header-section {
    position: relative;
    width: 100%;
    
    .header-image-container {
      position: relative;
      width: 100%;
      background: #f3f4f6;
      border-radius: 8px 8px 0 0;

      .header-text-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.1);
        
        :deep(.preview-title) {
          font-size: 16px;
          font-weight: 500;
          color: #fff;
          height: 54px;
          margin: 0 1px;
          padding: 3px 14px;
          text-indent: 94px;
          position: relative;
          
          &:before {
            content: "【国泰海通】";
            color: #faad14;
            font-weight: normal;
            position: absolute;
            top: 3px;
            left: 14px;
            text-indent: 0;
            padding-right: 5px;
          }
        }
      }
    }
    
    .header-text-only {
      padding: 16px;
      background: #f9fafb;
      border-radius: 8px 8px 0 0;
    }
  }
  
  .text-image-pairs {
    display: flex;
    flex-direction: column;
  }
  
  .text-image-pair {
    display: flex;
    align-items: center;
    padding: 8px 14px 14px;

    .image-section {
      flex-shrink: 0;
      width: 52px;
      height: 52px;
      overflow: hidden;
      background: #f3f4f6;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      
      :deep(.preview-image) {
        width: 100%;
        height: 100%;
        // object-fit: cover;
        margin: 0;
        border-radius: 4px;
      }
      
      :deep(.image-container) {
        width: 100%;
        height: 100%;
        
        img {
          width: 100%;
          height: 100%;
          // object-fit: cover;
        }
      }
    }
    
    .text-section {
      flex: 1;
      min-height: 40px;
      margin-left: 12px;
      
      :deep(.preview-desc) {
        height: 52px;
        font-size: 14px;
        text-indent: 0 !important; /* 移除【国泰海通】缩进 */
        margin: 0;
        
        &:before {
          display: none !important; /* 隐藏【国泰海通】 */
        }
      }
    }
  }
}
</style> 