﻿ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '456'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: com.hbm.redpacket_image_5 {actionType: 'OPEN_BROWSER', actionUrl: '456', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 5, 找到的索引: 1, 当前所有内容: Array(7)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 5, actionType: 'OPEN_BROWSER'}
ClickEventEditor.vue:206 URL输入变化，立即发送更新: 4567
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '456'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 5, 找到的索引: 1, 当前所有内容: Array(7)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
RedPacketTemplateRenderer.vue:290 RedPacketTemplateRenderer - shouldAddFocusClass: false for content: 8
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 5, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2179 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: true, isCouponProductTemplate: false}
ClickEventSettings.vue:2179 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: true, isCouponProductTemplate: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '4567'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '4567'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '4567'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: com.hbm.redpacket_image_5 {actionType: 'OPEN_BROWSER', actionUrl: '4567', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 5, 找到的索引: 1, 当前所有内容: Array(7)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 5, actionType: 'OPEN_BROWSER'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
RedPacketTemplateRenderer.vue:290 RedPacketTemplateRenderer - shouldAddFocusClass: false for content: 8
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 5, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '4567'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '4567'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
RedPacketTemplateRenderer.vue:290 RedPacketTemplateRenderer - shouldAddFocusClass: false for content: 8
ClickEventSettings.vue:432 清除模板com.hbm.redpacket的点击事件缓存
ClickEventSettings.vue:1172 缓存更新成功: com.hbm.redpacket_image_5 {actionType: 'OPEN_BROWSER', actionUrl: '4567', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 5, pageId: 2, templateId: 2, type: 'image', content: '该图片位建议选择比例为1：1（像素最小320:320）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 5, 找到的索引: 1, 当前所有内容: Array(7)}
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object), 4: Proxy(Object), 5: Proxy(Object), 6: Proxy(Object)}
ClickEventSettings.vue:2285 设置同步到内容完成: {contentId: 5, contentType: 'image'}
ClickEventSettings.vue:1971 组件切换或同模板内操作，保留字段数据
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.redpacket', templateName: '红包', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: redpacket
RedPacketTemplateRenderer.vue:290 RedPacketTemplateRenderer - shouldAddFocusClass: false for content: 8
TemplateEditor.vue:5780 点击了真正的左侧边栏，不处理，元素: <div data-v-11a26846 data-v-26241470 class=​"left-section">​…​</div>​flex
TemplateEditor.vue:1521 当前切换的模板cardId: com.hbm.imageandtext
TemplateEditor.vue:1522 所有基础模板数据: Proxy(Array) {0: {…}, 1: {…}, 2: {…}, 3: {…}, 4: {…}, 5: {…}, 6: {…}, 7: {…}, 8: {…}, 9: {…}, 10: {…}, 11: {…}, 12: {…}, 13: {…}, 14: {…}, 15: {…}, 16: {…}}
TemplateEditor.vue:1531 confirmSwitchTemplate: 准备发送 update-sub-type 事件，从基础模板获取的subType: 1
index.vue:164 handleSubTypeUpdate 被调用，接收到新的 subType: 1
index.vue:165 更新前的 subType 值: 1
TemplateFactory.js:67 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateEditor.vue:1548 检测到的模板类型: imageandtext
TemplateEditor.vue:5010 应用模板变更: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateEditor.vue:5022 重置多图文设置为默认值: 1组图文对
TemplateEditor.vue:5028 TemplateEditor - 重置所有模板设置状态，避免数据污染
TemplateEditor.vue:5038 TemplateEditor - 重置全局校验失败标志
TemplateEditor.vue:5045 是否为真正的模板切换: false
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateEditor.vue:5052 模板类型切换检查: {当前模板类型: 'imageandtext', 新模板类型: 'imageandtext', 是否为模板类型切换: false}
TemplateEditor.vue:5073 横滑模板内部操作，不清空点击事件缓存
TemplateFactory.js:67 传入的template: {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateEditor.vue:5090 检测到的模板类型2: imageandtext
TemplateEditor.vue:5091 模板cardId: com.hbm.imageandtext
TemplateEditor.vue:5219 模板内容设置完成，编辑模式: false
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplatePreviewCore.vue:96 === rendererComponent 调试信息 ===
TemplatePreviewCore.vue:97 props.templateType: imageandtext
TemplatePreviewCore.vue:135 选择的渲染器组件: {__name: 'StandardTemplateRenderer', props: {…}, emits: Array(2), __hmrId: '6fb42a47', setup: ƒ, …}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
ImageElement.vue:106 ImageElement - 轮播图模板判断: {模板数据: Proxy(Object), cardId: 'com.hbm.imageandtext', templateName: '图文', 判断结果: false}
ImageElement.vue:193 ImageElement - 不是轮播图模板，显示普通图片
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 2, 找到的索引: 1, 当前所有内容: Array(4)}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 3, 找到的索引: 2, 当前所有内容: Array(4)}
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateEditor.vue:5244 正在初始化内容参数显示...
TemplateEditor.vue:5277 正在处理标题内容: 编辑文本，最多显示17个字
TemplateEditor.vue:5277 正在处理描述内容: 编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。
TemplateEditor.vue:5305 内容参数显示初始化完成
TextElement.vue:953 TextElement: 编辑模式，进行参数格式化 编辑文本，最多显示17个字
TextElement.vue:953 TextElement: 编辑模式，进行参数格式化 编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。编辑文本描述，最多显示69个字。
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: {…}, 2: {…}, 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ImageSettings.vue:115 ImageSettings组件收到的appKey: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
MediaSelectorDialog.vue:162 初始化时设置当前选择的AppKey为: A173440270073611
MediaSelectorDialog.vue:169 props.filterAppKey变化，更新selectedAppKey为: A173440270073611
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: null, result: false}
ClickEventSettings.vue:2369 packageName changed: 
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: undefined, cacheEmailAddress: undefined, finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: undefined, cachePopupTitle: undefined, initialPopupTitle: '', propsPopupContent: undefined, cachePopupContent: undefined, …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: default_image_1 {actionType: 'OPEN_BROWSER', actionUrl: '1123', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'OPEN_BROWSER'}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5797 点击了预览容器内部，检查是否为可编辑内容，容器: <div data-v-b605d584 data-v-d31bea8b class=​"template-preview-core">​…​</div>​
TemplateEditor.vue:5807 点击了预览容器内的非可编辑区域，交由预览容器处理
ClickEventSettings.vue:1822 没有用户输入，执行初始化字段
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: default_image_1 {actionType: 'OPEN_BROWSER', actionUrl: '1123', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'OPEN_BROWSER'}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
TemplateEditor.vue:5926 点击了其他区域，不处理
TemplateEditor.vue:5927 === handleDialogContentClick 结束 - 未找到匹配元素 ===
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: default_image_1 {actionType: 'OPEN_BROWSER', actionUrl: '1123', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'OPEN_BROWSER'}
ClickEventEditor.vue:206 URL输入变化，立即发送更新: 11234
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '1123'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2179 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: true, isCouponProductTemplate: false}
ClickEventSettings.vue:2179 批量更新被跳过: {_ignoreUrlChange: false, _isUpdatingContent: true, _isInitializingFields: false, _contentChanging: true, isCouponProductTemplate: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '11234'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '11234'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '11234'}
ClickEventSettings.vue:2294 开始updateContentWithAction
ClickEventSettings.vue:1172 缓存更新成功: default_image_1 {actionType: 'OPEN_BROWSER', actionUrl: '11234', actionPath: '', packageName: Array(0), floorType: '0', …}
TemplateEditor.vue:1912 TemplateEditor - 接收到内容设置更新: {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateEditor.vue:1834 TemplateEditor - 查找内容索引: {查找的contentId: 1, 找到的索引: 0, 当前所有内容: Array(4)}
TemplateEditor.vue:1871 TemplateEditor - 也更新了selectedContent引用
TemplateEditor.vue:1952 TemplateEditor - 内容已更新，新的selectedTemplateContents: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
ClickEventSettings.vue:2356 内容更新完成: {contentType: 'image', contentId: 1, actionType: 'OPEN_BROWSER'}
TemplateFactory.js:67 传入的template: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateFactory.js:127 cardId匹配成功，返回类型: imageandtext
StandardTemplateRenderer.vue:107 StandardTemplateRenderer - 按positionNumber排序后的内容: (4) [Proxy(Object), Proxy(Object), Proxy(Object), Proxy(Object)]
TemplateSettings.vue:244 TemplateSettings - selectedTemplateContents 变化: Proxy(Array) {0: Proxy(Object), 1: Proxy(Object), 2: Proxy(Object), 3: Proxy(Object)}
TemplateSettings.vue:248 TemplateSettings - content 变化: Proxy(Object) {contentId: 1, pageId: 1, templateId: 1, type: 'image', content: '该图片位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内', …}
TemplateSettings.vue:306 TemplateSettings - 判断是否是轮播图内容: {模板: Proxy(Object), 内容: Proxy(Object)}
TemplateSettings.vue:318 TemplateSettings - 模板数据: Proxy(Object) {userId: null, appKey: null, cardId: 'com.hbm.imageandtext', templateName: '图文', scene: '通用类', …}
TemplateSettings.vue:322 TemplateSettings - 轮播图模板判断结果: false
TemplateSettings.vue:337 TemplateSettings - 不是轮播图内容，返回false
ClickEventSettings.vue:694 ClickEventSettings - isHorizontalSwipeTemplate 判断: {content: Proxy(Object), contentId: 1, type: 'image', actionType: 'OPEN_BROWSER', result: false}
ClickEventSettings.vue:2369 packageName changed: undefined
ClickEventSettings.vue:1488 初始化邮箱字段: {propsEmailAddress: '', cacheEmailAddress: '', finalEmailAddress: ''}
ClickEventSettings.vue:1504 非横滑模板初始化 - 弹窗字段计算: {propsPopupTitle: '', cachePopupTitle: '', initialPopupTitle: '', propsPopupContent: '', cachePopupContent: '', …}
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '11234'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:2107 手动更新触发: {actionType: 'OPEN_BROWSER', actionUrl: '11234'}
ClickEventSettings.vue:2138 其他更新进行中，跳过重复请求
ClickEventSettings.vue:1532 非横滑模板初始化 - 弹窗字段设置后: {popupTitle: '', popupContent: '', popupButtonText: ''}
ClickEventSettings.vue:2369 packageName changed: Proxy(Array) {}
