<!--
  通用参数输入组件
  支持普通输入框和可编辑区域两种模式
-->

<template>
  <div class="param-editable-input" :class="{ 'is-focused': isFocused }">
    <!-- 普通输入框模式 -->
    <template v-if="type === 'input'">
      <input
        ref="inputEl"
        :value="modelValue"
        :placeholder="placeholder"
        :maxlength="maxLength"
        class="param-input"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
    </template>
    
    <!-- 文本域模式 -->
    <template v-else-if="type === 'textarea'">
      <textarea
        ref="inputEl"
        :value="modelValue"
        :placeholder="placeholder"
        :maxlength="maxLength"
        class="param-textarea"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      ></textarea>
    </template>
    
    <!-- 可编辑区域模式 -->
    <template v-else>
      <div
        ref="inputEl"
        class="param-contenteditable"
        :contenteditable="true"
        @input="handleContentEditableInput"
        @focus="handleFocus"
        @blur="handleBlur"
        v-html="formattedContent"
      ></div>
    </template>
    
    <!-- 参数插入按钮 (可选) -->
    <div v-if="showInsertButton" class="param-insert-btn" @click="onInsertParamClick">
      <span>+</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useTemplateParam } from '@/composables/useTemplateParam';
import { useParamService } from '@/services/ParamService';

const props = defineProps({
  // v-model绑定值
  modelValue: {
    type: String,
    default: ''
  },
  // 输入框类型：input, textarea, contenteditable
  type: {
    type: String,
    default: 'input',
    validator: (value) => ['input', 'textarea', 'contenteditable'].includes(value)
  },
  // 面板ID - 用于关联参数
  panelId: {
    type: String,
    default: ''
  },
  // 占位文本
  placeholder: {
    type: String,
    default: '请输入'
  },
  // 最大长度
  maxLength: {
    type: Number,
    default: 500
  },
  // 是否显示参数插入按钮
  showInsertButton: {
    type: Boolean,
    default: false
  },
  // 自动聚焦
  autoFocus: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);

// 输入元素引用
const inputEl = ref(null);
const isFocused = ref(false);

// 获取参数服务实例
const paramService = useParamService();

// 使用参数管理组合函数
const { 
  insertParam, 
  focus, 
  setContent, 
  collectParams 
} = useTemplateParam({
  target: () => inputEl.value,
  panelId: () => props.panelId,
  autoFocus: props.autoFocus,
  onInserted: () => {
    emit('change', getValueFromElement());
  },
  onChange: (value) => {
    emit('update:modelValue', value);
    emit('change', value);
  }
});

// 计算属性：格式化后的内容
const formattedContent = computed(() => {
  if (props.type === 'contenteditable' && props.modelValue) {
    return paramService.formatContentWithParams(props.modelValue, props.panelId);
  }
  return props.modelValue;
});

// 从当前输入元素获取值
const getValueFromElement = () => {
  if (!inputEl.value) return '';
  
  if (props.type === 'input' || props.type === 'textarea') {
    return inputEl.value.value;
  } else if (props.type === 'contenteditable') {
    // 从可编辑元素中获取文本并保留参数
    return paramService.extractTextWithParams(inputEl.value.innerHTML);
  }
  
  return '';
};

// 普通输入框和文本域的输入处理
const handleInput = (e) => {
  emit('update:modelValue', e.target.value);
  emit('change', e.target.value);
};

// 可编辑区域的输入处理
const handleContentEditableInput = (e) => {
  // 将可编辑区域的HTML内容转换为纯文本+参数标记
  const value = paramService.extractTextWithParams(e.target.innerHTML);
  
  // 提取参数并关联到面板
  const paramIds = collectParams();
  paramIds.forEach(id => {
    if (id) {
      paramService.associateParamWithPanel(id, props.panelId);
    }
  });
  
  emit('update:modelValue', value);
  emit('change', value);
};

// 处理聚焦事件
const handleFocus = () => {
  isFocused.value = true;
  emit('focus');
};

// 处理失焦事件
const handleBlur = () => {
  isFocused.value = false;
  emit('blur');
};

// 当点击参数插入按钮
const onInsertParamClick = () => {
  // 获取新参数ID
  const paramId = paramService.getNextParamId(props.panelId);
  
  // 插入参数
  insertParam({ paramId, panelId: props.panelId });
};

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (props.type === 'contenteditable' && inputEl.value) {
    // 检查输入元素的内容是否与新值不同
    // 这样可以避免不必要的DOM操作
    const currentValue = paramService.extractTextWithParams(inputEl.value.innerHTML);
    if (currentValue !== newVal) {
      nextTick(() => {
        setContent(newVal);
      });
    }
  }
});

// 设置初始内容
onMounted(() => {
  if (props.type === 'contenteditable' && props.modelValue) {
    nextTick(() => {
      setContent(props.modelValue);
    });
  }
  
  // 自动聚焦
  if (props.autoFocus) {
    setTimeout(focus, 100);
  }
});

// 暴露给父组件的方法
defineExpose({
  focus,                // 设置焦点
  insertParam,          // 插入参数
  getValueFromElement,  // 获取当前值
  setContent            // 设置内容
});
</script>

<style scoped lang="scss">
.param-editable-input {
  position: relative;
  width: 100%;
  display: flex;
}

.param-input,
.param-textarea,
.param-contenteditable {
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 10px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.param-contenteditable {
  min-height: 32px;
  white-space: pre-wrap;
  word-break: break-word;
  overflow-y: auto;
  background-color: #fff;
}

.param-textarea {
  min-height: 80px;
  resize: vertical;
}

.is-focused .param-input,
.is-focused .param-textarea,
.is-focused .param-contenteditable {
  border-color: #409eff;
}

.param-insert-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  margin-left: 8px;
  background-color: #f0f2f5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.param-insert-btn:hover {
  background-color: #e6e8eb;
}

/* 参数按钮样式 */
:deep(.j-btn),
:deep(.param-input) {
  display: inline-block;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #d9ecff;
  border-radius: 3px;
  font-size: 12px;
  margin: 0 2px;
  cursor: default;
  user-select: none;
}
</style> 