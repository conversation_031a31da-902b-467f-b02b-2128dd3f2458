/**
 * 横滑模板类
 * 处理横滑模板的内容初始化和验证
 */

import BaseTemplate from './BaseTemplate';
import { ActionJsonGenerator, ClickEventTypeConverter } from '@/utils/clickEventManager';
import { ClickEventValidator } from '@/utils/clickEventManager';

// 生成唯一ID的辅助函数
function generateUniqueId() {
  return 'id-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

class HorizontalSwipeTemplate extends BaseTemplate {
  /**
   * 初始化横滑模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 初始化后的模板内容数组
   */
  static initializeContents(template) {
    
    try {
      let pages;
      
      // 处理不同的pages数据格式
      if (typeof template.pages === 'string') {
        // 如果pages是字符串，需要解析JSON
        // console.log('pages是字符串，尝试解析JSON:', template.pages);
        pages = JSON.parse(template.pages);
      } else if (Array.isArray(template.pages)) {
        // 如果pages已经是数组，直接使用
        pages = template.pages;
        // 新增兜底：如果是图文内容数组（没有contents字段），自动包成一页
        if (pages.length > 0 && !pages[0].contents) {
          pages = [{
            pageId: 4,
            contents: pages,
            clickEvent: { type: 'OPEN_BROWSER', url: '' }
          }];
        }
      } else {
        return this.createDefaultContents(template);
      }
      
      console.log('解析的pages数据:', pages);
      
      if (!Array.isArray(pages) || pages.length === 0) {
        return this.createDefaultContents(template);
      }
      
      // 横滑模板有多个页面，需要将所有页面的内容合并
      const allContents = [];
      pages.forEach((page, pageIndex) => {
        
        // 同时处理 contents 和 content 字段
        let pageContents = page.contents || page.content || [];
        
        if (Array.isArray(pageContents)) {
          pageContents.forEach((content, contentIndex) => {
            // 处理不同的内容结构
            let processedContent;
            
            if (content.contentId) {
              // 标准内容结构（来自数据库）
              processedContent = {
                ...content,
                contentId: content.contentId || generateUniqueId(),
                pageIndex: pageIndex,  // 强制使用数组索引作为pageIndex
                userId: template.userId
              };
            } else {
              // 新增卡片的内容结构（来自前端添加）
              let contentValue = '';
              let srcValue = null;
              
              if (content.type === 'image') {
                // 处理图片内容 - 修复图片路径读取逻辑
                if (content.src && content.src !== '/aim_files/aim_defult/defaultImg.jpg') {
                  // 如果有真实的图片路径，使用它
                  srcValue = content.src;
                  contentValue = content.content || content.alt || '图片';
                } else if (content.content && content.content.src && content.content.src !== '/aim_files/aim_defult/defaultImg.jpg') {
                  // 兼容性处理：如果src在content对象中
                  srcValue = content.content.src;
                  contentValue = content.content.alt || '图片';
                } else {
                  // 使用默认图片
                  srcValue = '/aim_files/aim_defult/defaultImg.jpg';
                  contentValue = '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内';
                }
              } else {
                // 处理文本和按钮内容
                contentValue = content.content || '';
              }
              
              processedContent = {
                contentId: generateUniqueId(),
                pageId: page.pageId || (pageIndex + 1),
                templateId: template.templateId || 4,
                type: content.type,
                content: contentValue,
                src: srcValue,
                isTextTitle: content.type === 'text' ? (contentIndex === 1 ? 1 : 0) : 0,
                positionNumber: contentIndex + 1,
                pageIndex: pageIndex,  // 强制使用数组索引作为pageIndex
                clickEvent: content.clickEvent,
                userId: template.userId
              };
            }
            
            // 修复：button类型内容的actionJson应由clickEvent生成，target取url
            if (processedContent.type === 'button') {
              // 优先从page.clickEvent获取
              const clickEvent = page.clickEvent || processedContent.clickEvent || {};
              const actionType = ClickEventTypeConverter.toActionType(clickEvent.type);
              const url = clickEvent.url || clickEvent.target || '';
              const actionJson = ActionJsonGenerator.generate(
                actionType,
                url,
                clickEvent.text || clickEvent.description || '',
                clickEvent
              );
              processedContent.actionType = actionType;
              processedContent.actionJson = actionJson;
            }
            allContents.push(processedContent);
          });
        }
      });
      
      console.log('横滑模板合并后的内容:', allContents);
      
      // 返回处理后的内容数组
      const result = allContents.map(content => ({
        ...content,
        userId: template.userId
      }));
      
      return result;
      
    } catch (error) {
      console.error('解析横滑模板数据出错:', error);
      return this.createDefaultContents(template);
    }
  }
  
  /**
   * 创建默认横滑模板内容
   * @param {Object} template 模板数据
   * @returns {Array} 默认内容数组
   */
  static createDefaultContents(template) {
    // 创建默认的两页内容（和接口返回的数据结构完全一致）
    const defaultPages = [
      // 第一页
      {
        pageId: 4,
        clickEvent: {
          type: 'OPEN_BROWSER',
          url: ''
        },
        contents: [
          {
            contentId: generateUniqueId(),
            pageId: 4,
            templateId: template.templateId || 4,
            type: 'image',
            content: '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
            srcType: null,
            src: '/aim_files/aim_defult/defaultImg.jpg',
            isTextTitle: 0,
            actionType: null,
            positionNumber: 1,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'center',
            pageLines: 1,
            auditState: null,
            actionJson: 'OPEN_BROWSER,OPEN_APP,OPEN_URL,OPEN_QUICK,DIAL_PHONE,COPY_PARAMETER, OPEN_SMS,OPEN_EMAIL,OPEN_SCHEDULE,OPEN_POPUP',
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 4,
            templateId: template.templateId || 4,
            type: 'text',
            content: '编辑标题，最多显示13个字',
            srcType: null,
            src: null,
            isTextTitle: 1,
            actionType: null,
            positionNumber: 2,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'left',
            pageLines: 2,
            auditState: null,
            actionJson: null,
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 4,
            templateId: template.templateId || 4,
            type: 'text',
            content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
            srcType: null,
            src: null,
            isTextTitle: 0,
            actionType: null,
            positionNumber: 3,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'left',
            pageLines: 3,
            auditState: null,
            actionJson: null,
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 4,
            templateId: template.templateId || 4,
            type: 'button',
            content: '编辑按钮',
            srcType: null,
            src: null,
            isTextTitle: 0,
            actionType: null,
            positionNumber: 4,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'center',
            pageLines: 4,
            auditState: null,
            actionJson: { target: '' },
            userId: template.userId
          }
        ]
      },
      // 第二页
      {
        pageId: 5,
        clickEvent: {
          type: 'OPEN_BROWSER',
          url: ''
        },
        contents: [
          {
            contentId: generateUniqueId(),
            pageId: 5,
            templateId: template.templateId || 4,
            type: 'image',
            content: '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
            srcType: null,
            src: '/aim_files/aim_defult/defaultImg.jpg',
            isTextTitle: 0,
            actionType: null,
            positionNumber: 1,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'center',
            pageLines: 1,
            auditState: null,
            actionJson: 'OPEN_BROWSER,OPEN_APP,OPEN_URL,OPEN_QUICK,DIAL_PHONE,COPY_PARAMETER, OPEN_SMS,OPEN_EMAIL,OPEN_SCHEDULE,OPEN_POPUP',
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 5,
            templateId: template.templateId || 4,
            type: 'text',
            content: '编辑标题，最多显示13个字',
            srcType: null,
            src: null,
            isTextTitle: 1,
            actionType: null,
            positionNumber: 2,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'left',
            pageLines: 2,
            auditState: null,
            actionJson: null,
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 5,
            templateId: template.templateId || 4,
            type: 'text',
            content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
            srcType: null,
            src: null,
            isTextTitle: 0,
            actionType: null,
            positionNumber: 3,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'left',
            pageLines: 3,
            auditState: null,
            actionJson: null,
            userId: template.userId
          },
          {
            contentId: generateUniqueId(),
            pageId: 5,
            templateId: template.templateId || 4,
            type: 'button',
            content: '编辑按钮',
            srcType: null,
            src: null,
            isTextTitle: 0,
            actionType: null,
            positionNumber: 4,
            visible: null,
            aimCover: null,
            aimCurrencyDisplay: null,
            aimOppoBackground: null,
            state: null,
            timeCreate: null,
            timeUpdate: null,
            auditContentId: null,
            pageLayout: 'center',
            pageLines: 4,
            auditState: null,
            actionJson: { target: '' },
            userId: template.userId
          }
        ]
      }
    ];
    
    console.log('创建的横滑容器结构:', defaultPages);
    console.log('=== createDefaultContents 结束 ===');
    
    return defaultPages;
  }
  
  /**
   * 校验内容是否符合横滑模板要求
   * @param {Array} contents 模板内容数组
   * @returns {boolean} 是否通过验证
   */
  static validateContents(contents) {
    if (!contents || !Array.isArray(contents)) {
      return false;
    }
    
    // 检查是否有横滑容器
    const hasSwipeContainer = contents.some(content => 
      content.type === 'swipeContainer' && 
      content.swipeItems && 
      Array.isArray(content.swipeItems) &&
      content.swipeItems.length >= 2
    );
    
    return hasSwipeContainer;
  }
  
  /**
   * 添加新的横滑卡片
   * @param {Object} swipeContainer 横滑容器对象
   * @returns {Object} 新添加的卡片
   */
  static addSwipeCard(swipeContainer) {
    if (!swipeContainer || !swipeContainer.cards) {
      return null;
    }
    
    // 生成新的pageId（从4开始递增）
    const newPageId = 4 + swipeContainer.cards.length;
    
    const newCard = {
      pageId: newPageId,
      contents: [
        {
          contentId: generateUniqueId(),
          pageId: newPageId,
          templateId: 4,
          type: 'image',
          content: '该图片位建议选择比例为1：1（像素最小1088:1088）的图片，大小建议500KB以内',
          srcType: null,
          src: '/aim_files/aim_defult/defaultImg.jpg',
          isTextTitle: 0,
          actionType: null,
          positionNumber: 1,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: 'center',
          pageLines: 1,
          auditState: null,
          actionJson: { target: '' }
        },
        {
          contentId: generateUniqueId(),
          pageId: newPageId,
          templateId: 4,
          type: 'text',
          content: '编辑标题，最多显示13个字',
          srcType: null,
          src: null,
          isTextTitle: 1,
          actionType: null,
          positionNumber: 2,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: 'left',
          pageLines: 2,
          auditState: null,
          actionJson: null
        },
        {
          contentId: generateUniqueId(),
          pageId: newPageId,
          templateId: 4,
          type: 'text',
          content: '编辑文本，最多显示30个字。编辑文本，最多显示30个字。',
          srcType: null,
          src: null,
          isTextTitle: 0,
          actionType: null,
          positionNumber: 3,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
            pageLayout: 'left',
            pageLines: 3,
            auditState: null,
            actionJson: null
        },
        {
          contentId: generateUniqueId(),
          pageId: newPageId,
          templateId: 4,
          type: 'button',
          content: '编辑按钮',
          srcType: null,
          src: null,
          isTextTitle: 0,
          actionType: null,
          positionNumber: 4,
          visible: null,
          aimCover: null,
          aimCurrencyDisplay: null,
          aimOppoBackground: null,
          state: null,
          timeCreate: null,
          timeUpdate: null,
          auditContentId: null,
          pageLayout: 'center',
          pageLines: 4,
          auditState: null,
          actionJson: { target: '' }
        }
      ],
      clickEvent: {
        type: 'OPEN_BROWSER',
        url: ''
      }
    };
    
    swipeContainer.cards.push(newCard);
    return newCard;
  }
  
  /**
   * 删除横滑卡片
   * @param {Object} swipeContainer 横滑容器对象
   * @param {number} index 要删除的索引
   * @returns {boolean} 是否删除成功
   */
  static removeSwipeCard(swipeContainer, index) {
    if (!swipeContainer || !swipeContainer.cards || 
        index < 0 || index >= swipeContainer.cards.length ||
        swipeContainer.cards.length <= 2) {
      return false;
    }
    
    swipeContainer.cards.splice(index, 1);
    return true;
  }

  /**
   * 获取卡片的点击事件对象，保证每个卡片独立
   * @param {Object} page 横滑卡片对象
   * @returns {Object} 点击事件对象
   */
  static getClickEvent(page) {
    if (!page.clickEvent) {
      page.clickEvent = {};
    }
    return page.clickEvent;
  }

  /**
   * 设置卡片的点击事件对象，保证每个卡片独立
   * @param {Object} page 横滑卡片对象
   * @param {Object} clickEvent 点击事件对象
   */
  static setClickEvent(page, clickEvent) {
    // 优先使用新格式字段（actionType），与电商模板保持一致
    const actionType = ClickEventTypeConverter.toActionType(clickEvent.actionType || clickEvent.type);
    const type = ClickEventTypeConverter.toClickEventType(actionType);
    page.clickEvent = {
      ...clickEvent,
      type,
      actionType
    };
  }
  /**
   * 生成 actionJson，所有横滑卡片都走本方法
   * @param {Object} page 横滑卡片对象
   * @returns {Object} actionJson
   */
  static buildActionJson(page) {
    const clickEvent = this.getClickEvent(page);
    return ActionJsonGenerator.fromClickEvent(clickEvent);
  }

  /**
   * 校验点击事件，所有横滑卡片都走本方法
   * @param {Object} page 横滑卡片对象
   * @returns {Object} {valid: boolean, message: string}
   */
  static validateClickEvent(page) {
    const clickEvent = this.getClickEvent(page);
    // 兼容 actionType/actionUrl/actionPath 字段
    const contentForValidation = {
      ...clickEvent,
      actionType: clickEvent.actionType || ClickEventTypeConverter.toActionType(clickEvent.type),
      actionUrl: clickEvent.url || clickEvent.actionUrl || '',
      actionPath: clickEvent.path || clickEvent.actionPath || '',
      packageName: clickEvent.packageName || '',
      floorType: clickEvent.floorType || '0',
      emailAddress: clickEvent.emailAddress || '',
      emailSubject: clickEvent.emailSubject || '',
      emailBody: clickEvent.emailBody || '',
      scheduleStartTimeString: clickEvent.scheduleStartTimeString || '',
      scheduleEndTimeString: clickEvent.scheduleEndTimeString || '',
      popupTitle: clickEvent.popupTitle || '',
      popupContent: clickEvent.popupContent || '',
      popupButtonText: clickEvent.popupButtonText || '',
      copyType: clickEvent.copyType || '1',
      selectedParamId: clickEvent.selectedParamId || '',
      fixedContent: clickEvent.fixedContent || ''
    };
    return ClickEventValidator.validate(contentForValidation);
  }
}

export default HorizontalSwipeTemplate; 