<template>
  <div 
    class="coupon-product-template" 
    :class="{ 'list-mode': usage === 'list' }"
    @click="handleCouponProductClick"
    :style="{ '--bg-image': `url(${getMediaUrl('/aim_files/aim_defult/EcommerceCouponBg.png')})` }"
  >
    <!-- 头部商品区域 -->
    <div v-if="displayData?.headerProduct" class="header-product-section">
      <div class="header-product-image">
        <img 
          :src="getImageSrc(displayData.headerProduct.image)"
          :alt="displayData.headerProduct.title || '商品图片'"
          class="header-image"
        />
      </div>
      <div class="header-product-info">
        <div class="header-product-title">{{ displayData.headerProduct.title }}</div>
        <div class="header-product-description">{{ displayData.headerProduct.description }}</div>
      </div>
    </div>

    <!-- 券信息区域 -->
    <div v-if="displayData?.coupon" class="coupon-section">
      <div class="coupon-amount-container">
        <div class="coupon-amount">
          <span class="coupon-amount-symbol" v-if="displayData.coupon.aimCurrencyDisplay === 1">￥</span>
          {{ displayData.coupon.amount }}
        </div>
        <div class="coupon-amount-container-right">
          <div class="coupon-title">{{ displayData.coupon.title }}</div>
          <div class="coupon-description">{{ displayData.coupon.description }}</div>
        </div>
      </div>
      <div class="coupon-button-container">
        <img 
          :src="getMediaUrl(displayData.coupon?.buttonImage || '/aim_files/aim_defult/neck.png')" 
          :class="{ 'animated': displayData.coupon?.buttonAnimation }"
          alt="券按钮图片" 
        />
      </div>
    </div>

    <!-- 商品列表区域 -->
    <div v-if="visibleProducts.length > 0" class="products-section">
      <div 
        v-for="(product, index) in visibleProducts" 
        :key="product.id || index"
        class="product-item"
      >
        <!-- 商品图片 -->
        <div class="product-image-section">
          <img 
            :src="getImageSrc(product.image)"
            :alt="product.title || `商品${index + 1}`"
            class="product-image"
          />
        </div>
        
        <!-- 商品信息 -->
        <div class="product-info-section">
          <!-- 商品标题 -->
          <div class="product-title">{{ product.title }}</div>
          
          <div class="">
            <!-- 商品标签 -->
            <div v-if="product.tag" class="product-tag">{{ product.tag }}</div>
            
            <!-- 商品价格 -->
            <div class="product-price-section flex-row-between">
              <span class="product-price">
                <template v-if="product.aimCurrencyDisplay === 1">￥</template>{{ product.price }}
              </span>
              
              <button class="product-button">
                {{ product.buttonText }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { getMediaUrl } from '@/utils/mediaUtils';
import CouponProductTemplate from '@/components/template/types/CouponProductTemplate.js';

const props = defineProps({
  contents: {
    type: Array,
    default: () => []
  },
  // 接收从父组件传递的显示数据
  couponProductDisplayData: {
    type: Object,
    default: null
  },
  // usage属性，用于控制组件行为
  usage: {
    type: String,
    default: 'editor' // 'editor' | 'list'
  },
  // getMediaUrl函数
  getMediaUrl: {
    type: Function,
    default: (src) => src
  }
});

const emit = defineEmits(['select-content', 'update:content']);

// 获取图片资源路径
const getImageSrc = (src) => {
  if (!src) return getMediaUrl('/aim_files/aim_defult/defaultImg2.jpg');
  return getMediaUrl(src);
};

// 处理券+商品模板点击事件
const handleCouponProductClick = (event) => {
  // 防止默认行为和事件冒泡
  event.preventDefault();
  event.stopPropagation();
  
  console.log('券+商品模板点击事件');
  
  emit('select-content', {
    contentId: 'coupon-product-settings',
    type: 'coupon-product-settings',
    isCouponProductSettings: true,
    currentData: displayData.value || {
      headerProduct: {},
      coupon: {},
      products: []
    }
  });
};

// 计算显示数据
const displayData = computed(() => {
  console.log('CouponProductTemplateRenderer - 计算显示数据');
  
  if (props.couponProductDisplayData) {
    console.log('CouponProductTemplateRenderer - 使用父组件传递的数据:', props.couponProductDisplayData);
    return props.couponProductDisplayData;
  }
  
  if (!props.contents || props.contents.length === 0) {
    console.log('CouponProductTemplateRenderer - 没有contents数据，返回默认数据');
    return {
      headerProduct: {},
      coupon: {},
      products: []
    };
  }
  
  const extractedData = CouponProductTemplate.handleCouponProductContent(props.contents);
  console.log('CouponProductTemplateRenderer - 从contents提取的数据:', extractedData);
  
  return extractedData;
});

// 计算可见商品列表
const visibleProducts = computed(() => {
  if (!displayData.value?.products || !Array.isArray(displayData.value.products)) {
    return [];
  }
  
  const filtered = displayData.value.products.filter(product => !product.hidden).slice(0, 3);
  console.log('CouponProductTemplateRenderer - 可见商品:', filtered.length, '总商品:', displayData.value.products.length);
  
  return filtered;
});

</script>

<style scoped lang="scss">
.coupon-product-template {
  background: var(--bg-image) no-repeat top center;
  background-size: 100%;
  padding: 0 12px;
  border-radius: 10px;
}

/* 头部商品区域 */
.header-product-section {
  display: flex;
  align-items: center;
  padding: 16px 4px;
}

.header-product-image {
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  margin-right: 10px;
}

.header-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.header-product-info {
  flex: 1;
}

.header-product-title {
  font-weight: 700;
  font-size: 18px;
  line-height: 30px;
  color: #131011;
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.header-product-description {
  font-size: 14px;
  color: #554a4c;
  width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  text-indent: 82px;
  &:after {
    content: "【国泰海通】";
    color: #faad14;
    font-weight: normal;
    position: absolute;
    top: 0;
    left: 0;
    text-indent: 0;
  }
}

/* 券信息区域 */
.coupon-section {
  height: 74px;
  display: flex;
  align-items: center;
  color: #fff;
  padding: 0 10px;
  box-sizing: border-box;
  border-radius: 12px;
  background: #f83154;
  background: linear-gradient(90deg, #f83154 0, #ff5976);
}

.coupon-amount-container {
  flex: 1;
  display: flex;
}

.coupon-amount {
  display: flex;
  align-items: center;
  font-size: 30px;
  font-weight: bold;
  line-height: 1;
  margin-right: 6px;
  flex: 0 0 65px;
  .coupon-amount-symbol{
    font-size: 15px;
    font-weight: normal;
  }
}
.coupon-amount-container-right{
  max-width: 170px;
}
.coupon-title {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.coupon-description {
  font-size: 12px;
  opacity: .7;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
}

.coupon-button-container {
  flex-shrink: 0;
  margin-left: 10px;

  img {
    display: block;
    width: 58px;

    &.animated {
      animation: mymove 1s linear 0s infinite;
      -webkit-animation: mymove 1s linear 0s infinite;
    }
  }
}

.coupon-button {
  background: white;
  color: #ff6b6b;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f8f9fa;
    transform: scale(1.02);
  }
}

@keyframes mymove {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@-webkit-keyframes mymove {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* 商品列表区域 */
.products-section {
  display: flex;
  flex-direction: column;
  padding-bottom: 20px;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 14px 0 0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.product-image-section {
  flex-shrink: 0;
  width: 106px;
  height: 106px;
  margin-right: 8px;
}

.product-image {
  width: 100%;
  height: 100%;
  // object-fit: cover;
  border-radius: 8px;
}

.product-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.product-title {
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  word-break: break-all;
  line-height: 18px;
  margin-bottom: 2px;
  height: 36px;
  font-size: 14px;
  color: #333;
}

.product-tag {
  border: 1px solid #fa2a2d;
  color: #fa2a2d;
  border-radius: 3px;
  font-size: 12px;
  padding: 0 3px;
  max-width: 84px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  display: inline-block;
}

.product-price-section {
  margin-top: 4px;
}

.flex-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.product-price {
  font-size: 18px;
  color: #fa2a2d;
  display: inline-block;
  max-width: 98px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.product-button {
  width: 80px;
  height: 30px;
  line-height: 30px;
  background-color: #fa2a2d;
  border-radius: 30px;
  text-align: center;
  color: #fff;
  font-size: 14px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  border: none;
  cursor: pointer;
}
</style> 