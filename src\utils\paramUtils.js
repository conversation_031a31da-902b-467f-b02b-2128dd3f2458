import { useParamService } from '@/services/ParamService';
/**
 * 参数工具类 - 统一处理项目中参数插入、格式化等功能
 */

/**
 * 获取参数文本的显示格式
 * @param {string|number} paramId 参数ID
 * @returns {string} 格式化后的参数文本
 */
export function getParamText(paramId) {
  return `{#param${paramId}#}`;
}

/**
 * 提取参数ID从参数文本
 * @param {string} paramText 参数文本，例如 {#param1#}
 * @returns {string|null} 参数ID或null
 */
export function extractParamId(paramText) {
  if (!paramText) return null;
  const match = paramText.match(/{#param(\d+)#}/);
  return match && match[1] ? match[1] : null;
}

/**
 * 获取指定面板的下一个可用参数ID
 * @param {string} panelId 面板ID
 * @returns {string} 下一个可用的参数ID
 */
export function getNextParamIdForGroup(panelId) {
  const paramService = useParamService();
  return paramService.getNextParamId(panelId);
}

/**
 * 获取所有使用中的参数ID
 * @returns {Set<string>} 使用中的参数ID集合
 */
export function getAllUsedParamIds() {
  const paramService = useParamService();
  return new Set(paramService.getAllParams());
}

/**
 * 收集DOM中所有参数元素
 * @param {HTMLElement} [root=document.body] 搜索的根元素
 * @returns {Array<HTMLElement>} 参数元素数组
 */
export function collectParamsFromDOM(root = document.body) {
  const paramService = useParamService();
  return paramService.collectParamsFromDOM(root);
}

/**
 * 将参数文本转换为参数按钮
 * @param {string} content 原始内容
 * @param {string} panelId 面板ID
 * @returns {string} 转换后的HTML内容
 */
export function formatContentWithParams(content, panelId) {
  const paramService = useParamService();
  return paramService.formatContentWithParams(content, panelId);
}

/**
 * 从HTML中提取文本内容和参数
 * @param {string} html HTML内容
 * @returns {string} 提取后的文本，保留参数
 */
export function extractTextWithParams(html) {
  const paramService = useParamService();
  return paramService.extractTextWithParams(html);
}

/**
 * 向指定元素插入参数
 * @param {HTMLElement} element 目标元素
 * @param {Object} options 参数选项
 * @returns {boolean} 是否成功插入
 */
export function insertParamToElement(element, options = {}) {
  const paramService = useParamService();
  return paramService.insertParam(element, options);
}

/**
 * 创建参数按钮元素
 * @param {string} paramText 参数文本
 * @param {string} panelId 面板ID
 * @returns {HTMLElement} 参数按钮元素
 */
export function createParamButton(paramText, panelId) {
  const paramService = useParamService();
  return paramService.createParamButton(paramText, panelId);
}

/**
 * 导入参数存储
 * @returns {Object|null} 参数存储或null
 */
export function importParamStore() {
  try {
    const paramService = useParamService();
    return paramService ? paramService.getStore() : null;
  } catch (error) {
    console.error('获取参数存储失败:', error);
    
    // 创建一个临时存储对象用于兼容
    return {
      usedParamIds: new Set(),
      deletedParamIds: new Set(),
      paramPanelMap: new Map(),
      panelParams: {},
      addUsedParamId(paramId) {
        if (paramId) this.usedParamIds.add(paramId.toString());
      },
      markParamAsDeleted(paramId) {
        if (paramId) {
          const paramIdStr = paramId.toString();
          this.deletedParamIds.add(paramIdStr);
          this.usedParamIds.delete(paramIdStr);
        }
      },
      associateParamWithPanel(paramId, panelId) {
        if (!paramId || !panelId) return;
        
        const paramIdStr = paramId.toString();
        this.paramPanelMap.set(paramIdStr, panelId);
        
        if (!this.panelParams[panelId]) {
          this.panelParams[panelId] = [];
        }
        
        if (!this.panelParams[panelId].includes(paramIdStr)) {
          this.panelParams[panelId].push(paramIdStr);
        }
      },
      get allUsedParamIds() {
        return Array.from(this.usedParamIds)
          .sort((a, b) => parseInt(a) - parseInt(b));
      }
    };
  }
}

/**
 * 关联参数到面板
 * @param {string} paramId 参数ID
 * @param {string} panelId 面板ID
 */
export function associateParamWithPanel(paramId, panelId) {
  const paramService = useParamService();
  paramService.associateParamWithPanel(paramId, panelId);
}

/**
 * 获取面板关联的所有参数
 * @param {string} panelId 面板ID
 * @returns {Array<string>} 参数ID数组
 */
export function getParamsByPanel(panelId) {
  const paramService = useParamService();
  return paramService.getParamsByPanel(panelId);
}

/**
 * 将参数标记为已删除，可以被重用
 * @param {string} paramId - 参数ID
 */
export function markParamAsDeleted(paramId) {
  try {
    // console.log('参数工具 - 标记参数已删除:', paramId);
    
    // 确保参数ID是字符串格式
    const paramIdStr = String(paramId);
    
    // 获取当前已删除的参数ID列表
    let deletedIds = [];
    try {
      const stored = localStorage.getItem('deletedParamIds');
      if (stored) {
        deletedIds = JSON.parse(stored);
      }
    } catch (e) {
      console.error('读取已删除参数ID失败:', e);
    }
    
    // 添加到已删除列表（如果不存在）
    if (!deletedIds.includes(paramIdStr)) {
      deletedIds.push(paramIdStr);
      localStorage.setItem('deletedParamIds', JSON.stringify(deletedIds));
      // console.log('已删除参数ID已保存到本地存储:', deletedIds);
    }
    
    // 如果存在全局参数服务实例，也标记为已删除
    if (window.GLOBAL_PARAM_SERVICE && typeof window.GLOBAL_PARAM_SERVICE.markAsDeleted === 'function') {
      // console.log('使用全局参数服务实例标记参数已删除');
      window.GLOBAL_PARAM_SERVICE.markAsDeleted(paramIdStr);
    }
    
    // 如果存在导入的参数服务，也标记为已删除
    if (typeof paramService !== 'undefined' && paramService && typeof paramService.markAsDeleted === 'function') {
      // console.log('使用导入的参数服务标记参数已删除');
      paramService.markAsDeleted(paramIdStr);
    }
    
    // 如果存在全局参数管理器，也标记为已删除
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.markParamAsDeleted === 'function') {
      // console.log('使用全局参数管理器标记参数已删除');
      window.GLOBAL_PARAM_MANAGER.markParamAsDeleted(paramIdStr);
    }
    
    return true;
  } catch (error) {
    console.error('标记参数为已删除失败:', error);
    return false;
  }
}

/**
 * 添加已使用的参数ID
 * @param {string} paramId 参数ID
 */
export function addUsedParamId(paramId) {
  const paramService = useParamService();
  paramService.addUsedParamId(paramId);
}

/**
 * 创建全局参数事件总线（如果尚未创建）
 * @returns {Object} 事件总线对象
 */
export function createOrGetParamEventBus() {
  if (!window.PARAM_EVENT_BUS) {
    window.PARAM_EVENT_BUS = {
      events: {},
      on(event, callback) {
        if (!this.events[event]) {
          this.events[event] = [];
        }
        this.events[event].push(callback);
      },
      off(event, callback) {
        if (!this.events[event]) return;
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      },
      emit(event, data) {
        if (!this.events[event]) return;
        this.events[event].forEach(callback => callback(data));
      }
    };
  }
  return window.PARAM_EVENT_BUS;
}

// 创建全局参数管理器（向后兼容）
export function createOrGetGlobalParamManager() {
  if (!window.GLOBAL_PARAM_MANAGER) {
    window.GLOBAL_PARAM_MANAGER = {
      usedParamIds: new Set(),
      panelParams: {},
      addParam(paramId, panelId) {
        this.usedParamIds.add(paramId);
        if (panelId) {
          if (!this.panelParams[panelId]) {
            this.panelParams[panelId] = new Set();
          }
          this.panelParams[panelId].add(paramId);
        }
      },
      removeParam(paramId, panelId) {
        this.usedParamIds.delete(paramId);
        if (panelId && this.panelParams[panelId]) {
          this.panelParams[panelId].delete(paramId);
        }
      },
      getParamsByPanel(panelId) {
        return Array.from(this.panelParams[panelId] || []);
      },
      getAllParams() {
        return Array.from(this.usedParamIds);
      }
    };
  }
  return window.GLOBAL_PARAM_MANAGER;
}

// 确保全局参数管理器和事件总线存在
createOrGetGlobalParamManager();
createOrGetParamEventBus();

/**
 * 创建参数按钮元素
 * @param {string} paramText 参数文本，如 {#param1#}
 * @returns {HTMLElement} 创建好的参数按钮元素
 */
export const createParamElement = (paramText) => {
  const paramElement = document.createElement('input');
  paramElement.type = 'button';
  paramElement.className = 'j-btn param-input';
  // 直接使用完整的参数文本作为按钮值
  paramElement.value = paramText;
  paramElement.readOnly = true;
  paramElement.setAttribute('unselectable', 'on');
  
  // 提取参数ID并设置data-param-id属性
  const match = paramText.match(/{#param(\d+)#}/);
  if (match && match[1]) {
    const paramId = match[1];
    paramElement.setAttribute('data-param-id', paramId);
  }
  
  return paramElement;
};

/**
 * 插入参数到可编辑区域
 * @param {HTMLElement} editorElement 编辑区域元素
 * @param {string} paramText 参数文本
 * @param {function} onInserted 插入完成后的回调函数
 */
export const insertParamToEditor = (editorElement, paramText, onInserted) => {
  if (!editorElement) {
    console.error('编辑区域元素不存在');
    return false;
  }

  try {
    // 创建参数元素
    const paramElement = createParamElement(paramText);
    
    // 确保编辑区域有焦点
    editorElement.focus();
    
    // 获取选区
    const selection = window.getSelection();
    let range;
    
    if (selection.rangeCount > 0) {
      range = selection.getRangeAt(0);
      // 确保选区在编辑区域内
      if (!editorElement.contains(range.commonAncestorContainer)) {
        // 如果选区不在编辑区域内，创建新的选区
        range = document.createRange();
        range.selectNodeContents(editorElement);
        range.collapse(false); // 移到结尾
        selection.removeAllRanges();
        selection.addRange(range);
      }
    } else {
      // 如果没有选区，创建新的选区
      range = document.createRange();
      range.selectNodeContents(editorElement);
      range.collapse(false); // 移到结尾
      selection.removeAllRanges();
      selection.addRange(range);
    }
    
    // 重新获取选区，确保有效
    range = selection.getRangeAt(0);
    
    // 保存当前选区位置
    const savedRange = {
      startContainer: range.startContainer,
      startOffset: range.startOffset,
      endContainer: range.endContainer,
      endOffset: range.endOffset,
      commonAncestorContainer: range.commonAncestorContainer
    };
    
    // 在当前选区位置插入参数元素
    range.insertNode(paramElement);
    
    // 在参数元素后插入零宽空格，以确保光标可以放在参数后面
    const zeroWidthSpace = document.createTextNode('\u200B');
    const spaceRange = document.createRange();
    spaceRange.setStartAfter(paramElement);
    spaceRange.setEndAfter(paramElement);
    spaceRange.insertNode(zeroWidthSpace);
    
    // 移动光标到零宽空格后面
    const newRange = document.createRange();
    newRange.setStartAfter(zeroWidthSpace);
    newRange.setEndAfter(zeroWidthSpace);
    selection.removeAllRanges();
    selection.addRange(newRange);
    
    // 调用回调函数
    if (typeof onInserted === 'function') {
      onInserted(editorElement, paramElement);
    }

    // 插入后强制收集参数，保证全局唯一
    collectParamsFromDOM();
    
    return true;
  } catch (error) {
    console.error('插入参数到编辑区域失败:', error);
    return false;
  }
};

/**
 * 插入参数到输入框
 * @param {HTMLInputElement} inputElement 输入框元素
 * @param {string} paramText 参数文本
 * @returns {boolean} 插入是否成功
 */
export const insertParamToInput = (inputElement, paramText) => {
  if (!inputElement || !paramText) {
    console.error('输入框元素或参数文本不存在');
    return false;
  }

  try {
    // 检查是否为 contenteditable 元素
    if (inputElement.getAttribute('contenteditable') === 'true') {
      return insertParamToContentEditable(inputElement, paramText);
    }

    // 确保输入框有焦点
    inputElement.focus();
    
    // 等待焦点生效
    setTimeout(() => {
      // 获取当前选中位置，如果没有则使用0
      const start = inputElement.selectionStart || 0;
      const end = inputElement.selectionEnd || 0;

      // 获取当前值
      const currentValue = inputElement.value || '';

      // 在光标位置插入参数
      const newValue = currentValue.substring(0, start) + paramText + currentValue.substring(end);
      
      // 更新输入框值
      inputElement.value = newValue;
      
      // 设置光标位置到参数后面
      const newPosition = start + paramText.length;
      inputElement.setSelectionRange(newPosition, newPosition);
      
      // 确保输入框获得焦点
      inputElement.focus();
      
      // 触发输入事件
      const event = new Event('input', { bubbles: true });
      inputElement.dispatchEvent(event);

      // 插入后强制收集参数，保证全局唯一
      collectParamsFromDOM();
    }, 10);
    
    return true;
  } catch (error) {
    console.error('插入参数到输入框失败:', error);
    return false;
  }
};

/**
 * 增强版的参数插入函数，解决光标位置问题，统一处理contenteditable元素中的参数插入
 * @param {Element} editableElement - 具有contenteditable属性的元素
 * @param {string|number} paramId - 参数ID
 * @param {Element} [sourceEditor] - 源编辑器元素，用于追踪参数所属
 * @param {Object} [options] - 额外选项
 * @param {boolean} [options.addZeroWidthSpace=true] - 是否添加零宽空格以修复光标问题
 * @param {boolean} [options.collectParams=true] - 是否在插入后收集参数
 * @param {Function} [options.onSuccess] - 成功回调函数
 * @returns {boolean} 操作是否成功
 */
export const enhancedInsertParamToContentEditable = (editableElement, paramId, sourceEditor, options = {}) => {
  if (!editableElement) {
    console.error('插入参数失败: 未提供可编辑元素');
    return false;
  }

  // 设置默认选项
  const defaultOptions = {
    addZeroWidthSpace: true,
    collectParams: true,
    onSuccess: null
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    console.log(`增强版插入参数 ${paramId} 到元素`, editableElement);
    
    // 从参数文本中提取ID
    let actualParamId = paramId;
    if (typeof paramId === 'string' && paramId.includes('param')) {
      const match = paramId.match(/{#param(\d+)#}/);
      if (match && match[1]) {
        actualParamId = match[1];
      }
    }
    
    if (!actualParamId) {
      console.error('无法插入参数：无效的参数ID');
      return false;
    }
    
    // 确保参数是标准格式
    const paramText = `{#param${actualParamId}#}`;
    
    // 初始化全局参数管理器
    const manager = initGlobalParamManager();
    
    // 创建参数按钮元素
    const button = document.createElement('input');
    button.type = 'button';
    button.className = 'j-btn param-input';
    button.value = paramText;
    button.setAttribute('data-param-id', actualParamId);
    button.readOnly = true;
    button.setAttribute('unselectable', 'on');
    
    // 确定所属面板
    let panelId = '';
    
    // 1. 尝试从源编辑器元素获取
    if (sourceEditor) {
      panelId = sourceEditor.getAttribute('data-owner-panel');
      if (!panelId) {
        const closestPanel = sourceEditor.closest('[data-panel-id], .setting-panel, .setting-group');
        if (closestPanel) {
          panelId = closestPanel.id || closestPanel.getAttribute('data-panel-id');
        }
      }
    }
    
    // 2. 如果没有面板ID，尝试从可编辑元素获取
    if (!panelId) {
      panelId = editableElement.getAttribute('data-owner-panel');
      if (!panelId) {
        const closestPanel = editableElement.closest('[data-panel-id], .setting-panel, .setting-group');
        if (closestPanel) {
          panelId = closestPanel.id || closestPanel.getAttribute('data-panel-id');
        }
      }
    }
    
    // 3. 如果仍然没有面板ID，使用全局默认值
    if (!panelId) {
      panelId = 'global-panel';
    }
    
    // 设置参数按钮的所属面板
    button.setAttribute('data-owner-panel', panelId);
    
    // 记录参数所属的元素ID
    const editableId = editableElement.id || `editable-${Date.now()}`;
    if (!editableElement.id) {
      editableElement.id = editableId;
    }
    
    // 设置可编辑元素的所属面板
    if (!editableElement.hasAttribute('data-owner-panel')) {
      editableElement.setAttribute('data-owner-panel', panelId);
    }
    
    // 设置参数按钮的所属元素属性
    button.setAttribute('data-owner-element', editableId);
    
    // 关联参数到面板
    if (manager) {
      if (typeof manager.addParamToPanel === 'function') {
        manager.addParamToPanel(actualParamId, panelId);
      } else if (typeof manager.associateParamWithPanel === 'function') {
        manager.associateParamWithPanel(actualParamId, panelId);
      }
    }
    
    // 添加点击事件处理删除
    button.addEventListener('click', (e) => {
      // Ctrl+点击 或 Command+点击 删除参数
      if (e.ctrlKey || e.metaKey) {
        const parentElement = button.parentElement;
        if (parentElement) {
          // 获取参数ID
          const paramIdToDelete = button.getAttribute('data-param-id');
          if (paramIdToDelete) {
            // 标记参数为已删除
            markParamAsDeleted(paramIdToDelete);
          }
          
          // 从DOM中移除按钮
          parentElement.removeChild(button);
          
          // 触发内容更改事件
          const changeEvent = new Event('input', { bubbles: true });
          parentElement.dispatchEvent(changeEvent);
        }
      }
    });
    
    // 获取当前选区
    const selection = window.getSelection();
    
    // 关键修复：如果没有选区，创建一个新选区
    if (!selection.rangeCount) {
      const range = document.createRange();
      range.selectNodeContents(editableElement);
      range.collapse(false); // 移动到末尾
      selection.removeAllRanges();
      selection.addRange(range);
    }
    
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      
      // 确保选区在可编辑元素内
      if (!editableElement.contains(range.commonAncestorContainer)) {
        // 如果选区不在目标元素内，重新创建选区
        const newRange = document.createRange();
        newRange.selectNodeContents(editableElement);
        newRange.collapse(false);
        selection.removeAllRanges();
        selection.addRange(newRange);
        range = selection.getRangeAt(0);
      }
      
      // 确保选区在可编辑元素内
      if (editableElement.contains(range.commonAncestorContainer)) {
        // 删除选中内容
        range.deleteContents();
        
        // 插入参数按钮
        range.insertNode(button);
        
        // 重要改进：如果需要添加零宽空格
        if (finalOptions.addZeroWidthSpace) {
          // 插入非阻塞空格，防止按钮后面无法放置光标
          const space = document.createTextNode('\u200B');
          
          // 将range设置到按钮后面
          range.setStartAfter(button);
          range.setEndAfter(button);
          
          // 插入零宽空格
          range.insertNode(space);
          
          // 将光标放在空格后面
          range.setStartAfter(space);
          range.setEndAfter(space);
        } else {
          // 不添加零宽空格，直接设置光标到按钮后面
          range.setStartAfter(button);
          range.setEndAfter(button);
        }
        
        // 应用选区
        selection.removeAllRanges();
        selection.addRange(range);
        
        // 触发内容变化事件
        setTimeout(() => {
          const inputEvent = new Event('input', { bubbles: true });
          editableElement.dispatchEvent(inputEvent);
          
          console.log(`参数 ${actualParamId} 已插入到面板 ${panelId}`);
          
          // 如果启用参数收集
          if (finalOptions.collectParams) {
            collectParamsFromDOM();
          }
          
          // 调用成功回调
          if (typeof finalOptions.onSuccess === 'function') {
            finalOptions.onSuccess(button, actualParamId, panelId);
          }
        }, 10);
        
        return true;
      }
    }
    
    // 如果执行到这里，说明没有合适的选区，添加到内容末尾
    editableElement.appendChild(button);
    
    // 如果需要添加零宽空格
    if (finalOptions.addZeroWidthSpace) {
      const space = document.createTextNode('\u200B');
      editableElement.appendChild(space);
      
      // 移动光标到空格后面
      const range = document.createRange();
      range.setStartAfter(space);
      range.setEndAfter(space);
      selection.removeAllRanges();
      selection.addRange(range);
    } else {
      // 不添加零宽空格，直接设置光标到按钮后面
      const range = document.createRange();
      range.setStartAfter(button);
      range.setEndAfter(button);
      selection.removeAllRanges();
      selection.addRange(range);
    }
    
    // 触发内容变化事件
    setTimeout(() => {
      const inputEvent = new Event('input', { bubbles: true });
      editableElement.dispatchEvent(inputEvent);
      
      console.log(`参数 ${actualParamId} 已追加到面板 ${panelId}`);
      
      // 如果启用参数收集
      if (finalOptions.collectParams) {
        collectParamsFromDOM();
      }
      
      // 调用成功回调
      if (typeof finalOptions.onSuccess === 'function') {
        finalOptions.onSuccess(button, actualParamId, panelId);
      }
    }, 10);
    
    return true;
  } catch (error) {
    console.error('增强版插入参数按钮时出错:', error);
    return false;
  }
};

/**
 * 将参数插入到contenteditable元素中 - 向后兼容版本
 * 该函数使用增强版实现，保持原有函数名称以保持兼容性
 * @param {Element} element - 要插入参数的contenteditable元素
 * @param {number|string} paramId - 参数ID
 * @param {Element} [sourceEditor] - 源编辑器元素，用于追踪参数所属
 * @returns {boolean} 操作是否成功
 */
export const insertParamToContentEditable = (element, paramId, sourceEditor) => {
  console.log(`准备插入参数 ${paramId} 到元素`, element);
  
  // 使用增强版函数
  return enhancedInsertParamToContentEditable(element, paramId, sourceEditor);
};

/**
 * 重写 insertParamToContentEditableV2 函数，使用增强版实现
 * @param {Element} editableElement - 具有contenteditable属性的元素
 * @param {string|number} paramId - 参数ID
 * @param {Element} [sourceEditor] - 源编辑器元素，用于追踪参数所属
 * @returns {boolean} 操作是否成功
 */
const insertParamToContentEditableV2 = (editableElement, paramId, sourceEditor) => {
  // 直接使用增强版函数
  return enhancedInsertParamToContentEditable(editableElement, paramId, sourceEditor);
};

/**
 * 格式化包含参数的文本内容，将参数转换为按钮元素
 * @param {string} content 包含参数的文本内容
 * @returns {string} 格式化后的HTML字符串
 */
export const formatParamContent = (content) => {
  if (!content) return '';
  
  // 如果内容中不包含参数，直接返回原内容
  if (!content.includes('{#param')) {
    return content;
  }
  
  // 使用正则表达式查找所有参数
  const paramRegex = /{#param(\d+)#}/g;
  
  // 替换所有参数为带样式的参数元素
  return content.replace(paramRegex, (match) => {
    return `<input type="button" class="j-btn param-input" value="${match}" readonly unselectable="on">`;
  });
};

/**
 * 清理包含HTML和参数元素的内容，提取纯文本和参数
 * @param {string} htmlContent 包含HTML和参数元素的内容
 * @returns {string} 提取后的纯文本和参数
 */
export const cleanTextContent = (htmlContent) => {
  if (!htmlContent) return '';
  
  // 创建临时容器解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlContent;
  
  // 提取文本和参数
  let cleanedContent = '';
  
  // 处理所有子节点
  for (const node of tempDiv.childNodes) {
    if (node.nodeType === Node.TEXT_NODE) {
      // 文本节点直接添加
      cleanedContent += node.textContent;
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      // 如果是参数按钮元素
      if ((node.tagName === 'INPUT' || node.tagName === 'SPAN') && 
          (node.classList.contains('j-btn') || node.classList.contains('param-input'))) {
        // 添加参数值
        cleanedContent += node.value || node.textContent;
      } else {
        // 其他元素递归提取文本
        cleanedContent += node.textContent;
      }
    }
  }
  
  return cleanedContent;
}; 

/**
 * 获取所有参数，包括隐藏面板中的参数
 * @returns {Array} 所有参数ID数组
 */
export const getAllParameters = () => {
  try {
    // 使用paramStore
    const paramStore = useParamService();
    
    // 从DOM收集参数
    paramStore.collectParamsFromDOM();
    
    // 获取所有已使用的参数ID
    const result = paramStore.allUsedParamIds;
    
    console.log('获取所有参数（包括隐藏）:', result);
    return result;
  } catch (error) {
    console.error('获取所有参数时出错:', error);
    return [];
  }
};

/**
 * 初始化全局参数管理器
 * @param {boolean} [reset=false] - 是否重置管理器（创建新实例）
 * @returns {Object} 全局参数管理器实例
 */
export const initGlobalParamManager = (reset = false) => {
  try {
    // 如果需要强制重置或管理器不存在，创建新的管理器
    if (reset || !window.GLOBAL_PARAM_MANAGER) {
      console.log('创建新的全局参数管理器实例' + (reset ? ' (强制重置)' : ''));
      
      // 保存当前已删除的参数ID（如果存在）
      let currentDeletedIds = [];
      if (window.GLOBAL_PARAM_MANAGER && window.GLOBAL_PARAM_MANAGER.deletedParamIds) {
        currentDeletedIds = Array.from(window.GLOBAL_PARAM_MANAGER.deletedParamIds);
      }
      
      // 创建新的全局参数管理器
      window.GLOBAL_PARAM_MANAGER = {
        // 参数ID集合 - 使用Set确保唯一性
        usedParamIds: new Set(),
        
        // 已删除的参数ID集合，用于重用
        deletedParamIds: new Set(),
        
        // 参数定义映射，保存参数的名称、类型等信息
        paramDefinitions: new Map(),
        
        // 面板与参数关联映射 - panel ID -> param IDs[]
        panelParams: {},
        
        // 参数与面板关联映射 - param ID -> panel ID
        paramPanelMap: new Map(),
        
        // 兼容旧版的参数组管理
        paramGroups: { 'global': new Set() },
        
        // 参数搜索范围 - 默认为整个文档，但可以限制在模板对话框内
        searchRoot: document.querySelector('.template-dialog-root') || document,
        
        // 将参数关联到面板
        associateParamWithPanel: function(paramId, panelId) {
          if (!paramId || !panelId) return;
          
          // 更新参数-面板映射
          this.paramPanelMap.set(paramId, panelId);
          
          // 更新面板-参数映射
          if (!this.panelParams[panelId]) {
            this.panelParams[panelId] = [];
          }
          
          // 如果参数尚未关联到面板，则添加
          if (!this.panelParams[panelId].includes(paramId)) {
            this.panelParams[panelId].push(paramId);
          }
        },
        
        // 添加参数到组 - 用于兼容旧版接口
        addParamToGroup: function(groupId, paramId) {
          // 添加到使用中参数集合
          this.usedParamIds.add(paramId);
          
          // 如果有指定组ID，则关联到面板
          if (groupId && groupId !== 'global') {
            this.associateParamWithPanel(paramId, groupId);
          }
          
          // 确保参数组存在
          if (!this.paramGroups[groupId]) {
            this.paramGroups[groupId] = new Set();
          }
          this.paramGroups[groupId].add(paramId);
          
          return paramId;
        },
        
        // 重置管理器 - 清空已使用参数但保留已删除参数
        reset: function() {
          this.usedParamIds.clear();
          this.paramDefinitions.clear();
          this.paramPanelMap.clear();
          this.panelParams = {};
          
          // 重置参数组，但保留global组
          this.paramGroups = { 'global': new Set() };
          
          // 已删除参数集合不清空，允许重用
          console.log('参数管理器已重置（保留已删除参数）');
        }
      };
      
      // 更新搜索范围
      const templateDialogRoot = document.querySelector('.template-dialog-root');
      if (templateDialogRoot) {
        console.log('限制参数管理器的搜索范围到模板编辑区域');
        window.GLOBAL_PARAM_MANAGER.searchRoot = templateDialogRoot;
      }
      
      // 恢复已删除的参数ID
      if (currentDeletedIds.length > 0) {
        currentDeletedIds.forEach(id => {
          window.GLOBAL_PARAM_MANAGER.deletedParamIds.add(id);
        });
        console.log(`已恢复${currentDeletedIds.length}个已删除的参数ID`);
      }
    }
    
    return window.GLOBAL_PARAM_MANAGER;
  } catch (error) {
    console.error('初始化全局参数管理器失败:', error);
    
    // 即使出错也尝试返回有效的管理器
    if (!window.GLOBAL_PARAM_MANAGER) {
      window.GLOBAL_PARAM_MANAGER = {
        usedParamIds: new Set(),
        deletedParamIds: new Set(),
        paramDefinitions: new Map(),
        panelParams: {},
        paramPanelMap: new Map(),
        paramGroups: { 'global': new Set() },
        searchRoot: document.querySelector('.template-dialog-root') || document,
        associateParamWithPanel: function() {},
        addParamToGroup: function() {},
        reset: function() {}
      };
    }
    
    return window.GLOBAL_PARAM_MANAGER;
  }
};

/**
 * 新增：从所有内容对象收集参数ID，保证全局唯一
 * @returns {void}
 */
export const collectParamsFromAllContents = () => {
  // 支持 window.selectedTemplateContents 或 window.TEMPLATE_CONTENTS
  const contents = window.selectedTemplateContents || window.TEMPLATE_CONTENTS;
  if (!contents || !Array.isArray(contents)) return;

  // 初始化全局参数管理器
  const manager = initGlobalParamManager();
  manager.usedParamIds.clear();
  manager.paramGroups['global'].clear();

  // 递归收集参数ID
  const regex = /{#param(\d+)#}/g;
  const foundIds = new Set();
  const findParams = (obj) => {
    if (!obj) return;
    if (typeof obj === 'string') {
      let match;
      while ((match = regex.exec(obj)) !== null) {
        foundIds.add(match[1]);
      }
    } else if (typeof obj === 'object') {
      Object.values(obj).forEach(val => findParams(val));
    }
  };
  contents.forEach(item => findParams(item));
  foundIds.forEach(id => {
    manager.usedParamIds.add(id);
    manager.paramGroups['global'].add(id);
  });
  // 日志
  console.log('[全内容收集] 当前全局已用参数ID:', Array.from(manager.usedParamIds));
};

/**
 * 重置参数管理器 - 但保留DOM中的参数
 */
export const resetParamManager = () => {
  try {
    const manager = initGlobalParamManager();
    
    if (manager && typeof manager.reset === 'function') {
      manager.reset();
    } else {
      
      // 尝试使用参数服务
      const paramService = useParamService();
      if (paramService && typeof paramService.resetStore === 'function') {
        paramService.resetStore();
      }
    }
  } catch (error) {
    console.error('重置参数管理器时出错:', error);
  }
};

/**
 * 强制完全重置参数管理器 - 清空所有参数
 */
export const forceResetParamManager = () => {
  try {
    if (window.GLOBAL_PARAM_MANAGER && typeof window.GLOBAL_PARAM_MANAGER.forceReset === 'function') {
      window.GLOBAL_PARAM_MANAGER.forceReset();
    } else {
      // 尝试重新初始化参数管理器
      const manager = initGlobalParamManager(true);
      
      // 尝试使用参数服务
      const paramService = useParamService();
      if (paramService && typeof paramService.resetStore === 'function') {
        paramService.resetStore();
      }
    }
  } catch (error) {
    console.error('强制重置参数管理器时出错:', error);
  }
};

/**
 * 全局初始化参数系统
 * 这个函数会创建或获取全局参数管理器，并且可以选择性地清除localStorage中的参数相关缓存
 * @param {boolean} [clearStorage=false] - 是否清除localStorage中的参数缓存
 * @param {boolean} [collectFromDOM=true] - 是否在初始化后从DOM收集参数
 * @returns {Object} 全局参数管理器实例
 */
export const globalInitParamSystem = (clearStorage = false, collectFromDOM = true) => {
  try {
    console.log(`全局初始化参数系统 (clearStorage=${clearStorage}, collectFromDOM=${collectFromDOM})`);
    
    // 清除全局变量
    if (window.GLOBAL_PARAM_MANAGER) {
      console.log('删除全局参数管理器');
      delete window.GLOBAL_PARAM_MANAGER;
    }
    
    if (window.GLOBAL_PARAM_NUMBERS) {
      console.log('删除全局参数编号集合');
      delete window.GLOBAL_PARAM_NUMBERS;
    }
    
    // 初始化Pinia Store
    const paramStore = useParamService();
    
    // 重置参数存储
    paramStore.resetStore();
    
    // 如果需要，则从DOM收集参数
    if (collectFromDOM) {
      console.log('从DOM收集所有可见参数...');
      paramStore.collectParamsFromDOM();
    }
    
    return { usedParamIds: paramStore.allUsedParamIds }; // 返回兼容格式的对象
  } catch (error) {
    console.error('全局初始化参数系统时出错:', error);
    return { usedParamIds: [] };
  }
};

/**
 * 检查是否存在特定格式参数
 * 检查指定根元素下是否有标准格式的参数 {#paramX#}
 * @param {HTMLElement} [searchRoot=document] 搜索的根元素，默认为 document
 * @returns {Array} 找到的参数ID数组
 */
export const checkForStandardFormatParams = (searchRoot = document) => {
  try {
    console.log('开始检查标准格式参数，搜索范围:', searchRoot === document ? '整个文档' : '模板编辑区域');
    
    // 收集页面上所有真实的{#paramX#}格式参数
    const allElements = searchRoot.querySelectorAll('*');
    const validParamIds = new Set();
    
    // 在所有元素中搜索标准格式参数
    allElements.forEach(element => {
      // 忽略脚本和样式元素
      if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE') {
        return;
      }
      
      // 检查元素属性
      if (element.getAttribute('data-param-id')) {
        const paramId = element.getAttribute('data-param-id');
        if (paramId) {
          validParamIds.add(paramId);
        }
      }
      
      // 检查元素的值或内容
      const contentToCheck = element.value || element.innerHTML || element.textContent || '';
      if (contentToCheck && typeof contentToCheck === 'string') {
        const regex = /{#param(\d+)#}/g;
        let match;
        while ((match = regex.exec(contentToCheck)) !== null) {
          if (match[1]) {
            validParamIds.add(match[1]);
          }
        }
      }
    });
    
    const paramIds = Array.from(validParamIds);
    console.log('在搜索范围内找到标准格式参数:', paramIds);
    return paramIds;
  } catch (error) {
    console.error('检查标准格式参数时出错:', error);
    return [];
  }
};

/**
 * 强制从所有组件收集参数信息
 * 这个函数会遍历所有可能包含参数的元素和组件，确保参数ID的全局唯一性
 */
export const forceGlobalParamCollectionFromAllComponents = () => {
  // 使用防抖防止短时间内多次调用
  const now = Date.now();
  if (window._lastParamCollection && (now - window._lastParamCollection < 500)) {
    // 如果500毫秒内已经执行过，直接返回，避免重复执行
    return;
  }
  
  window._lastParamCollection = now;
  console.log('强制从所有组件收集参数信息...');
  
  try {
    // 获取参数存储
    const paramStore = useParamService();
    if (!paramStore) {
      console.error('参数存储服务不可用');
      return [];
    }

    // 找到模板对话框的根元素，限制参数收集范围
    const templateDialogRoot = document.querySelector('.template-dialog-root');
    if (!templateDialogRoot) {
      console.log('未找到模板对话框根元素，使用整个文档作为搜索范围');
    } else {
      console.log('已找到模板对话框根元素，限制参数收集范围');
    }
    
    // 使用模板对话框根元素或整个文档作为搜索范围
    const searchRoot = templateDialogRoot || document;
    
    // 在收集前先清空所有可能的参数ID
    // 确保不会收集到虚假参数
    if (paramStore.usedParamIds && typeof paramStore.usedParamIds.clear === 'function') {
      paramStore.usedParamIds.clear();
    } else {
      console.warn('参数存储服务的usedParamIds不可用或不是Set类型');
      
      // 尝试创建新的参数ID集合
      if (paramStore) {
        paramStore.usedParamIds = new Set();
      }
    }
    
    // 清除可能隐藏的参数元素关联
    try {
      const hiddenParamElements = searchRoot.querySelectorAll('.setting-panel:not(.active) [data-param-id]'); // 使用 searchRoot
      if (hiddenParamElements.length > 0) {
        console.log(`发现${hiddenParamElements.length}个隐藏面板中的参数元素`);
      }
    } catch (e) {
      console.warn('检查隐藏面板参数元素时出错:', e);
    }
    
    // 完全扫描，确保收集所有标准格式参数
    const standardFormatParams = checkForStandardFormatParams(searchRoot); // 传递 searchRoot
    
    // 如果有标准格式参数，添加到参数存储
    if (standardFormatParams && standardFormatParams.length > 0 && paramStore) {
      standardFormatParams.forEach(paramId => {
        if (typeof paramStore.addUsedParamId === 'function') {
          paramStore.addUsedParamId(paramId);
        } else {
          // 如果addUsedParamId方法不可用，尝试直接添加到集合
          if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
            paramStore.usedParamIds.add(paramId);
          }
        }
      });
    }
    
    // 从DOM收集所有参数信息 - 使用深度查找模式
    const scanAllPanels = () => {
      // 查找所有面板元素
      const allPanels = searchRoot.querySelectorAll('.setting-panel, .setting-group'); // 使用 searchRoot
      console.log(`扫描${allPanels.length}个面板`);
      
      // 对每个面板执行参数收集
      allPanels.forEach(panel => {
        const panelId = panel.id || panel.getAttribute('data-panel-id') || 
                      'panel-' + Math.random().toString(36).substr(2, 9);
        
        // 查找面板内的参数元素
        const panelParams = panel.querySelectorAll('[data-param-id], .j-btn, .param-input');
        
        if (panelParams.length > 0) {
          console.log(`在面板 ${panelId} 中找到 ${panelParams.length} 个参数元素`);
          
          // 处理每个参数元素
          panelParams.forEach(element => {
            let paramId = element.getAttribute('data-param-id');
            
            if (!paramId) {
              // 尝试从值中提取
              const valueText = element.value || element.textContent || '';
              const match = valueText.match(/{#param(\d+)#}/);
              if (match && match[1]) {
                paramId = match[1];
                
                // 设置参数ID属性
                element.setAttribute('data-param-id', paramId);
              }
            }
            
            if (paramId && paramStore) {
              // 添加到参数存储
              if (typeof paramStore.addUsedParamId === 'function') {
                paramStore.addUsedParamId(paramId);
              } else if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
                paramStore.usedParamIds.add(paramId);
              }
              
              // 关联到面板
              if (typeof paramStore.associateParamWithPanel === 'function') {
                paramStore.associateParamWithPanel(paramId, panelId);
              }
              
              // 确保参数元素有面板关联
              if (!element.hasAttribute('data-owner-panel')) {
                element.setAttribute('data-owner-panel', panelId);
              }
            }
          });
        }
      });
    };
    
    // 执行全面板扫描
    scanAllPanels();
    
    // 然后执行标准的参数收集
    if (paramStore && typeof paramStore.collectParamsFromDOM === 'function') {
      paramStore.collectParamsFromDOM(searchRoot);
    }
    
    // 查找所有可编辑区域内的参数 - 只收集标准格式 {#paramX#}
    const editableElements = searchRoot.querySelectorAll('[contenteditable="true"]'); // 使用 searchRoot
    const editableParams = new Set();
    
    editableElements.forEach(element => {
      const content = element.innerHTML;
      // 只匹配标准格式 {#paramX#}，避免误识别纯文本"参数X"
      const regex = /{#param(\d+)#}/g;
      let match;
      
      while ((match = regex.exec(content)) !== null) {
        if (match[1]) {
          const paramId = match[1];
          editableParams.add(paramId);
          // 将参数添加到已使用集合
          if (paramStore) {
            if (typeof paramStore.addUsedParamId === 'function') {
              paramStore.addUsedParamId(paramId);
            } else if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
              paramStore.usedParamIds.add(paramId);
            }
            
            // 关联到面板（如果有）
            const panelId = element.getAttribute('data-owner-panel') || 
                           element.closest('[data-panel-id]')?.getAttribute('data-panel-id') ||
                           element.closest('.setting-panel, .setting-group')?.getAttribute('id');
            
            if (panelId && typeof paramStore.associateParamWithPanel === 'function') {
              paramStore.associateParamWithPanel(paramId, panelId);
            }
          }
        }
      }
    });
    
    // 查找点击事件配置中的参数 - 只收集标准格式 {#paramX#}
    if (window.CLICK_EVENT_CACHE) {
      console.log('window.CLICK_EVENT_CACHE:', window.CLICK_EVENT_CACHE);  // 添加日志确认内容
      // 假设点击事件关联的元素在 searchRoot 内，这里简单过滤
      Object.values(window.CLICK_EVENT_CACHE).forEach(data => {
        if (data && data.actionUrl && typeof data.actionUrl === 'string') {
          // 只匹配标准格式 {#paramX#}
          const regex = /{#param(\d+)#}/g;
          let match;
          
          while ((match = regex.exec(data.actionUrl)) !== null) {
            if (match[1] && paramStore) {
              // 检查参数是否在 searchRoot 内，这里简单假设在 actionUrl 中的参数有效
              // 实际情况可能需要更复杂的判断
              if (typeof paramStore.addUsedParamId === 'function') {
                paramStore.addUsedParamId(match[1]);
              } else if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
                paramStore.usedParamIds.add(match[1]);
              }
            }
          }
        }
      });
    }
    
    // 过滤掉可能是虚假参数的ID（纯文本"参数X"，而非{#paramX#}格式）
    // 确保已使用的参数都是通过正则{#paramX#}格式收集的
    let hasValidatedUsedIds = false;
    const validateParameterIds = (callback) => {
      // 使用防抖防止短时间内多次调用
      const now = Date.now();
      if (window._lastValidateIds && (now - window._lastValidateIds < 300)) {
        // 如果300毫秒内已经执行过，直接返回
        if (typeof callback === 'function') {
          callback(paramStore);
        }
        return;
      }
      
      window._lastValidateIds = now;
      
      if (hasValidatedUsedIds) {
        if (typeof callback === 'function') {
          callback(paramStore);
        }
        return;
      }
      
      try {
        const standardFormatParams = collectStandardFormatParams(searchRoot); // 传递 searchRoot
        const validParamIds = new Set(standardFormatParams);

        console.log('通过标准格式验证的参数IDs:', Array.from(validParamIds));
        
        // 所有从DOM中正确提取的参数都是有效的
        // 将所有标准格式参数都添加到usedParamIds集合中
        if (paramStore) {
          validParamIds.forEach(id => {
            // 移除排除参数1 - 4的逻辑
            if (typeof paramStore.addUsedParamId === 'function') {
              paramStore.addUsedParamId(id);
            } else if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
              paramStore.usedParamIds.add(id);
            }
          });
        }
        
        hasValidatedUsedIds = true;
        
        if (typeof callback === 'function') {
          callback(paramStore);
        }
      } catch (error) {
        console.error('验证参数ID时出错:', error);
        if (typeof callback === 'function') {
          callback(paramStore);
        }
      }
    };
    
    // 执行参数验证
    validateParameterIds((paramStore) => {
      if (standardFormatParams && paramStore) {
        standardFormatParams.forEach(paramId => {
          if (typeof paramStore.addUsedParamId === 'function') {
            paramStore.addUsedParamId(paramId);
          } else if (paramStore.usedParamIds && typeof paramStore.usedParamIds.add === 'function') {
            paramStore.usedParamIds.add(paramId);
          }
        });
      }
      
      let finalParamIds = [];
      if (paramStore && paramStore.usedParamIds) {
        finalParamIds = Array.from(paramStore.usedParamIds)
          .sort((a, b) => parseInt(a) - parseInt(b));
        
        console.log('从所有组件收集到参数:', finalParamIds);
      }
      
      return finalParamIds;
    });
  } catch (error) {
    console.error('强制收集参数信息时出错:', error);
    return [];
  }
};

/**
 * 初始化参数事件监听
 * 注册对全局事件总线的监听，处理参数相关事件
 */
export const setupParamEventListeners = () => {
  // 确保全局事件总线存在
  if (!window.PARAM_EVENT_BUS) {
    console.log('初始化参数事件总线');
    window.PARAM_EVENT_BUS = {
      listeners: {},
      // 添加事件监听
      on(event, callback) {
        if (!this.listeners[event]) {
          this.listeners[event] = [];
        }
        this.listeners[event].push(callback);
        return () => this.off(event, callback);
      },
      // 移除事件监听
      off(event, callback) {
        if (!this.listeners[event]) return;
        if (!callback) {
          delete this.listeners[event];
          return;
        }
        this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
      },
      // 触发事件
      emit(event, ...args) {
        if (!this.listeners[event]) return;
        this.listeners[event].forEach(callback => {
          try {
            callback(...args);
          } catch (error) {
            console.error(`事件监听器错误 ${event}:`, error);
          }
        });
      }
    };
  }
  
  // 监听组件重置事件
  window.PARAM_EVENT_BUS.on('component-reset', (data) => {
    console.log(`收到组件重置事件:`, data);
    
    if (data && data.component && data.panelId) {
      try {
        // 获取参数存储
        const paramStore = useParamService();
        if (!paramStore) return;
        
        // 清理与指定面板关联的参数
        const panelParams = new Set();
        
        // 查找与该面板关联的所有参数ID
        paramStore.paramPanelMap.forEach((panelId, paramId) => {
          if (panelId === data.panelId) {
            panelParams.add(paramId);
          }
        });
        
        if (panelParams.size > 0) {
          console.log(`找到${panelParams.size}个与面板 ${data.panelId} 关联的参数:`, 
            Array.from(panelParams).join(', '));
          
          // 不删除参数，只解除与面板的关联
          panelParams.forEach(paramId => {
            // 从面板关联映射中删除
            paramStore.paramPanelMap.delete(paramId);
          });
          
          console.log(`已解除参数与面板 ${data.panelId} 的关联`);
        }
      } catch (error) {
        console.error('处理组件重置事件时出错:', error);
      }
    }
  });
  
  // 在初始化时立即设置
  setupParamEventListeners.isInitialized = true;
  console.log('参数事件监听已初始化');
  
  return true;
};

// 导出以供使用
setupParamEventListeners.isInitialized = false;

/**
 * 从DOM中收集标准格式的参数ID
 * @param {HTMLElement} [searchRoot=document] 搜索的根元素，默认为 document
 * @returns {Array<string>} 收集到的参数ID数组
 */
const collectStandardFormatParams = (searchRoot = document) => {
  try {
    const validParamIds = new Set();
    
    // 1. 查找具有data-param-id属性的元素
    const elementsWithParamId = searchRoot.querySelectorAll('[data-param-id]');
    elementsWithParamId.forEach(element => {
      // 检查元素属性
      if (element.getAttribute('data-param-id')) {
        const paramId = element.getAttribute('data-param-id');
        validParamIds.add(paramId);
      }
    });
    
    // 2. 查找所有可编辑元素中的标准格式参数
    const editableElements = searchRoot.querySelectorAll('[contenteditable="true"]');
    editableElements.forEach(element => {
      if (!element || !element.innerHTML) return;
      
      const contentToCheck = element.innerHTML;
      // 匹配标准格式 {#paramX#}
      const regex = /{#param(\d+)#}/g;
      let match;
      while ((match = regex.exec(contentToCheck)) !== null) {
        if (match[1]) {
          validParamIds.add(match[1]);
        }
      }
    });
    
    // 3. 在INPUT和TEXTAREA元素中搜索
    const inputElements = searchRoot.querySelectorAll('input, textarea');
    inputElements.forEach(element => {
      if (!element || !element.value) return;
      
      const contentToCheck = element.value;
      if (contentToCheck.includes('{#param') && contentToCheck.includes('#}')) {
        // 匹配标准格式 {#paramX#}
        const regex = /{#param(\d+)#}/g;
        let match;
        while ((match = regex.exec(contentToCheck)) !== null) {
          if (match[1]) {
            validParamIds.add(match[1]);
          }
        }
      }
    });
    
    const paramIds = Array.from(validParamIds);
    console.log('在搜索范围内找到标准格式参数:', paramIds);
    return paramIds;
  } catch (error) {
    console.error('收集标准格式参数时出错:', error);
    return [];
  }
};
