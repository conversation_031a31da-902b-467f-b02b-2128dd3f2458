<template>
  <div class="setting-group">
    <div class="setting-item">
      <div class="group-title">OPPO背景图设置</div>
      <div class="setting-img oppo-bg-setting" @click="openOppoMediaDialog">
        <!-- 已选择的OPPO背景图 -->
        <img :src="oppoBackgroundSrc" alt="OPPO背景图" v-if="oppoBackgroundSrc">
        <!-- 未选择OPPO背景图时 -->
        <div class="no-image" v-else>
          <el-icon><Picture /></el-icon>
          <span>未选择OPPO背景图</span>
        </div>
      </div>
      <el-button @click="openOppoMediaDialog" class="image-upload-button" type="primary">
        从图库选择
      </el-button>
      <!-- <div class="setting-desc">OPPO手机显示的红包背景图片，建议选择红色背景图片</div> -->
    </div>
  </div>
  
  <!-- OPPO背景图素材库选择弹框 -->
  <MediaSelectorDialog 
    v-model="oppoDialogVisible"
    mediaType="image"
    @confirm="handleOppoMediaSelect"
  />
</template>

<script setup>
import { ref, computed, inject } from 'vue';
import { Picture } from '@element-plus/icons-vue';
import MediaSelectorDialog from '../MediaSelectorDialog.vue';
import { getMediaUrl } from '@/utils/mediaUtils';

const emit = defineEmits(['update:imageContent']);

// 从全局状态中注入变更标记
const hasChanges = inject('hasChanges', { value: false });

const props = defineProps({
  imageContent: {
    type: Object,
    required: true
  }
});

const oppoDialogVisible = ref(false);

// 新增：计算 OPPO 背景图地址时，增加空值检查
const oppoBackgroundSrc = computed(() => {
  if (!props.imageContent || !props.imageContent.aimOppoBackground) {
    return ''; // 或默认占位图路径
  }
  return getMediaUrl(props.imageContent.aimOppoBackground);
});

// 打开OPPO背景图选择弹框
const openOppoMediaDialog = () => {
  console.log('打开OPPO背景图选择弹框');
  console.log('当前imageContent:', props.imageContent);
  oppoDialogVisible.value = true;
};

// 处理OPPO背景图选择
const handleOppoMediaSelect = (media) => {
  console.log('选择了OPPO背景图:', media);
  if (media && media.mediaUrl) {
    // 构造相对路径: /aim_files/appKey/path
    const relativePath = `/aim_files/${media.appKey}/${media.path}`;
    
    // 保存OPPO背景图路径
    props.imageContent.aimOppoBackground = relativePath;
    console.log('设置了OPPO背景图路径:', props.imageContent.aimOppoBackground);
    
    // 强制触发更新
    emit('update:imageContent', {...props.imageContent});
    
    // 标记内容有更改，需要提示保存
    if (hasChanges) {
      hasChanges.value = true;
    }
    emit('update:image-content', {...props.imageContent}); // 修改事件名为连字符
    console.log('触发了 update:imageContent 事件:', {...props.imageContent}); // 新增日志
  }
};
</script>

<style scoped lang="scss">
.setting-group {
  .group-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    position: relative;
    background: #eee;
    padding: 14px;
    margin: 10px 0;
  }

  .setting-item {
    margin-bottom: 16px;
    padding: 0 14px;
    
    .setting-img {
      width: 100%;
      height: 210px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 12px 0;
      cursor: pointer;
      overflow: hidden;
      border-radius: 4px;
      transition: all 0.3s;
      
      &.oppo-bg-setting {
        border: 2px dashed #409eff;
        background-color: rgba(64, 159, 255, 0.1);
      }
      
      img {
        width: 100%;
      }
      
      .no-image {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #909399;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 8px;
        }
      }
    }

    .setting-desc {
      font-size: 14px;
      line-height: 22px;
      margin: 12px 0;
      color: #666;
    }
  }
}
</style> 