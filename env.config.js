const envConfig = {
    development: { //开发环境
        apiBaseUrl: "/api",
        // 开发环境可能会有一些特殊配置，如是否开启调试日志
        enableDebugLog: false,
    },
    test: { //测试环境
        apiBaseUrl: 'http://58.246.39.26:23451',
        enableDebugLog: false
    },
    production: { //生产环境
        apiBaseUrl: 'http:10.181.113.67:8096',
        enableDebugLog: false
    }
};

// 获取当前环境，这里假设使用 Vite 构建工具，它会将环境变量 MODE 注入到 import.meta.env 中
const currentEnv = import.meta.env.MODE;

// 根据当前环境导出对应的配置
export default envConfig[currentEnv];