<template>
  <div class="setting-group">
    <!-- 轮播图设置 - 仅在轮播图模板中显示 -->
    <CarouselSettings 
      v-if="isCarouselTemplate"
      ref="carouselSettingsRef"
      :content="imageContent"
      :app-key="appKey"
      @update:content="handleImageContentUpdate"
      @insert-param="handleParamInsert"
      @manage-param="handleParamManage"
    />
    
    <!-- 普通图片设置 - 在非轮播图模板中显示 -->
    <div v-else class="setting-item">
      <div class="setting-img" @click="openMediaDialog">
        <!-- 视频类型的默认图片显示 -->
        <img v-if="imageContent.type === 'video' && (!mediaSrc || mediaSrc === getMediaUrl(imageContent.defaultSrc))" 
             :src="mediaSrc || getMediaUrl(imageContent.defaultSrc)" 
             alt="默认视频缩略图">
        <!-- 已选择的图片 -->
        <img :src="mediaSrc" alt="内容图片" v-else-if="mediaSrc && imageContent.type !== 'video'">
        <!-- 已选择的视频 -->
        <video 
          v-else-if="mediaSrc && imageContent.type === 'video' && mediaSrc !== getMediaUrl(imageContent.defaultSrc)" 
          :src="mediaSrc" 
          controls
          controlsList="nodownload"
          class="video-display"
          preload="metadata"
          @loadedmetadata="videoLoaded"
          ref="videoPlayer"
        ></video>
        <!-- 未选择任何媒体时 -->
        <div class="no-image" v-else>
          <el-icon><Picture /></el-icon>
          <span>未选择{{ imageContent.type === 'video' ? '视频' : '图片' }}</span>
        </div>
      </div>
      <el-button type="primary" @click="openMediaDialog" class="image-upload-button">
        {{ buttonText }}
      </el-button>
      <div class="setting-desc">{{ imageContent.content }}</div>
      
      <!-- 视频封面设置 - 仅在视频类型时显示 -->
      <div v-if="imageContent.type === 'video'" class="video-cover-setting">
        <div class="cover-setting-header" @click="toggleCoverSetting">
          <span class="setting-label">封面设置</span>
          <el-icon class="expand-icon" :class="{ 'expanded': isCoverSettingExpanded }">
            <ArrowDown />
          </el-icon>
        </div>
        
        <!-- 折叠内容区域 -->
        <div class="cover-setting-content" :class="{ 'expanded': isCoverSettingExpanded }">
          <div class="setting-img cover-img" @click="openCoverDialog">
            <!-- 已选择的封面图片 -->
            <img v-if="coverSrc" :src="coverSrc" alt="视频封面">
            <!-- 未选择封面时显示默认图片 -->
            <img v-else :src="defaultCoverSrc" alt="默认封面">
          </div>
          <div class="setting-desc">{{ imageContent.aimCover ? "该视频位建议选择比例为16：9（像素最小1088:612）的图片，大小建议500KB以内" : '' }}</div>
          <el-button @click="openCoverDialog" class="cover-upload-button" type="primary">
            选择视频封面
          </el-button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 素材库选择弹框 -->
  <MediaSelectorDialog 
    v-model="dialogVisible"
    :mediaType="props.imageContent.type"
    :filter-app-key="appKey"
    @confirm="handleMediaSelect"
  />
  
  <!-- 封面选择弹框 -->
  <MediaSelectorDialog 
    v-model="coverDialogVisible"
    :mediaType="'image'"
    :filter-app-key="appKey"
    @confirm="handleCoverSelect"
  />
</template>

<script setup>
import { getMediaUrl } from '@/utils/mediaUtils';
import templateFactory from '@/factories/TemplateFactory.js';
import { ArrowDown, Picture } from '@element-plus/icons-vue';

const emit = defineEmits(['update:imageContent', 'insert-param', 'manage-param']);

// 从全局状态中注入版式切换对话框状态
// const switchTemplateDialogVisible = inject('switchTemplateDialogVisible', { value: false });
const hasChanges = inject('hasChanges', { value: false });

const props = defineProps({
  imageContent: {
    type: Object,
    required: true
  },
  template: {
    type: Object,
    default: null
  },
  appKey: {
    type: String,
    default: ''
  }
});

// 在调试模式下输出appKey，方便确认传参
console.log('ImageSettings组件收到的appKey:', props.appKey);

const dialogVisible = ref(false);
const coverDialogVisible = ref(false);
const videoPlayer = ref(null);
const carouselSettingsRef = ref(null); // 轮播图设置组件引用
const currentMediaType = ref('image'); // 当前选择的媒体类型
const isCoverSettingExpanded = ref(true); // 封面设置展开状态

// 用本地变量接收 props.imageContent，避免直接修改 props
const localImageContent = reactive({ ...props.imageContent });

// 监听 props.imageContent 变化，实时同步到本地变量
watch(() => props.imageContent, (newVal) => {
  Object.assign(localImageContent, newVal);
}, { deep: true });

// 判断是否是轮播图模板
const isCarouselTemplate = computed(() => {
  if (!props.template) return false;
  
  // 支持两种传参方式：template.value 或直接 template
  const templateData = props.template.value || props.template;
  const result = templateFactory.isCarouselTemplate(templateData);
  
  return result;
});

// 处理图片内容更新
const handleImageContentUpdate = (updatedContent) => {
  emit('update:imageContent', updatedContent);
};

// 处理参数插入
const handleParamInsert = (paramInfo) => {
  emit('insert-param', paramInfo);
};

// 处理参数管理
const handleParamManage = () => {
  emit('manage-param');
};

// 根据content类型决定按钮文字
const buttonText = computed(() => {
  return localImageContent.type === 'video' ? '选择视频' : '选择图片';
});

// 获取媒体源地址（添加服务器前缀）
const mediaSrc = computed(() => {
  if (!localImageContent.src) return '';
  return getMediaUrl(localImageContent.src);
});

// 获取封面源地址
const coverSrc = computed(() => {
  // 如果没有选择封面，返回空
  if (!localImageContent.aimCover || 
      localImageContent.aimCover === '/aim_files/aim_defult/defaultVideo.jpg' ||
      localImageContent.aimCover.includes('视频位建议') || 
      localImageContent.aimCover.includes('该图片位建议')) {
    return '';
  }
  return getMediaUrl(localImageContent.aimCover);
});

// 获取默认封面图片
const defaultCoverSrc = computed(() => {
  return getMediaUrl('/aim_files/aim_defult/defaultVideo.jpg');
});

// 打开媒体选择弹框
const openMediaDialog = () => {
  // 明确打印当前使用的appKey
  console.log('打开媒体选择弹框，当前使用的appKey:', props.appKey);
  
  // 设置当前媒体类型
  currentMediaType.value = localImageContent.type;
  
  // 确保MediaSelectorDialog组件已经重置状态
  dialogVisible.value = false;
  nextTick(() => {
    dialogVisible.value = true;
  });
};

// 打开封面选择弹框
const openCoverDialog = () => {
  console.log('打开封面选择弹框，当前使用的appKey:', props.appKey);
  
  // 确保MediaSelectorDialog组件已经重置状态
  coverDialogVisible.value = false;
  nextTick(() => {
    coverDialogVisible.value = true;
  });
};

// 处理媒体选择
const handleMediaSelect = (media) => {
  console.log('选择了媒体:', media);
  
  if (media && (media.mediaUrl || media.path)) {
    const sourceAppKey = media.appKey || props.appKey;
    
    if (!sourceAppKey) {
      console.error('错误：无法获取有效的appKey！');
      ElMessage.error('无法获取素材的有效appKey，请重试');
      return;
    }
    
    const relativePath = `/aim_files/${sourceAppKey}/${media.path}`;
    
    // 保存相对路径到内容对象
    localImageContent.src = relativePath;
    console.log('设置了视频路径:', localImageContent.src);

    localImageContent.userEdited = true;
    localImageContent.sourceAppKey = sourceAppKey;
    
    emit('update:imageContent', {...localImageContent});
    
    if (hasChanges) {
      hasChanges.value = true;
    }
    
    setTimeout(() => {
      const event = new Event('mediaLoaded');
      window.dispatchEvent(event);
    }, 100);
  } else {
    console.error('媒体对象无效或缺少必要字段:', media);
    ElMessage.error('选择的媒体文件无效，请重试');
  }
};

// 处理封面选择
const handleCoverSelect = (media) => {
  console.log('选择了封面:', media);
  
  if (media && (media.mediaUrl || media.path)) {
    // 严格控制appKey优先级：媒体自身的appKey > 当前组件传入的appKey
    const sourceAppKey = media.appKey || props.appKey;
    
    if (!sourceAppKey) {
      console.error('错误：无法获取有效的appKey！');
      ElMessage.error('无法获取素材的有效appKey，请重试');
      return;
    }
    
    // 构造相对路径并更新封面
    const relativePath = `/aim_files/${sourceAppKey}/${media.path}`;
    
    // 保存封面路径到内容对象
    localImageContent.aimCover = relativePath;
    console.log('设置了封面路径:', localImageContent.aimCover);

    // 标记为已修改
    localImageContent.userEdited = true;
    
    // 强制触发更新
    emit('update:imageContent', {...localImageContent});
    
    // 标记内容有更改，需要提示保存
    if (hasChanges) {
      hasChanges.value = true;
    }
    
    ElMessage.success('封面设置成功');
  } else {
    console.error('封面对象无效或缺少必要字段:', media);
    ElMessage.error('选择的封面文件无效，请重试');
  }
};

// 视频加载完成后的处理函数
const videoLoaded = () => {
  console.log('视频加载完成');
  // 确保视频可见
  if (videoPlayer.value) {
    // 可以在这里设置播放器的初始状态
    videoPlayer.value.volume = 0.5;
  }
};

// 校验图片内容
const validateImageContent = () => {
  if (!localImageContent) {
    ElMessage.error('内容对象为空');
    return false;
  }
  if (localImageContent.type === 'video') {
    if (!localImageContent.src) {
      ElMessage.error('请上传视频');
      return false;
    }
    // 校验封面
    if (
      !localImageContent.aimCover ||
      localImageContent.aimCover === '/aim_files/aim_defult/defaultVideo.jpg' ||
      localImageContent.aimCover.includes('视频位建议') ||
      localImageContent.aimCover.includes('该图片位建议')
    ) {
      ElMessage.error('请上传有效的视频封面图片');
      return false;
    }
    return true;
  }
  // 普通图片校验
  if (!localImageContent.src) {
    ElMessage.error('请上传图片');
    return false;
  }
  return true;
};

// 处理轮播图校验事件
const handleCarouselValidation = (event) => {
  console.log('ImageSettings收到轮播图校验事件:', event.detail);
  
  let isValid = true;
  
  try {
    // 调用轮播图的校验方法
    if (isCarouselTemplate.value && carouselSettingsRef.value) {
      isValid = carouselSettingsRef.value.validateCarousel();
    } else {
      // 如果没有轮播图组件，就进行基本校验
      isValid = validateImageContent();
    }
  } catch (error) {
    console.error('校验过程出错:', error);
    isValid = false;
  }
  
  // 发送校验结果
  const resultEvent = new CustomEvent('carousel-validation-result', {
    detail: { isValid }
  });
  document.dispatchEvent(resultEvent);
};

// 生命周期钩子
onMounted(() => {
  // 监听轮播图校验事件
  document.addEventListener('validate-carousel', handleCarouselValidation);
});

onBeforeUnmount(() => {
  // 清理事件监听器
  document.removeEventListener('validate-carousel', handleCarouselValidation);
});

// 暴露校验函数给父组件
defineExpose({
  validateImageContent
});

// 切换封面设置展开/收缩
const toggleCoverSetting = () => {
  isCoverSettingExpanded.value = !isCoverSettingExpanded.value;
};

// 监听localImageContent.type的变化，初始化默认封面
watch(() => localImageContent.type, (newType, oldType) => {
  if (newType === 'video' && (!localImageContent.aimCover || localImageContent.aimCover === '')) {
    localImageContent.aimCover = '/aim_files/aim_defult/defaultVideo.jpg';
  }
}, { immediate: true });

// 重置内容到默认状态
const resetToDefault = () => {
  if (localImageContent.type === 'video') {
    localImageContent.src = '/aim_files/aim_defult/defaultVideo.jpg';
    localImageContent.aimCover = '/aim_files/aim_defult/defaultVideo.jpg';
    localImageContent.userEdited = false;
    emit('update:imageContent', {...localImageContent});
  }
};

// 监听版式切换 - 只在真正的模板切换时重置，避免校验失败时误重置
watch(() => props.template, (newTemplate, oldTemplate) => {
  // 只有在模板ID或cardId真正发生变化时才重置
  if (newTemplate && oldTemplate &&
      (newTemplate.templateId !== oldTemplate.templateId ||
       newTemplate.cardId !== oldTemplate.cardId)) {
    resetToDefault();
  }
}, { deep: true });
</script>

<style scoped lang="scss">
.setting-group {
  .group-title {
    font-size: 13px;
    font-weight: bold;
    color: #333;
    position: relative;
    background: #eee;
    padding: 14px;
    margin: 10px 0;
  }

  .setting-item {
    margin-bottom: 16px;
    padding: 0 14px;
    .setting-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .setting-img {
      width: 100%;
      height: 210px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 12px 0;
      cursor: pointer;
      overflow: hidden;
      // border-radius: 4px;
      transition: all 0.3s;
    
      
      img {
        width: 100%;
        height: 100%;
        // object-fit: cover;
      }
      
      .video-display {
        width: 100%;
        height: 100%;
        max-height: 100%;
        object-fit: contain;
        background-color: #000;
      }
      
      .no-image {
        display: flex;
        flex-direction: column;
        align-items: center;
        color: #909399;
        
        .el-icon {
          font-size: 48px;
          margin-bottom: 8px;
        }
      }
    }

    .setting-desc {
      font-size: 14px;
      line-height: 22px;
      margin: 12px 0;
      color: #666;
    }
    
    // 视频封面设置样式
    .video-cover-setting {
      margin-top: 24px;
      position: relative;
      left: -4%;
      right: 0;
      width: 107.7%;
      
      .cover-setting-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        cursor: pointer;
        background-color: #eee;
        
        .setting-label {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
        
        .expand-icon {
          font-size: 16px;
          color: #909399;
          transition: transform 0.3s;
          
          &.expanded {
            transform: rotate(180deg);
          }
        }
      }
      
      .cover-setting-content {
        padding: 16px;
        border-top: 1px solid #f0f0f0;
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        transition: all 0.3s ease;
        
        &.expanded {
          max-height: 300px;
          opacity: 1;
          padding-top: 6px;
        }
        
        .cover-img {
          // height: 120px;
          margin-bottom: 16px;
  
          
          img {
            width: 100%;
            height: 100%;
            // object-fit: cover;
          }
        }
      }
    }
  }
  
  .image-upload-button, .cover-upload-button{
    font-size: 13px;
    padding: 0 24px;
  }
}
</style> 